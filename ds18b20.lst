
ds18b20.elf:     file format elf32-littleriscv
ds18b20.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x000035d8 memsz 0x000035d8 flags r-x
    LOAD off    0x00005000 vaddr 0x20000000 paddr 0x000035d8 align 2**12
         filesz 0x000000c0 memsz 0x0000012c flags rw-
    LOAD off    0x00005800 vaddr 0x20007800 paddr 0x20007800 align 2**12
         filesz 0x00000000 memsz 0x00000800 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         00000004  00000000  00000000  00001000  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .vector       000001bc  00000004  00000004  00001004  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .text         00003418  000001c0  000001c0  000011c0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  3 .fini         00000000  000035d8  000035d8  000050c0  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  4 .dalign       00000000  20000000  20000000  000050c0  2**0
                  CONTENTS
  5 .dlalign      00000000  000035d8  000035d8  000050c0  2**0
                  CONTENTS
  6 .data         000000c0  20000000  000035d8  00005000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .bss          0000006c  200000c0  00003698  000050c0  2**2
                  ALLOC
  8 .stack        00000800  20007800  20007800  00005800  2**0
                  ALLOC
  9 .debug_info   00016321  00000000  00000000  000050c0  2**0
                  CONTENTS, READONLY, DEBUGGING
 10 .debug_abbrev 00003abe  00000000  00000000  0001b3e1  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_aranges 00000b30  00000000  00000000  0001eea0  2**3
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_ranges 00000b70  00000000  00000000  0001f9d0  2**3
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_line   0000e0d9  00000000  00000000  00020540  2**0
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_str    00003564  00000000  00000000  0002e619  2**0
                  CONTENTS, READONLY, DEBUGGING
 15 .comment      00000033  00000000  00000000  00031b7d  2**0
                  CONTENTS, READONLY
 16 .debug_frame  000024cc  00000000  00000000  00031bb0  2**2
                  CONTENTS, READONLY, DEBUGGING
 17 .debug_loc    000058fc  00000000  00000000  0003407c  2**0
                  CONTENTS, READONLY, DEBUGGING
 18 .stab         00000084  00000000  00000000  00039978  2**2
                  CONTENTS, READONLY, DEBUGGING
 19 .stabstr      00000117  00000000  00000000  000399fc  2**0
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
00000004 l    d  .vector	00000000 .vector
000001c0 l    d  .text	00000000 .text
000035d8 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
000035d8 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
200000c0 l    d  .bss	00000000 .bss
20007800 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .stab	00000000 .stab
00000000 l    d  .stabstr	00000000 .stabstr
00000000 l    df *ABS*	00000000 ./Startup/startup_ch32v30x_D8C.o
00000004 l       .vector	00000000 _vector_base
00000000 l    df *ABS*	00000000 ch32v30x_it.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 system_ch32v30x.c
00000000 l    df *ABS*	00000000 ch32v30x_gpio.c
00000000 l    df *ABS*	00000000 ch32v30x_misc.c
00000000 l    df *ABS*	00000000 ch32v30x_rcc.c
20000034 l     O .data	00000010 APBAHBPrescTable
200000ac l     O .data	00000004 ADCPrescTable
00000000 l    df *ABS*	00000000 ch32v30x_spi.c
00000000 l    df *ABS*	00000000 ch32v30x_tim.c
00000000 l    df *ABS*	00000000 ch32v30x_usart.c
00000000 l    df *ABS*	00000000 SysTickDelay.c
200000d0 l     O .bss	00000002 fac_ms
200000d2 l     O .bss	00000001 fac_us
00000000 l    df *ABS*	00000000 brewing_control.c
00000000 l    df *ABS*	00000000 ds18b20.c
00000000 l    df *ABS*	00000000 heater.c
00000000 l    df *ABS*	00000000 key.c
00000000 l    df *ABS*	00000000 lcd.c
00000000 l    df *ABS*	00000000 timer.c
00000000 l    df *ABS*	00000000 water_level.c
00000000 l    df *ABS*	00000000 water_pump.c
00000000 l    df *ABS*	00000000 debug.c
200000d4 l     O .bss	00000002 p_ms
200000d6 l     O .bss	00000001 p_us
200000b0 l     O .data	00000004 curbrk.5274
00000000 l    df *ABS*	00000000 lesf2.c
00000000 l    df *ABS*	00000000 mulsf3.c
00000000 l    df *ABS*	00000000 floatsisf.c
00000000 l    df *ABS*	00000000 extendsfdf2.c
00000000 l    df *ABS*	00000000 libgcc2.c
00000000 l    df *ABS*	00000000 printf.c
00000000 l    df *ABS*	00000000 puts.c
00000000 l    df *ABS*	00000000 wbuf.c
00000000 l    df *ABS*	00000000 wsetup.c
00000000 l    df *ABS*	00000000 fflush.c
00000000 l    df *ABS*	00000000 findfp.c
00002542 l     F .text	00000066 std
00000000 l    df *ABS*	00000000 fwalk.c
00000000 l    df *ABS*	00000000 makebuf.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 nano-vfprintf.c
000029d4 l     F .text	00000028 __sfputc_r
00000000 l    df *ABS*	00000000 nano-vfprintf_i.c
00000000 l    df *ABS*	00000000 sbrkr.c
00000000 l    df *ABS*	00000000 stdio.c
00000000 l    df *ABS*	00000000 writer.c
00000000 l    df *ABS*	00000000 closer.c
00000000 l    df *ABS*	00000000 fstatr.c
00000000 l    df *ABS*	00000000 isattyr.c
00000000 l    df *ABS*	00000000 lseekr.c
00000000 l    df *ABS*	00000000 memchr.c
00000000 l    df *ABS*	00000000 mlock.c
00000000 l    df *ABS*	00000000 readr.c
00000000 l    df *ABS*	00000000 close.c
00000000 l    df *ABS*	00000000 fstat.c
00000000 l    df *ABS*	00000000 isatty.c
00000000 l    df *ABS*	00000000 lseek.c
00000000 l    df *ABS*	00000000 read.c
00000000 l    df *ABS*	00000000 libgcc2.c
00000000 l    df *ABS*	00000000 impure.c
20000044 l     O .data	00000060 impure_data
00000000 l    df *ABS*	00000000 reent.c
00000668  w      .text	00000000 EXTI2_IRQHandler
00000668  w      .text	00000000 TIM8_TRG_COM_IRQHandler
00000668  w      .text	00000000 TIM8_CC_IRQHandler
000031c8 g     F .text	00000028 _isatty_r
00002114 g     F .text	000000d4 _puts_r
000031f0 g     F .text	0000002c _lseek_r
00000d44 g     F .text	000000b4 BrewingControl_KeyHandler
00000668  w      .text	00000000 UART8_IRQHandler
0000103a g     F .text	00000024 DS18B20_ReadByte
000020d4 g     F .text	00000040 printf
200008a8 g       .data	00000000 __global_pointer$
000001c8 g     F .text	00000028 .hidden __riscv_save_8
00000668  w      .text	00000000 TIM1_CC_IRQHandler
00003112 g     F .text	00000030 __sseek
000025f8 g     F .text	0000006c __sinit
00000a24 g     F .text	00000004 SPI_I2S_SendData
000021f4 g     F .text	000000bc __swbuf_r
000002ca g     F .text	00000010 HardFault_Handler
200000c4 g     O .bss	00000001 p
000025b2 g     F .text	00000046 __sfmoreglue
00003236 g     F .text	00000002 __malloc_unlock
00001174 g     F .text	00000092 key_init
000002dc g     F .text	0000003a key_proc
20000000 g     O .data	00000024 scheduler_task_t
00000214 g     F .text	0000000c .hidden __riscv_restore_3
00000668  w      .text	00000000 TIM6_IRQHandler
00000ba6 g     F .text	0000000e TIM_OC1PreloadConfig
00000668  w      .text	00000000 SysTick_Handler
000007cc g     F .text	0000004e NVIC_Init
00000668  w      .text	00000000 PVD_IRQHandler
00000668  w      .text	00000000 SDIO_IRQHandler
00001906 g     F .text	0000005e stir_360
00000668  w      .text	00000000 TIM9_BRK_IRQHandler
00000200 g     F .text	00000020 .hidden __riscv_restore_10
00000a28 g     F .text	00000004 SPI_I2S_ReceiveData
00000668  w      .text	00000000 DMA2_Channel8_IRQHandler
000002c8 g     F .text	00000002 NMI_Handler
00000668  w      .text	00000000 CAN2_RX1_IRQHandler
00000668  w      .text	00000000 EXTI3_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_11
000014e6 g     F .text	00000062 spi_readwrite
00000668  w      .text	00000000 USBHS_IRQHandler
00000c88 g     F .text	0000000a USART_GetFlagStatus
00000668  w      .text	00000000 DMA2_Channel9_IRQHandler
0000319e g     F .text	0000002a _fstat_r
00000668  w      .text	00000000 TIM10_CC_IRQHandler
20000128 g     O .bss	00000004 errno
200000c0 g       .bss	00000000 _sbss
00000800 g       *ABS*	00000000 __stack_size
000015b0 g     F .text	00000030 LCD_WR_REG
00001b1a g     F .text	0000005a USART_Printf_Init
00000ecc g     F .text	00000026 DS18B20_IO_OUT
00000668  w      .text	00000000 USBFS_IRQHandler
00000214 g     F .text	0000000c .hidden __riscv_restore_2
00001964 g     F .text	00000020 stir
000019e0 g     F .text	00000082 WaterPump_Init
00000f16 g     F .text	00000030 DS18B20_Reset
000025a8 g     F .text	0000000a _cleanup_r
00000d22 g     F .text	00000022 BrewingControl_Start
00000668  w      .text	00000000 EXTI0_IRQHandler
00000668  w      .text	00000000 I2C2_EV_IRQHandler
00000668  w      .text	00000000 TIM10_TRG_COM_IRQHandler
00000b62 g     F .text	00000018 TIM_Cmd
000021e8 g     F .text	0000000c puts
200000a8 g     O .data	00000004 SystemCoreClock
00003270 g     F .text	0000000c _fstat
00000004 g       .init	00000000 _einit
00000bd0 g     F .text	0000000c TIM_ClearITPendingBit
00001bd8 g     F .text	0000008c .hidden __lesf2
00000990 g     F .text	0000001e RCC_APB2PeriphClockCmd
000001c0 g     F .text	00000030 .hidden __riscv_save_12
00000668  w      .text	00000000 CAN2_SCE_IRQHandler
00000668  w      .text	00000000 ADC1_2_IRQHandler
0000187c g     F .text	0000008a TIM2_PWM_Init
000006f4 g     F .text	000000c0 GPIO_Init
00000668  w      .text	00000000 Break_Point_Handler
00000200 g     F .text	00000020 .hidden __riscv_restore_11
200000cc g     O .bss	00000004 NVIC_Priority_Group
00000668  w      .text	00000000 SPI1_IRQHandler
00000c6a g     F .text	00000016 USART_Cmd
00003074 g     F .text	0000002a _sbrk_r
000001f0 g     F .text	0000000c .hidden __riscv_save_1
00000668  w      .text	00000000 TAMPER_IRQHandler
00000214 g     F .text	0000000c .hidden __riscv_restore_0
00003238 g     F .text	0000002c _read_r
000001d6 g     F .text	0000001a .hidden __riscv_save_7
00000668  w      .text	00000000 CAN2_RX0_IRQHandler
00001fba g     F .text	000000ac .hidden __extendsfdf2
00000668  w      .text	00000000 TIM8_UP_IRQHandler
000009ae g     F .text	0000001e RCC_APB1PeriphClockCmd
00000668  w      .text	00000000 Ecall_M_Mode_Handler
20007800 g       .stack	00000000 _heap_end
0000327c g     F .text	0000000c _isatty
200000b8 g     O .data	00000004 _global_impure_ptr
0000020a g     F .text	00000016 .hidden __riscv_restore_5
00000ef2 g     F .text	00000024 DS18B20_IO_IN
00000a2c g     F .text	0000000a SPI_I2S_GetFlagStatus
00000668  w      .text	00000000 DMA2_Channel2_IRQHandler
00000668  w      .text	00000000 DMA1_Channel4_IRQHandler
00001bb2 g     F .text	00000026 _sbrk
200000e0 g     O .bss	00000010 ds18b20
00000668  w      .text	00000000 TIM9_UP_IRQHandler
0000020a g     F .text	00000016 .hidden __riscv_restore_6
00000668  w      .text	00000000 USART3_IRQHandler
200000c3 g     O .bss	00000001 key_val
00000668  w      .text	00000000 RTC_IRQHandler
2000012c g       .bss	00000000 _ebss
00000668  w      .text	00000000 DMA1_Channel7_IRQHandler
00000ae0 g     F .text	00000082 TIM_OC1Init
00000668  w      .text	00000000 CAN1_RX1_IRQHandler
00001aba g     F .text	0000002a Delay_Init
00000668  w      .text	00000000 DVP_IRQHandler
00000668  w      .text	00000000 UART5_IRQHandler
00001206 g     F .text	00000200 key_read
200000c1 g     O .bss	00000001 key_old
00001578 g     F .text	00000038 LCD_WR_DATA
00001406 g     F .text	000000dc SPI_LCD_Init
000007be g     F .text	00000004 GPIO_SetBits
00000668  w      .text	00000000 TIM4_IRQHandler
00000b8c g     F .text	0000001a TIM_ARRPreloadConfig
000001c8 g     F .text	00000028 .hidden __riscv_save_9
00001a62 g     F .text	00000058 WaterPump_Control
00000d02 g     F .text	00000020 BrewingControl_Init
00000668  w      .text	00000000 DMA2_Channel1_IRQHandler
00001128 g     F .text	00000016 Heater_Stop
0000353c g     O .text	00000020 __sf_fake_stderr
000001d6 g     F .text	0000001a .hidden __riscv_save_4
00000668  w      .text	00000000 I2C1_EV_IRQHandler
00000bb8 g     F .text	00000018 TIM_GetITStatus
0000081a g     F .text	00000176 RCC_GetClocksFreq
00000668  w      .text	00000000 DMA1_Channel6_IRQHandler
00002066 g     F .text	0000006e .hidden __clzsi2
000029fc g     F .text	00000042 __sfputs_r
00000668  w      .text	00000000 UART4_IRQHandler
00000668  w      .text	00000000 DMA2_Channel4_IRQHandler
0000321c g     F .text	00000018 memchr
00000bdc g     F .text	0000008e USART_Init
00000f9a g     F .text	00000044 DS18B20_WriteBit
00002858 g     F .text	000000a8 _free_r
0000036c g     F .text	0000002c TIM3_IRQHandler
00000668  w      .text	00000000 RCC_IRQHandler
00001984 g     F .text	00000032 WaterLevel_Init
000001f0 g     F .text	0000000c .hidden __riscv_save_3
0000105e g     F .text	00000020 DS18B20_StartConvert
00000668  w      .text	00000000 TIM1_TRG_COM_IRQHandler
00000668  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
00000668  w      .text	00000000 DMA2_Channel7_IRQHandler
20000024 g     O .data	00000010 AHBPrescTable
00003288 g     F .text	0000000c _lseek
00000398 g     F .text	0000003c scheduler_run
00001ef4 g     F .text	000000c6 .hidden __floatsisf
00000668  w      .text	00000000 EXTI15_10_IRQHandler
00001112 g     F .text	00000016 Heater_Start
0000107e g     F .text	0000004c DS18B20_ReadTempRaw
00000b7a g     F .text	00000012 TIM_ITConfig
0000101e g     F .text	0000001c DS18B20_WriteByte
000007c2 g     F .text	00000004 GPIO_ResetBits
00000668  w      .text	00000000 TIM7_IRQHandler
00003176 g     F .text	00000028 _close_r
00000668  w      .text	00000000 CAN2_TX_IRQHandler
20000000 g       .dalign	00000000 _data_vma
00000668  w      .text	00000000 TIM5_IRQHandler
200000c5 g     O .bss	00000001 task_num
00000f46 g     F .text	00000054 DS18B20_Check
000022b0 g     F .text	000000fc __swsetup_r
00000668  w      .text	00000000 EXTI9_5_IRQHandler
00002664 g     F .text	000000a0 __sfp
00000cc4 g     F .text	0000003e Delay_ms
000001c8 g     F .text	00000028 .hidden __riscv_save_10
0000309e g     F .text	0000002c __sread
000019b6 g     F .text	00000010 delay_us
00000668  w      .text	00000000 ETH_WKUP_IRQHandler
00003234 g     F .text	00000002 __malloc_lock
0000020a g     F .text	00000016 .hidden __riscv_restore_4
00000200 g     F .text	00000020 .hidden __riscv_restore_8
00000fde g     F .text	00000040 DS18B20_ReadBit
000024dc g     F .text	00000066 _fflush_r
000001d6 g     F .text	0000001a .hidden __riscv_save_6
00000668  w      .text	00000000 SPI2_IRQHandler
00001548 g     F .text	00000030 LCD_WR_DATA8
0000355c g     O .text	00000020 __sf_fake_stdin
00001634 g     F .text	00000206 LCD_Init
00000220 g     F .text	000000a8 memset
00000200 g     F .text	00000020 .hidden __riscv_restore_9
0000020a g     F .text	00000016 .hidden __riscv_restore_7
000003d4 g     F .text	0000006c main
000019c6 g     F .text	0000001a delay_ms
00000668  w      .text	00000000 TIM10_BRK_IRQHandler
00001bd8 g     F .text	0000008c .hidden __ltsf2
00000668  w      .text	00000000 TIM9_CC_IRQHandler
00003142 g     F .text	00000006 __sclose
00000668  w      .text	00000000 DMA2_Channel5_IRQHandler
00002900 g     F .text	000000d4 _malloc_r
20000108 g     O .bss	00000020 g_pump_ctrl
00000668  w      .text	00000000 DMA1_Channel5_IRQHandler
00000668  w      .text	00000000 EXTI4_IRQHandler
00001ae4 g     F .text	00000036 Delay_Ms
00000668  w      .text	00000000 USB_LP_CAN1_RX0_IRQHandler
00001c64 g     F .text	00000290 .hidden __mulsf3
00000a0a g     F .text	0000001a SPI_Cmd
00000440 g     F .text	000000fa SystemInit
00000668  w      .text	00000000 RNG_IRQHandler
00000df8 g     F .text	000000d4 BrewingControl_Task
0000113e g     F .text	00000036 Heater_Init
0000183a g     F .text	00000042 LCD_Fill
000020d4 g     F .text	00000040 iprintf
00000668  w      .text	00000000 USB_HP_CAN1_TX_IRQHandler
0000343c g     O .text	00000100 .hidden __clz_tab
00000000 g       .init	00000000 _sinit
000010ca g     F .text	00000048 DS18B20_ReadRealtimeTemp
00003148 g     F .text	0000002e _write_r
00000668  w      .text	00000000 DMA1_Channel3_IRQHandler
000015e0 g     F .text	00000054 LCD_Address_Set
00000316 g     F .text	00000056 Tim3_Init
200000f0 g     O .bss	00000018 g_brewing_ctrl
00000668  w      .text	00000000 ETH_IRQHandler
00002cc6 g     F .text	0000010c _printf_common
200000b4 g     O .data	00000004 _impure_ptr
00000668  w      .text	00000000 TIM1_UP_IRQHandler
000023ac g     F .text	00000130 __sflush_r
000002da g     F .text	00000002 lcd_proc
00000668  w      .text	00000000 WWDG_IRQHandler
00000668  w      .text	00000000 USBHSWakeup_IRQHandler
00000668  w      .text	00000000 DMA2_Channel11_IRQHandler
00000668  w      .text	00000000 Ecall_U_Mode_Handler
00000668  w      .text	00000000 DMA2_Channel6_IRQHandler
00000668  w      .text	00000000 TIM2_IRQHandler
20008000 g       .stack	00000000 _eusrstack
000001f0 g     F .text	0000000c .hidden __riscv_save_2
00000668  w      .text	00000000 SW_Handler
00000668  w      .text	00000000 TIM1_BRK_IRQHandler
0000276e g     F .text	00000058 __swhatbuf_r
00000c80 g     F .text	00000008 USART_SendData
00000668  w      .text	00000000 DMA2_Channel10_IRQHandler
00000668  w      .text	00000000 EXTI1_IRQHandler
200000c0 g     O .bss	00000001 key_down
000001d6 g     F .text	0000001a .hidden __riscv_save_5
00001b74 g     F .text	0000003e _write
200000c2 g     O .bss	00000001 key_up
200000c0 g       .data	00000000 _edata
2000012c g       .bss	00000000 _end
00000a36 g     F .text	000000aa TIM_TimeBaseInit
00000668  w      .text	00000000 RTCAlarm_IRQHandler
000035d8 g       .dlalign	00000000 _data_lma
00000668  w      .text	00000000 TIM10_UP_IRQHandler
00000668  w      .text	00000000 TIM9_TRG_COM_IRQHandler
00000668  w      .text	00000000 UART7_IRQHandler
00000668  w      .text	00000000 USART2_IRQHandler
00000668  w      .text	00000000 UART6_IRQHandler
000030ca g     F .text	00000048 __swrite
00002a3e g     F .text	00000288 _vfiprintf_r
00002704 g     F .text	0000006a _fwalk_reent
0000053a g     F .text	0000012e SystemCoreClockUpdate
00000668  w      .text	00000000 I2C2_ER_IRQHandler
00000668  w      .text	00000000 DMA1_Channel2_IRQHandler
0000357c g     O .text	00000020 __sf_fake_stdout
000001fc g     F .text	00000024 .hidden __riscv_restore_12
00000668  w      .text	00000000 TIM8_BRK_IRQHandler
00003294 g     F .text	0000000c _read
0000066a  w      .text	00000000 handle_reset
00000668  w      .text	00000000 CAN1_SCE_IRQHandler
00000668  w      .text	00000000 FLASH_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_0
00000668  w      .text	00000000 USART1_IRQHandler
000027c6 g     F .text	00000092 __smakebuf_r
00002dd2 g     F .text	000002a2 _printf_i
000014e2 g     F .text	00000004 SPI3_IRQHandler
200000dc g     O .bss	00000004 __malloc_sbrk_start
00000668  w      .text	00000000 I2C1_ER_IRQHandler
00000c92 g     F .text	00000032 Delay_us
000009cc g     F .text	0000003e SPI_Init
000007c6 g     F .text	00000006 NVIC_PriorityGroupConfig
200000d8 g     O .bss	00000004 __malloc_free_list
00000214 g     F .text	0000000c .hidden __riscv_restore_1
00002a3e g     F .text	00000288 _vfprintf_r
00000bb4 g     F .text	00000004 TIM_SetCompare1
000007b4 g     F .text	0000000a GPIO_ReadInputDataBit
200000c8 g     O .bss	00000004 uwtick
00000668  w      .text	00000000 USBWakeUp_IRQHandler
00003264 g     F .text	0000000c _close
00000668  w      .text	00000000 DMA2_Channel3_IRQHandler



Disassembly of section .init:

00000000 <_sinit>:
   0:	66a0006f          	j	66a <handle_reset>

Disassembly of section .vector:

00000004 <_vector_base>:
	...
   c:	02c8                	addi	a0,sp,324
   e:	0000                	unimp
  10:	02ca                	slli	t0,t0,0x12
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0668                	addi	a0,sp,780
	...
  22:	0000                	unimp
  24:	0668                	addi	a0,sp,780
  26:	0000                	unimp
  28:	0668                	addi	a0,sp,780
	...
  32:	0000                	unimp
  34:	0668                	addi	a0,sp,780
  36:	0000                	unimp
  38:	0000                	unimp
  3a:	0000                	unimp
  3c:	0668                	addi	a0,sp,780
  3e:	0000                	unimp
  40:	0000                	unimp
  42:	0000                	unimp
  44:	0668                	addi	a0,sp,780
  46:	0000                	unimp
  48:	0668                	addi	a0,sp,780
  4a:	0000                	unimp
  4c:	0668                	addi	a0,sp,780
  4e:	0000                	unimp
  50:	0668                	addi	a0,sp,780
  52:	0000                	unimp
  54:	0668                	addi	a0,sp,780
  56:	0000                	unimp
  58:	0668                	addi	a0,sp,780
  5a:	0000                	unimp
  5c:	0668                	addi	a0,sp,780
  5e:	0000                	unimp
  60:	0668                	addi	a0,sp,780
  62:	0000                	unimp
  64:	0668                	addi	a0,sp,780
  66:	0000                	unimp
  68:	0668                	addi	a0,sp,780
  6a:	0000                	unimp
  6c:	0668                	addi	a0,sp,780
  6e:	0000                	unimp
  70:	0668                	addi	a0,sp,780
  72:	0000                	unimp
  74:	0668                	addi	a0,sp,780
  76:	0000                	unimp
  78:	0668                	addi	a0,sp,780
  7a:	0000                	unimp
  7c:	0668                	addi	a0,sp,780
  7e:	0000                	unimp
  80:	0668                	addi	a0,sp,780
  82:	0000                	unimp
  84:	0668                	addi	a0,sp,780
  86:	0000                	unimp
  88:	0668                	addi	a0,sp,780
  8a:	0000                	unimp
  8c:	0668                	addi	a0,sp,780
  8e:	0000                	unimp
  90:	0668                	addi	a0,sp,780
  92:	0000                	unimp
  94:	0668                	addi	a0,sp,780
  96:	0000                	unimp
  98:	0668                	addi	a0,sp,780
  9a:	0000                	unimp
  9c:	0668                	addi	a0,sp,780
  9e:	0000                	unimp
  a0:	0668                	addi	a0,sp,780
  a2:	0000                	unimp
  a4:	0668                	addi	a0,sp,780
  a6:	0000                	unimp
  a8:	0668                	addi	a0,sp,780
  aa:	0000                	unimp
  ac:	0668                	addi	a0,sp,780
  ae:	0000                	unimp
  b0:	0668                	addi	a0,sp,780
  b2:	0000                	unimp
  b4:	0668                	addi	a0,sp,780
  b6:	0000                	unimp
  b8:	036c                	addi	a1,sp,396
  ba:	0000                	unimp
  bc:	0668                	addi	a0,sp,780
  be:	0000                	unimp
  c0:	0668                	addi	a0,sp,780
  c2:	0000                	unimp
  c4:	0668                	addi	a0,sp,780
  c6:	0000                	unimp
  c8:	0668                	addi	a0,sp,780
  ca:	0000                	unimp
  cc:	0668                	addi	a0,sp,780
  ce:	0000                	unimp
  d0:	0668                	addi	a0,sp,780
  d2:	0000                	unimp
  d4:	0668                	addi	a0,sp,780
  d6:	0000                	unimp
  d8:	0668                	addi	a0,sp,780
  da:	0000                	unimp
  dc:	0668                	addi	a0,sp,780
  de:	0000                	unimp
  e0:	0668                	addi	a0,sp,780
  e2:	0000                	unimp
  e4:	0668                	addi	a0,sp,780
  e6:	0000                	unimp
  e8:	0668                	addi	a0,sp,780
  ea:	0000                	unimp
  ec:	0668                	addi	a0,sp,780
  ee:	0000                	unimp
  f0:	0668                	addi	a0,sp,780
  f2:	0000                	unimp
  f4:	0668                	addi	a0,sp,780
  f6:	0000                	unimp
  f8:	0668                	addi	a0,sp,780
  fa:	0000                	unimp
  fc:	0668                	addi	a0,sp,780
  fe:	0000                	unimp
 100:	0668                	addi	a0,sp,780
 102:	0000                	unimp
 104:	0000                	unimp
 106:	0000                	unimp
 108:	0668                	addi	a0,sp,780
 10a:	0000                	unimp
 10c:	0668                	addi	a0,sp,780
 10e:	0000                	unimp
 110:	14e2                	slli	s1,s1,0x38
 112:	0000                	unimp
 114:	0668                	addi	a0,sp,780
 116:	0000                	unimp
 118:	0668                	addi	a0,sp,780
 11a:	0000                	unimp
 11c:	0668                	addi	a0,sp,780
 11e:	0000                	unimp
 120:	0668                	addi	a0,sp,780
 122:	0000                	unimp
 124:	0668                	addi	a0,sp,780
 126:	0000                	unimp
 128:	0668                	addi	a0,sp,780
 12a:	0000                	unimp
 12c:	0668                	addi	a0,sp,780
 12e:	0000                	unimp
 130:	0668                	addi	a0,sp,780
 132:	0000                	unimp
 134:	0668                	addi	a0,sp,780
 136:	0000                	unimp
 138:	0668                	addi	a0,sp,780
 13a:	0000                	unimp
 13c:	0668                	addi	a0,sp,780
 13e:	0000                	unimp
 140:	0668                	addi	a0,sp,780
 142:	0000                	unimp
 144:	0668                	addi	a0,sp,780
 146:	0000                	unimp
 148:	0668                	addi	a0,sp,780
 14a:	0000                	unimp
 14c:	0668                	addi	a0,sp,780
 14e:	0000                	unimp
 150:	0668                	addi	a0,sp,780
 152:	0000                	unimp
 154:	0668                	addi	a0,sp,780
 156:	0000                	unimp
 158:	0668                	addi	a0,sp,780
 15a:	0000                	unimp
 15c:	0668                	addi	a0,sp,780
 15e:	0000                	unimp
 160:	0668                	addi	a0,sp,780
 162:	0000                	unimp
 164:	0668                	addi	a0,sp,780
 166:	0000                	unimp
 168:	0668                	addi	a0,sp,780
 16a:	0000                	unimp
 16c:	0668                	addi	a0,sp,780
 16e:	0000                	unimp
 170:	0668                	addi	a0,sp,780
 172:	0000                	unimp
 174:	0668                	addi	a0,sp,780
 176:	0000                	unimp
 178:	0668                	addi	a0,sp,780
 17a:	0000                	unimp
 17c:	0668                	addi	a0,sp,780
 17e:	0000                	unimp
 180:	0668                	addi	a0,sp,780
 182:	0000                	unimp
 184:	0668                	addi	a0,sp,780
 186:	0000                	unimp
 188:	0668                	addi	a0,sp,780
 18a:	0000                	unimp
 18c:	0668                	addi	a0,sp,780
 18e:	0000                	unimp
 190:	0668                	addi	a0,sp,780
 192:	0000                	unimp
 194:	0668                	addi	a0,sp,780
 196:	0000                	unimp
 198:	0668                	addi	a0,sp,780
 19a:	0000                	unimp
 19c:	0668                	addi	a0,sp,780
 19e:	0000                	unimp
 1a0:	0668                	addi	a0,sp,780
	...

Disassembly of section .text:

000001c0 <__riscv_save_12>:
     1c0:	7139                	addi	sp,sp,-64
     1c2:	4301                	li	t1,0
     1c4:	c66e                	sw	s11,12(sp)
     1c6:	a019                	j	1cc <__riscv_save_10+0x4>

000001c8 <__riscv_save_10>:
     1c8:	7139                	addi	sp,sp,-64
     1ca:	5341                	li	t1,-16
     1cc:	c86a                	sw	s10,16(sp)
     1ce:	ca66                	sw	s9,20(sp)
     1d0:	cc62                	sw	s8,24(sp)
     1d2:	ce5e                	sw	s7,28(sp)
     1d4:	a019                	j	1da <__riscv_save_4+0x4>

000001d6 <__riscv_save_4>:
     1d6:	7139                	addi	sp,sp,-64
     1d8:	5301                	li	t1,-32
     1da:	d05a                	sw	s6,32(sp)
     1dc:	d256                	sw	s5,36(sp)
     1de:	d452                	sw	s4,40(sp)
     1e0:	d64e                	sw	s3,44(sp)
     1e2:	d84a                	sw	s2,48(sp)
     1e4:	da26                	sw	s1,52(sp)
     1e6:	dc22                	sw	s0,56(sp)
     1e8:	de06                	sw	ra,60(sp)
     1ea:	40610133          	sub	sp,sp,t1
     1ee:	8282                	jr	t0

000001f0 <__riscv_save_0>:
     1f0:	1141                	addi	sp,sp,-16
     1f2:	c04a                	sw	s2,0(sp)
     1f4:	c226                	sw	s1,4(sp)
     1f6:	c422                	sw	s0,8(sp)
     1f8:	c606                	sw	ra,12(sp)
     1fa:	8282                	jr	t0

000001fc <__riscv_restore_12>:
     1fc:	4db2                	lw	s11,12(sp)
     1fe:	0141                	addi	sp,sp,16

00000200 <__riscv_restore_10>:
     200:	4d02                	lw	s10,0(sp)
     202:	4c92                	lw	s9,4(sp)
     204:	4c22                	lw	s8,8(sp)
     206:	4bb2                	lw	s7,12(sp)
     208:	0141                	addi	sp,sp,16

0000020a <__riscv_restore_4>:
     20a:	4b02                	lw	s6,0(sp)
     20c:	4a92                	lw	s5,4(sp)
     20e:	4a22                	lw	s4,8(sp)
     210:	49b2                	lw	s3,12(sp)
     212:	0141                	addi	sp,sp,16

00000214 <__riscv_restore_0>:
     214:	4902                	lw	s2,0(sp)
     216:	4492                	lw	s1,4(sp)
     218:	4422                	lw	s0,8(sp)
     21a:	40b2                	lw	ra,12(sp)
     21c:	0141                	addi	sp,sp,16
     21e:	8082                	ret

00000220 <memset>:
     220:	433d                	li	t1,15
     222:	872a                	mv	a4,a0
     224:	02c37363          	bgeu	t1,a2,24a <memset+0x2a>
     228:	00f77793          	andi	a5,a4,15
     22c:	efbd                	bnez	a5,2aa <memset+0x8a>
     22e:	e5ad                	bnez	a1,298 <memset+0x78>
     230:	ff067693          	andi	a3,a2,-16
     234:	8a3d                	andi	a2,a2,15
     236:	96ba                	add	a3,a3,a4
     238:	c30c                	sw	a1,0(a4)
     23a:	c34c                	sw	a1,4(a4)
     23c:	c70c                	sw	a1,8(a4)
     23e:	c74c                	sw	a1,12(a4)
     240:	0741                	addi	a4,a4,16
     242:	fed76be3          	bltu	a4,a3,238 <memset+0x18>
     246:	e211                	bnez	a2,24a <memset+0x2a>
     248:	8082                	ret
     24a:	40c306b3          	sub	a3,t1,a2
     24e:	068a                	slli	a3,a3,0x2
     250:	00000297          	auipc	t0,0x0
     254:	9696                	add	a3,a3,t0
     256:	00a68067          	jr	10(a3)
     25a:	00b70723          	sb	a1,14(a4)
     25e:	00b706a3          	sb	a1,13(a4)
     262:	00b70623          	sb	a1,12(a4)
     266:	00b705a3          	sb	a1,11(a4)
     26a:	00b70523          	sb	a1,10(a4)
     26e:	00b704a3          	sb	a1,9(a4)
     272:	00b70423          	sb	a1,8(a4)
     276:	00b703a3          	sb	a1,7(a4)
     27a:	00b70323          	sb	a1,6(a4)
     27e:	00b702a3          	sb	a1,5(a4)
     282:	00b70223          	sb	a1,4(a4)
     286:	00b701a3          	sb	a1,3(a4)
     28a:	00b70123          	sb	a1,2(a4)
     28e:	00b700a3          	sb	a1,1(a4)
     292:	00b70023          	sb	a1,0(a4)
     296:	8082                	ret
     298:	0ff5f593          	andi	a1,a1,255
     29c:	00859693          	slli	a3,a1,0x8
     2a0:	8dd5                	or	a1,a1,a3
     2a2:	01059693          	slli	a3,a1,0x10
     2a6:	8dd5                	or	a1,a1,a3
     2a8:	b761                	j	230 <memset+0x10>
     2aa:	00279693          	slli	a3,a5,0x2
     2ae:	00000297          	auipc	t0,0x0
     2b2:	9696                	add	a3,a3,t0
     2b4:	8286                	mv	t0,ra
     2b6:	fa8680e7          	jalr	-88(a3)
     2ba:	8096                	mv	ra,t0
     2bc:	17c1                	addi	a5,a5,-16
     2be:	8f1d                	sub	a4,a4,a5
     2c0:	963e                	add	a2,a2,a5
     2c2:	f8c374e3          	bgeu	t1,a2,24a <memset+0x2a>
     2c6:	b7a5                	j	22e <memset+0xe>

000002c8 <NMI_Handler>:
     2c8:	a001                	j	2c8 <NMI_Handler>

000002ca <HardFault_Handler>:
     2ca:	beef07b7          	lui	a5,0xbeef0
     2ce:	e000e737          	lui	a4,0xe000e
     2d2:	08078793          	addi	a5,a5,128 # beef0080 <_eusrstack+0x9eee8080>
     2d6:	c73c                	sw	a5,72(a4)
     2d8:	a001                	j	2d8 <HardFault_Handler+0xe>

000002da <lcd_proc>:
     2da:	8082                	ret

000002dc <key_proc>:
     2dc:	f15ff2ef          	jal	t0,1f0 <__riscv_save_0>
     2e0:	727000ef          	jal	ra,1206 <key_read>
     2e4:	81918713          	addi	a4,gp,-2023 # 200000c1 <key_old>
     2e8:	80a18da3          	sb	a0,-2021(gp) # 200000c3 <key_val>
     2ec:	231c                	lbu	a5,0(a4)
     2ee:	a308                	sb	a0,0(a4)
     2f0:	00f54633          	xor	a2,a0,a5
     2f4:	fff7c793          	not	a5,a5
     2f8:	80c18e23          	sb	a2,-2020(gp) # 200000c4 <p>
     2fc:	8fe9                	and	a5,a5,a0
     2fe:	80f18c23          	sb	a5,-2024(gp) # 200000c0 <_edata>
     302:	fff54693          	not	a3,a0
     306:	8ef1                	and	a3,a3,a2
     308:	80d18d23          	sb	a3,-2022(gp) # 200000c2 <key_up>
     30c:	c781                	beqz	a5,314 <key_proc+0x38>
     30e:	853e                	mv	a0,a5
     310:	235000ef          	jal	ra,d44 <BrewingControl_KeyHandler>
     314:	b701                	j	214 <__riscv_restore_0>

00000316 <Tim3_Init>:
     316:	edbff2ef          	jal	t0,1f0 <__riscv_save_0>
     31a:	1101                	addi	sp,sp,-32
     31c:	84aa                	mv	s1,a0
     31e:	842e                	mv	s0,a1
     320:	4509                	li	a0,2
     322:	4585                	li	a1,1
     324:	2569                	jal	9ae <RCC_APB1PeriphClockCmd>
     326:	82e0                	sh	s0,20(sp)
     328:	40000437          	lui	s0,0x40000
     32c:	084c                	addi	a1,sp,20
     32e:	40040513          	addi	a0,s0,1024 # 40000400 <_eusrstack+0x1fff8400>
     332:	84e4                	sh	s1,24(sp)
     334:	00011d23          	sh	zero,26(sp)
     338:	00011b23          	sh	zero,22(sp)
     33c:	2ded                	jal	a36 <TIM_TimeBaseInit>
     33e:	4605                	li	a2,1
     340:	04100593          	li	a1,65
     344:	40040513          	addi	a0,s0,1024
     348:	033000ef          	jal	ra,b7a <TIM_ITConfig>
     34c:	12d00793          	li	a5,301
     350:	867c                	sh	a5,12(sp)
     352:	478d                	li	a5,3
     354:	875c                	sb	a5,14(sp)
     356:	0068                	addi	a0,sp,12
     358:	4785                	li	a5,1
     35a:	c83e                	sw	a5,16(sp)
     35c:	2985                	jal	7cc <NVIC_Init>
     35e:	4585                	li	a1,1
     360:	40040513          	addi	a0,s0,1024
     364:	7fe000ef          	jal	ra,b62 <TIM_Cmd>
     368:	6105                	addi	sp,sp,32
     36a:	b56d                	j	214 <__riscv_restore_0>

0000036c <TIM3_IRQHandler>:
     36c:	40000537          	lui	a0,0x40000
     370:	4585                	li	a1,1
     372:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     376:	043000ef          	jal	ra,bb8 <TIM_GetITStatus>
     37a:	c511                	beqz	a0,386 <TIM3_IRQHandler+0x1a>
     37c:	82018793          	addi	a5,gp,-2016 # 200000c8 <uwtick>
     380:	4398                	lw	a4,0(a5)
     382:	0705                	addi	a4,a4,1
     384:	c398                	sw	a4,0(a5)
     386:	40000537          	lui	a0,0x40000
     38a:	4585                	li	a1,1
     38c:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     390:	041000ef          	jal	ra,bd0 <TIM_ClearITPendingBit>
     394:	30200073          	mret

00000398 <scheduler_run>:
     398:	e3fff2ef          	jal	t0,1d6 <__riscv_save_4>
     39c:	200004b7          	lui	s1,0x20000
     3a0:	4401                	li	s0,0
     3a2:	00048493          	mv	s1,s1
     3a6:	4a31                	li	s4,12
     3a8:	81d1c783          	lbu	a5,-2019(gp) # 200000c5 <task_num>
     3ac:	00f46363          	bltu	s0,a5,3b2 <scheduler_run+0x1a>
     3b0:	bda9                	j	20a <__riscv_restore_4>
     3b2:	034407b3          	mul	a5,s0,s4
     3b6:	8201a683          	lw	a3,-2016(gp) # 200000c8 <uwtick>
     3ba:	97a6                	add	a5,a5,s1
     3bc:	43d8                	lw	a4,4(a5)
     3be:	4790                	lw	a2,8(a5)
     3c0:	9732                	add	a4,a4,a2
     3c2:	00e6e563          	bltu	a3,a4,3cc <scheduler_run+0x34>
     3c6:	c794                	sw	a3,8(a5)
     3c8:	439c                	lw	a5,0(a5)
     3ca:	9782                	jalr	a5
     3cc:	0405                	addi	s0,s0,1
     3ce:	0ff47413          	andi	s0,s0,255
     3d2:	bfd9                	j	3a8 <scheduler_run+0x10>

000003d4 <main>:
     3d4:	e1dff2ef          	jal	t0,1f0 <__riscv_save_0>
     3d8:	4509                	li	a0,2
     3da:	26f5                	jal	7c6 <NVIC_PriorityGroupConfig>
     3dc:	2ab9                	jal	53a <SystemCoreClockUpdate>
     3de:	6571                	lui	a0,0x1c
     3e0:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x18c28>
     3e4:	736010ef          	jal	ra,1b1a <USART_Printf_Init>
     3e8:	6d2010ef          	jal	ra,1aba <Delay_Init>
     3ec:	553000ef          	jal	ra,113e <Heater_Init>
     3f0:	5f0010ef          	jal	ra,19e0 <WaterPump_Init>
     3f4:	590010ef          	jal	ra,1984 <WaterLevel_Init>
     3f8:	484010ef          	jal	ra,187c <TIM2_PWM_Init>
     3fc:	107000ef          	jal	ra,d02 <BrewingControl_Init>
     400:	234010ef          	jal	ra,1634 <LCD_Init>
     404:	6741                	lui	a4,0x10
     406:	177d                	addi	a4,a4,-1
     408:	07f00693          	li	a3,127
     40c:	07f00613          	li	a2,127
     410:	4581                	li	a1,0
     412:	4501                	li	a0,0
     414:	426010ef          	jal	ra,183a <LCD_Fill>
     418:	55d000ef          	jal	ra,1174 <key_init>
     41c:	05f00593          	li	a1,95
     420:	3e800513          	li	a0,1000
     424:	3dcd                	jal	316 <Tim3_Init>
     426:	00003537          	lui	a0,0x3
     42a:	470d                	li	a4,3
     42c:	2a050513          	addi	a0,a0,672 # 32a0 <_read+0xc>
     430:	80e18ea3          	sb	a4,-2019(gp) # 200000c5 <task_num>
     434:	5b5010ef          	jal	ra,21e8 <puts>
     438:	0eb000ef          	jal	ra,d22 <BrewingControl_Start>
     43c:	3fb1                	jal	398 <scheduler_run>
     43e:	bffd                	j	43c <main+0x68>

00000440 <SystemInit>:
     440:	400217b7          	lui	a5,0x40021
     444:	4398                	lw	a4,0(a5)
     446:	f0ff06b7          	lui	a3,0xf0ff0
     44a:	1141                	addi	sp,sp,-16
     44c:	00176713          	ori	a4,a4,1
     450:	c398                	sw	a4,0(a5)
     452:	43d8                	lw	a4,4(a5)
     454:	00020637          	lui	a2,0x20
     458:	8f75                	and	a4,a4,a3
     45a:	c3d8                	sw	a4,4(a5)
     45c:	4398                	lw	a4,0(a5)
     45e:	fef706b7          	lui	a3,0xfef70
     462:	16fd                	addi	a3,a3,-1
     464:	8f75                	and	a4,a4,a3
     466:	c398                	sw	a4,0(a5)
     468:	4398                	lw	a4,0(a5)
     46a:	fffc06b7          	lui	a3,0xfffc0
     46e:	16fd                	addi	a3,a3,-1
     470:	8f75                	and	a4,a4,a3
     472:	c398                	sw	a4,0(a5)
     474:	43d8                	lw	a4,4(a5)
     476:	ff0106b7          	lui	a3,0xff010
     47a:	16fd                	addi	a3,a3,-1
     47c:	8f75                	and	a4,a4,a3
     47e:	c3d8                	sw	a4,4(a5)
     480:	4398                	lw	a4,0(a5)
     482:	ec0006b7          	lui	a3,0xec000
     486:	16fd                	addi	a3,a3,-1
     488:	8f75                	and	a4,a4,a3
     48a:	c398                	sw	a4,0(a5)
     48c:	00ff0737          	lui	a4,0xff0
     490:	c798                	sw	a4,8(a5)
     492:	0207a623          	sw	zero,44(a5) # 4002102c <_eusrstack+0x2001902c>
     496:	c402                	sw	zero,8(sp)
     498:	c602                	sw	zero,12(sp)
     49a:	4398                	lw	a4,0(a5)
     49c:	66c1                	lui	a3,0x10
     49e:	8f55                	or	a4,a4,a3
     4a0:	c398                	sw	a4,0(a5)
     4a2:	400216b7          	lui	a3,0x40021
     4a6:	6705                	lui	a4,0x1
     4a8:	429c                	lw	a5,0(a3)
     4aa:	8ff1                	and	a5,a5,a2
     4ac:	c63e                	sw	a5,12(sp)
     4ae:	47a2                	lw	a5,8(sp)
     4b0:	0785                	addi	a5,a5,1
     4b2:	c43e                	sw	a5,8(sp)
     4b4:	47b2                	lw	a5,12(sp)
     4b6:	e781                	bnez	a5,4be <SystemInit+0x7e>
     4b8:	47a2                	lw	a5,8(sp)
     4ba:	fee797e3          	bne	a5,a4,4a8 <SystemInit+0x68>
     4be:	400217b7          	lui	a5,0x40021
     4c2:	439c                	lw	a5,0(a5)
     4c4:	00e79713          	slli	a4,a5,0xe
     4c8:	06075763          	bgez	a4,536 <SystemInit+0xf6>
     4cc:	4785                	li	a5,1
     4ce:	c63e                	sw	a5,12(sp)
     4d0:	4732                	lw	a4,12(sp)
     4d2:	4785                	li	a5,1
     4d4:	04f71f63          	bne	a4,a5,532 <SystemInit+0xf2>
     4d8:	400217b7          	lui	a5,0x40021
     4dc:	43d8                	lw	a4,4(a5)
     4de:	ffc106b7          	lui	a3,0xffc10
     4e2:	16fd                	addi	a3,a3,-1
     4e4:	c3d8                	sw	a4,4(a5)
     4e6:	43d8                	lw	a4,4(a5)
     4e8:	c3d8                	sw	a4,4(a5)
     4ea:	43d8                	lw	a4,4(a5)
     4ec:	40076713          	ori	a4,a4,1024
     4f0:	c3d8                	sw	a4,4(a5)
     4f2:	43d8                	lw	a4,4(a5)
     4f4:	8f75                	and	a4,a4,a3
     4f6:	c3d8                	sw	a4,4(a5)
     4f8:	43d8                	lw	a4,4(a5)
     4fa:	002906b7          	lui	a3,0x290
     4fe:	8f55                	or	a4,a4,a3
     500:	c3d8                	sw	a4,4(a5)
     502:	4398                	lw	a4,0(a5)
     504:	010006b7          	lui	a3,0x1000
     508:	8f55                	or	a4,a4,a3
     50a:	c398                	sw	a4,0(a5)
     50c:	4398                	lw	a4,0(a5)
     50e:	00671693          	slli	a3,a4,0x6
     512:	fe06dde3          	bgez	a3,50c <SystemInit+0xcc>
     516:	43d8                	lw	a4,4(a5)
     518:	400216b7          	lui	a3,0x40021
     51c:	9b71                	andi	a4,a4,-4
     51e:	c3d8                	sw	a4,4(a5)
     520:	43d8                	lw	a4,4(a5)
     522:	00276713          	ori	a4,a4,2
     526:	c3d8                	sw	a4,4(a5)
     528:	4721                	li	a4,8
     52a:	42dc                	lw	a5,4(a3)
     52c:	8bb1                	andi	a5,a5,12
     52e:	fee79ee3          	bne	a5,a4,52a <SystemInit+0xea>
     532:	0141                	addi	sp,sp,16
     534:	8082                	ret
     536:	c602                	sw	zero,12(sp)
     538:	bf61                	j	4d0 <SystemInit+0x90>

0000053a <SystemCoreClockUpdate>:
     53a:	400216b7          	lui	a3,0x40021
     53e:	42d8                	lw	a4,4(a3)
     540:	200007b7          	lui	a5,0x20000
     544:	4611                	li	a2,4
     546:	8b31                	andi	a4,a4,12
     548:	0a878793          	addi	a5,a5,168 # 200000a8 <SystemCoreClock>
     54c:	00c70563          	beq	a4,a2,556 <SystemCoreClockUpdate+0x1c>
     550:	4621                	li	a2,8
     552:	02c70863          	beq	a4,a2,582 <SystemCoreClockUpdate+0x48>
     556:	007a1737          	lui	a4,0x7a1
     55a:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
     55e:	c398                	sw	a4,0(a5)
     560:	40021737          	lui	a4,0x40021
     564:	4358                	lw	a4,4(a4)
     566:	8311                	srli	a4,a4,0x4
     568:	00f77693          	andi	a3,a4,15
     56c:	20000737          	lui	a4,0x20000
     570:	02470713          	addi	a4,a4,36 # 20000024 <AHBPrescTable>
     574:	9736                	add	a4,a4,a3
     576:	2314                	lbu	a3,0(a4)
     578:	4398                	lw	a4,0(a5)
     57a:	00d75733          	srl	a4,a4,a3
     57e:	c398                	sw	a4,0(a5)
     580:	8082                	ret
     582:	42d8                	lw	a4,4(a3)
     584:	42d4                	lw	a3,4(a3)
     586:	6641                	lui	a2,0x10
     588:	8349                	srli	a4,a4,0x12
     58a:	8b3d                	andi	a4,a4,15
     58c:	8ef1                	and	a3,a3,a2
     58e:	00270613          	addi	a2,a4,2
     592:	cf15                	beqz	a4,5ce <SystemCoreClockUpdate+0x94>
     594:	473d                	li	a4,15
     596:	02e60f63          	beq	a2,a4,5d4 <SystemCoreClockUpdate+0x9a>
     59a:	4741                	li	a4,16
     59c:	02e60f63          	beq	a2,a4,5da <SystemCoreClockUpdate+0xa0>
     5a0:	4745                	li	a4,17
     5a2:	4581                	li	a1,0
     5a4:	00e61363          	bne	a2,a4,5aa <SystemCoreClockUpdate+0x70>
     5a8:	4641                	li	a2,16
     5aa:	e2a1                	bnez	a3,5ea <SystemCoreClockUpdate+0xb0>
     5ac:	40024737          	lui	a4,0x40024
     5b0:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     5b4:	8b41                	andi	a4,a4,16
     5b6:	c70d                	beqz	a4,5e0 <SystemCoreClockUpdate+0xa6>
     5b8:	007a1737          	lui	a4,0x7a1
     5bc:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
     5c0:	02c70633          	mul	a2,a4,a2
     5c4:	c390                	sw	a2,0(a5)
     5c6:	ddc9                	beqz	a1,560 <SystemCoreClockUpdate+0x26>
     5c8:	4398                	lw	a4,0(a5)
     5ca:	8305                	srli	a4,a4,0x1
     5cc:	bf49                	j	55e <SystemCoreClockUpdate+0x24>
     5ce:	4581                	li	a1,0
     5d0:	4649                	li	a2,18
     5d2:	bfe1                	j	5aa <SystemCoreClockUpdate+0x70>
     5d4:	4585                	li	a1,1
     5d6:	4635                	li	a2,13
     5d8:	bfc9                	j	5aa <SystemCoreClockUpdate+0x70>
     5da:	4581                	li	a1,0
     5dc:	463d                	li	a2,15
     5de:	b7f1                	j	5aa <SystemCoreClockUpdate+0x70>
     5e0:	003d1737          	lui	a4,0x3d1
     5e4:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3cd328>
     5e8:	bfe1                	j	5c0 <SystemCoreClockUpdate+0x86>
     5ea:	40021537          	lui	a0,0x40021
     5ee:	5558                	lw	a4,44(a0)
     5f0:	00f71693          	slli	a3,a4,0xf
     5f4:	5558                	lw	a4,44(a0)
     5f6:	0406df63          	bgez	a3,654 <SystemCoreClockUpdate+0x11a>
     5fa:	8311                	srli	a4,a4,0x4
     5fc:	8b3d                	andi	a4,a4,15
     5fe:	00170693          	addi	a3,a4,1
     602:	007a1737          	lui	a4,0x7a1
     606:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
     60a:	02d75733          	divu	a4,a4,a3
     60e:	c398                	sw	a4,0(a5)
     610:	5554                	lw	a3,44(a0)
     612:	82a1                	srli	a3,a3,0x8
     614:	8abd                	andi	a3,a3,15
     616:	e28d                	bnez	a3,638 <SystemCoreClockUpdate+0xfe>
     618:	4695                	li	a3,5
     61a:	02d70733          	mul	a4,a4,a3
     61e:	8305                	srli	a4,a4,0x1
     620:	c398                	sw	a4,0(a5)
     622:	40021737          	lui	a4,0x40021
     626:	5758                	lw	a4,44(a4)
     628:	4394                	lw	a3,0(a5)
     62a:	8b3d                	andi	a4,a4,15
     62c:	0705                	addi	a4,a4,1
     62e:	02e6d733          	divu	a4,a3,a4
     632:	c398                	sw	a4,0(a5)
     634:	4398                	lw	a4,0(a5)
     636:	b769                	j	5c0 <SystemCoreClockUpdate+0x86>
     638:	4505                	li	a0,1
     63a:	00a69463          	bne	a3,a0,642 <SystemCoreClockUpdate+0x108>
     63e:	46e5                	li	a3,25
     640:	bfe9                	j	61a <SystemCoreClockUpdate+0xe0>
     642:	453d                	li	a0,15
     644:	00a69663          	bne	a3,a0,650 <SystemCoreClockUpdate+0x116>
     648:	46d1                	li	a3,20
     64a:	02e68733          	mul	a4,a3,a4
     64e:	bfc9                	j	620 <SystemCoreClockUpdate+0xe6>
     650:	0689                	addi	a3,a3,2
     652:	bfe5                	j	64a <SystemCoreClockUpdate+0x110>
     654:	8b3d                	andi	a4,a4,15
     656:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     65a:	007a1737          	lui	a4,0x7a1
     65e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
     662:	02d75733          	divu	a4,a4,a3
     666:	b7f1                	j	632 <SystemCoreClockUpdate+0xf8>

00000668 <ADC1_2_IRQHandler>:
     668:	a001                	j	668 <ADC1_2_IRQHandler>

0000066a <handle_reset>:
     66a:	20000197          	auipc	gp,0x20000
     66e:	23e18193          	addi	gp,gp,574 # 200008a8 <__global_pointer$>
     672:	20008117          	auipc	sp,0x20008
     676:	98e10113          	addi	sp,sp,-1650 # 20008000 <_eusrstack>
     67a:	00003517          	auipc	a0,0x3
     67e:	f5e50513          	addi	a0,a0,-162 # 35d8 <_data_lma>
     682:	20000597          	auipc	a1,0x20000
     686:	97e58593          	addi	a1,a1,-1666 # 20000000 <_data_vma>
     68a:	81818613          	addi	a2,gp,-2024 # 200000c0 <_edata>
     68e:	00c5fa63          	bgeu	a1,a2,6a2 <handle_reset+0x38>
     692:	00052283          	lw	t0,0(a0)
     696:	0055a023          	sw	t0,0(a1)
     69a:	0511                	addi	a0,a0,4
     69c:	0591                	addi	a1,a1,4
     69e:	fec5eae3          	bltu	a1,a2,692 <handle_reset+0x28>
     6a2:	81818513          	addi	a0,gp,-2024 # 200000c0 <_edata>
     6a6:	88418593          	addi	a1,gp,-1916 # 2000012c <_ebss>
     6aa:	00b57763          	bgeu	a0,a1,6b8 <handle_reset+0x4e>
     6ae:	00052023          	sw	zero,0(a0)
     6b2:	0511                	addi	a0,a0,4
     6b4:	feb56de3          	bltu	a0,a1,6ae <handle_reset+0x44>
     6b8:	42fd                	li	t0,31
     6ba:	bc029073          	csrw	0xbc0,t0
     6be:	42ad                	li	t0,11
     6c0:	80429073          	csrw	0x804,t0
     6c4:	000062b7          	lui	t0,0x6
     6c8:	08828293          	addi	t0,t0,136 # 6088 <_data_lma+0x2ab0>
     6cc:	30029073          	csrw	mstatus,t0
     6d0:	00000297          	auipc	t0,0x0
     6d4:	93428293          	addi	t0,t0,-1740 # 4 <_einit>
     6d8:	0032e293          	ori	t0,t0,3
     6dc:	30529073          	csrw	mtvec,t0
     6e0:	d61ff0ef          	jal	ra,440 <SystemInit>
     6e4:	00000297          	auipc	t0,0x0
     6e8:	cf028293          	addi	t0,t0,-784 # 3d4 <main>
     6ec:	34129073          	csrw	mepc,t0
     6f0:	30200073          	mret

000006f4 <GPIO_Init>:
     6f4:	459c                	lw	a5,8(a1)
     6f6:	0107f713          	andi	a4,a5,16
     6fa:	00f7f813          	andi	a6,a5,15
     6fe:	c701                	beqz	a4,706 <GPIO_Init+0x12>
     700:	41d8                	lw	a4,4(a1)
     702:	00e86833          	or	a6,a6,a4
     706:	218e                	lhu	a1,0(a1)
     708:	0ff5f713          	andi	a4,a1,255
     70c:	c339                	beqz	a4,752 <GPIO_Init+0x5e>
     70e:	4118                	lw	a4,0(a0)
     710:	4681                	li	a3,0
     712:	4e85                	li	t4,1
     714:	4f3d                	li	t5,15
     716:	02800f93          	li	t6,40
     71a:	04800293          	li	t0,72
     71e:	4e21                	li	t3,8
     720:	00de9633          	sll	a2,t4,a3
     724:	00c5f8b3          	and	a7,a1,a2
     728:	03161163          	bne	a2,a7,74a <GPIO_Init+0x56>
     72c:	00269893          	slli	a7,a3,0x2
     730:	011f1333          	sll	t1,t5,a7
     734:	fff34313          	not	t1,t1
     738:	00e37733          	and	a4,t1,a4
     73c:	011818b3          	sll	a7,a6,a7
     740:	00e8e733          	or	a4,a7,a4
     744:	05f79f63          	bne	a5,t6,7a2 <GPIO_Init+0xae>
     748:	c950                	sw	a2,20(a0)
     74a:	0685                	addi	a3,a3,1
     74c:	fdc69ae3          	bne	a3,t3,720 <GPIO_Init+0x2c>
     750:	c118                	sw	a4,0(a0)
     752:	0ff00713          	li	a4,255
     756:	04b77563          	bgeu	a4,a1,7a0 <GPIO_Init+0xac>
     75a:	4154                	lw	a3,4(a0)
     75c:	4621                	li	a2,8
     75e:	4e85                	li	t4,1
     760:	4f3d                	li	t5,15
     762:	02800f93          	li	t6,40
     766:	04800293          	li	t0,72
     76a:	4e41                	li	t3,16
     76c:	00ce98b3          	sll	a7,t4,a2
     770:	0115f733          	and	a4,a1,a7
     774:	02e89263          	bne	a7,a4,798 <GPIO_Init+0xa4>
     778:	00261713          	slli	a4,a2,0x2
     77c:	1701                	addi	a4,a4,-32
     77e:	00ef1333          	sll	t1,t5,a4
     782:	fff34313          	not	t1,t1
     786:	00d376b3          	and	a3,t1,a3
     78a:	00e81733          	sll	a4,a6,a4
     78e:	8ed9                	or	a3,a3,a4
     790:	01f79d63          	bne	a5,t6,7aa <GPIO_Init+0xb6>
     794:	01152a23          	sw	a7,20(a0)
     798:	0605                	addi	a2,a2,1
     79a:	fdc619e3          	bne	a2,t3,76c <GPIO_Init+0x78>
     79e:	c154                	sw	a3,4(a0)
     7a0:	8082                	ret
     7a2:	fa5794e3          	bne	a5,t0,74a <GPIO_Init+0x56>
     7a6:	c910                	sw	a2,16(a0)
     7a8:	b74d                	j	74a <GPIO_Init+0x56>
     7aa:	fe5797e3          	bne	a5,t0,798 <GPIO_Init+0xa4>
     7ae:	01152823          	sw	a7,16(a0)
     7b2:	b7dd                	j	798 <GPIO_Init+0xa4>

000007b4 <GPIO_ReadInputDataBit>:
     7b4:	4508                	lw	a0,8(a0)
     7b6:	8d6d                	and	a0,a0,a1
     7b8:	00a03533          	snez	a0,a0
     7bc:	8082                	ret

000007be <GPIO_SetBits>:
     7be:	c90c                	sw	a1,16(a0)
     7c0:	8082                	ret

000007c2 <GPIO_ResetBits>:
     7c2:	c94c                	sw	a1,20(a0)
     7c4:	8082                	ret

000007c6 <NVIC_PriorityGroupConfig>:
     7c6:	82a1a223          	sw	a0,-2012(gp) # 200000cc <NVIC_Priority_Group>
     7ca:	8082                	ret

000007cc <NVIC_Init>:
     7cc:	8241a703          	lw	a4,-2012(gp) # 200000cc <NVIC_Priority_Group>
     7d0:	4789                	li	a5,2
     7d2:	2110                	lbu	a2,0(a0)
     7d4:	02f71163          	bne	a4,a5,7f6 <NVIC_Init+0x2a>
     7d8:	3114                	lbu	a3,1(a0)
     7da:	478d                	li	a5,3
     7dc:	00d7ed63          	bltu	a5,a3,7f6 <NVIC_Init+0x2a>
     7e0:	213c                	lbu	a5,2(a0)
     7e2:	069a                	slli	a3,a3,0x6
     7e4:	e000e737          	lui	a4,0xe000e
     7e8:	0796                	slli	a5,a5,0x5
     7ea:	8fd5                	or	a5,a5,a3
     7ec:	0ff7f793          	andi	a5,a5,255
     7f0:	9732                	add	a4,a4,a2
     7f2:	40f70023          	sb	a5,1024(a4) # e000e400 <_eusrstack+0xc0006400>
     7f6:	4154                	lw	a3,4(a0)
     7f8:	4705                	li	a4,1
     7fa:	00565793          	srli	a5,a2,0x5
     7fe:	00c71733          	sll	a4,a4,a2
     802:	ca89                	beqz	a3,814 <__stack_size+0x14>
     804:	04078793          	addi	a5,a5,64
     808:	078a                	slli	a5,a5,0x2
     80a:	e000e6b7          	lui	a3,0xe000e
     80e:	97b6                	add	a5,a5,a3
     810:	c398                	sw	a4,0(a5)
     812:	8082                	ret
     814:	06078793          	addi	a5,a5,96
     818:	bfc5                	j	808 <__stack_size+0x8>

0000081a <RCC_GetClocksFreq>:
     81a:	40021737          	lui	a4,0x40021
     81e:	435c                	lw	a5,4(a4)
     820:	4691                	li	a3,4
     822:	8bb1                	andi	a5,a5,12
     824:	00d78563          	beq	a5,a3,82e <RCC_GetClocksFreq+0x14>
     828:	46a1                	li	a3,8
     82a:	06d78263          	beq	a5,a3,88e <RCC_GetClocksFreq+0x74>
     82e:	007a17b7          	lui	a5,0x7a1
     832:	20078793          	addi	a5,a5,512 # 7a1200 <_data_lma+0x79dc28>
     836:	c11c                	sw	a5,0(a0)
     838:	40021637          	lui	a2,0x40021
     83c:	425c                	lw	a5,4(a2)
     83e:	20000737          	lui	a4,0x20000
     842:	03470713          	addi	a4,a4,52 # 20000034 <APBAHBPrescTable>
     846:	8391                	srli	a5,a5,0x4
     848:	8bbd                	andi	a5,a5,15
     84a:	97ba                	add	a5,a5,a4
     84c:	2394                	lbu	a3,0(a5)
     84e:	411c                	lw	a5,0(a0)
     850:	00d7d7b3          	srl	a5,a5,a3
     854:	c15c                	sw	a5,4(a0)
     856:	4254                	lw	a3,4(a2)
     858:	82a1                	srli	a3,a3,0x8
     85a:	8a9d                	andi	a3,a3,7
     85c:	96ba                	add	a3,a3,a4
     85e:	2294                	lbu	a3,0(a3)
     860:	00d7d6b3          	srl	a3,a5,a3
     864:	c514                	sw	a3,8(a0)
     866:	4254                	lw	a3,4(a2)
     868:	82ad                	srli	a3,a3,0xb
     86a:	8a9d                	andi	a3,a3,7
     86c:	9736                	add	a4,a4,a3
     86e:	2318                	lbu	a4,0(a4)
     870:	00e7d7b3          	srl	a5,a5,a4
     874:	c55c                	sw	a5,12(a0)
     876:	4258                	lw	a4,4(a2)
     878:	8339                	srli	a4,a4,0xe
     87a:	00377693          	andi	a3,a4,3
     87e:	80418713          	addi	a4,gp,-2044 # 200000ac <ADCPrescTable>
     882:	9736                	add	a4,a4,a3
     884:	2318                	lbu	a4,0(a4)
     886:	02e7d7b3          	divu	a5,a5,a4
     88a:	c91c                	sw	a5,16(a0)
     88c:	8082                	ret
     88e:	435c                	lw	a5,4(a4)
     890:	4358                	lw	a4,4(a4)
     892:	66c1                	lui	a3,0x10
     894:	83c9                	srli	a5,a5,0x12
     896:	8f75                	and	a4,a4,a3
     898:	1ffff6b7          	lui	a3,0x1ffff
     89c:	70c6a683          	lw	a3,1804(a3) # 1ffff70c <_data_lma+0x1fffc134>
     8a0:	8bbd                	andi	a5,a5,15
     8a2:	0789                	addi	a5,a5,2
     8a4:	01169613          	slli	a2,a3,0x11
     8a8:	00064863          	bltz	a2,8b8 <RCC_GetClocksFreq+0x9e>
     8ac:	46c5                	li	a3,17
     8ae:	4601                	li	a2,0
     8b0:	02d79263          	bne	a5,a3,8d4 <RCC_GetClocksFreq+0xba>
     8b4:	47c9                	li	a5,18
     8b6:	a839                	j	8d4 <RCC_GetClocksFreq+0xba>
     8b8:	4689                	li	a3,2
     8ba:	02d78f63          	beq	a5,a3,8f8 <RCC_GetClocksFreq+0xde>
     8be:	46bd                	li	a3,15
     8c0:	02d78e63          	beq	a5,a3,8fc <RCC_GetClocksFreq+0xe2>
     8c4:	46c1                	li	a3,16
     8c6:	02d78e63          	beq	a5,a3,902 <RCC_GetClocksFreq+0xe8>
     8ca:	46c5                	li	a3,17
     8cc:	4601                	li	a2,0
     8ce:	00d79363          	bne	a5,a3,8d4 <RCC_GetClocksFreq+0xba>
     8d2:	47c1                	li	a5,16
     8d4:	ef1d                	bnez	a4,912 <RCC_GetClocksFreq+0xf8>
     8d6:	40024737          	lui	a4,0x40024
     8da:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     8de:	8b41                	andi	a4,a4,16
     8e0:	c705                	beqz	a4,908 <RCC_GetClocksFreq+0xee>
     8e2:	007a1737          	lui	a4,0x7a1
     8e6:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
     8ea:	02f707b3          	mul	a5,a4,a5
     8ee:	c11c                	sw	a5,0(a0)
     8f0:	d621                	beqz	a2,838 <RCC_GetClocksFreq+0x1e>
     8f2:	411c                	lw	a5,0(a0)
     8f4:	8385                	srli	a5,a5,0x1
     8f6:	b781                	j	836 <RCC_GetClocksFreq+0x1c>
     8f8:	4601                	li	a2,0
     8fa:	bf6d                	j	8b4 <RCC_GetClocksFreq+0x9a>
     8fc:	4605                	li	a2,1
     8fe:	47b5                	li	a5,13
     900:	bfd1                	j	8d4 <RCC_GetClocksFreq+0xba>
     902:	4601                	li	a2,0
     904:	47bd                	li	a5,15
     906:	b7f9                	j	8d4 <RCC_GetClocksFreq+0xba>
     908:	003d1737          	lui	a4,0x3d1
     90c:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3cd328>
     910:	bfe9                	j	8ea <RCC_GetClocksFreq+0xd0>
     912:	400215b7          	lui	a1,0x40021
     916:	55d8                	lw	a4,44(a1)
     918:	00f71693          	slli	a3,a4,0xf
     91c:	55d8                	lw	a4,44(a1)
     91e:	0406df63          	bgez	a3,97c <RCC_GetClocksFreq+0x162>
     922:	8311                	srli	a4,a4,0x4
     924:	8b3d                	andi	a4,a4,15
     926:	00170693          	addi	a3,a4,1
     92a:	007a1737          	lui	a4,0x7a1
     92e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
     932:	02d75733          	divu	a4,a4,a3
     936:	c118                	sw	a4,0(a0)
     938:	55d4                	lw	a3,44(a1)
     93a:	82a1                	srli	a3,a3,0x8
     93c:	8abd                	andi	a3,a3,15
     93e:	e28d                	bnez	a3,960 <RCC_GetClocksFreq+0x146>
     940:	4695                	li	a3,5
     942:	02d70733          	mul	a4,a4,a3
     946:	8305                	srli	a4,a4,0x1
     948:	c118                	sw	a4,0(a0)
     94a:	40021737          	lui	a4,0x40021
     94e:	5758                	lw	a4,44(a4)
     950:	4114                	lw	a3,0(a0)
     952:	8b3d                	andi	a4,a4,15
     954:	0705                	addi	a4,a4,1
     956:	02e6d733          	divu	a4,a3,a4
     95a:	c118                	sw	a4,0(a0)
     95c:	4118                	lw	a4,0(a0)
     95e:	b771                	j	8ea <RCC_GetClocksFreq+0xd0>
     960:	4585                	li	a1,1
     962:	00b69463          	bne	a3,a1,96a <RCC_GetClocksFreq+0x150>
     966:	46e5                	li	a3,25
     968:	bfe9                	j	942 <RCC_GetClocksFreq+0x128>
     96a:	45bd                	li	a1,15
     96c:	00b69663          	bne	a3,a1,978 <RCC_GetClocksFreq+0x15e>
     970:	46d1                	li	a3,20
     972:	02e68733          	mul	a4,a3,a4
     976:	bfc9                	j	948 <RCC_GetClocksFreq+0x12e>
     978:	0689                	addi	a3,a3,2
     97a:	bfe5                	j	972 <RCC_GetClocksFreq+0x158>
     97c:	8b3d                	andi	a4,a4,15
     97e:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     982:	007a1737          	lui	a4,0x7a1
     986:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
     98a:	02d75733          	divu	a4,a4,a3
     98e:	b7f1                	j	95a <RCC_GetClocksFreq+0x140>

00000990 <RCC_APB2PeriphClockCmd>:
     990:	c599                	beqz	a1,99e <RCC_APB2PeriphClockCmd+0xe>
     992:	40021737          	lui	a4,0x40021
     996:	4f1c                	lw	a5,24(a4)
     998:	8d5d                	or	a0,a0,a5
     99a:	cf08                	sw	a0,24(a4)
     99c:	8082                	ret
     99e:	400217b7          	lui	a5,0x40021
     9a2:	4f98                	lw	a4,24(a5)
     9a4:	fff54513          	not	a0,a0
     9a8:	8d79                	and	a0,a0,a4
     9aa:	cf88                	sw	a0,24(a5)
     9ac:	8082                	ret

000009ae <RCC_APB1PeriphClockCmd>:
     9ae:	c599                	beqz	a1,9bc <RCC_APB1PeriphClockCmd+0xe>
     9b0:	40021737          	lui	a4,0x40021
     9b4:	4f5c                	lw	a5,28(a4)
     9b6:	8d5d                	or	a0,a0,a5
     9b8:	cf48                	sw	a0,28(a4)
     9ba:	8082                	ret
     9bc:	400217b7          	lui	a5,0x40021
     9c0:	4fd8                	lw	a4,28(a5)
     9c2:	fff54513          	not	a0,a0
     9c6:	8d79                	and	a0,a0,a4
     9c8:	cfc8                	sw	a0,28(a5)
     9ca:	8082                	ret

000009cc <SPI_Init>:
     9cc:	211a                	lhu	a4,0(a0)
     9ce:	678d                	lui	a5,0x3
     9d0:	04078793          	addi	a5,a5,64 # 3040 <_printf_i+0x26e>
     9d4:	21b6                	lhu	a3,2(a1)
     9d6:	8f7d                	and	a4,a4,a5
     9d8:	219e                	lhu	a5,0(a1)
     9da:	8fd5                	or	a5,a5,a3
     9dc:	21d6                	lhu	a3,4(a1)
     9de:	8fd5                	or	a5,a5,a3
     9e0:	21f6                	lhu	a3,6(a1)
     9e2:	8fd5                	or	a5,a5,a3
     9e4:	2596                	lhu	a3,8(a1)
     9e6:	8fd5                	or	a5,a5,a3
     9e8:	25b6                	lhu	a3,10(a1)
     9ea:	8fd5                	or	a5,a5,a3
     9ec:	25d6                	lhu	a3,12(a1)
     9ee:	8fd5                	or	a5,a5,a3
     9f0:	25f6                	lhu	a3,14(a1)
     9f2:	8fd5                	or	a5,a5,a3
     9f4:	8fd9                	or	a5,a5,a4
     9f6:	a11e                	sh	a5,0(a0)
     9f8:	2d5a                	lhu	a4,28(a0)
     9fa:	77fd                	lui	a5,0xfffff
     9fc:	7ff78793          	addi	a5,a5,2047 # fffff7ff <_eusrstack+0xdfff77ff>
     a00:	8ff9                	and	a5,a5,a4
     a02:	ad5e                	sh	a5,28(a0)
     a04:	299e                	lhu	a5,16(a1)
     a06:	a91e                	sh	a5,16(a0)
     a08:	8082                	ret

00000a0a <SPI_Cmd>:
     a0a:	211e                	lhu	a5,0(a0)
     a0c:	c589                	beqz	a1,a16 <SPI_Cmd+0xc>
     a0e:	0407e793          	ori	a5,a5,64
     a12:	a11e                	sh	a5,0(a0)
     a14:	8082                	ret
     a16:	07c2                	slli	a5,a5,0x10
     a18:	83c1                	srli	a5,a5,0x10
     a1a:	fbf7f793          	andi	a5,a5,-65
     a1e:	07c2                	slli	a5,a5,0x10
     a20:	83c1                	srli	a5,a5,0x10
     a22:	bfc5                	j	a12 <SPI_Cmd+0x8>

00000a24 <SPI_I2S_SendData>:
     a24:	a54e                	sh	a1,12(a0)
     a26:	8082                	ret

00000a28 <SPI_I2S_ReceiveData>:
     a28:	254a                	lhu	a0,12(a0)
     a2a:	8082                	ret

00000a2c <SPI_I2S_GetFlagStatus>:
     a2c:	250a                	lhu	a0,8(a0)
     a2e:	8d6d                	and	a0,a0,a1
     a30:	00a03533          	snez	a0,a0
     a34:	8082                	ret

00000a36 <TIM_TimeBaseInit>:
     a36:	211e                	lhu	a5,0(a0)
     a38:	40013737          	lui	a4,0x40013
     a3c:	c0070693          	addi	a3,a4,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     a40:	07c2                	slli	a5,a5,0x10
     a42:	83c1                	srli	a5,a5,0x10
     a44:	04d50063          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a48:	400006b7          	lui	a3,0x40000
     a4c:	02d50c63          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a50:	40068693          	addi	a3,a3,1024 # 40000400 <_eusrstack+0x1fff8400>
     a54:	02d50863          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a58:	400016b7          	lui	a3,0x40001
     a5c:	80068613          	addi	a2,a3,-2048 # 40000800 <_eusrstack+0x1fff8800>
     a60:	02c50263          	beq	a0,a2,a84 <TIM_TimeBaseInit+0x4e>
     a64:	c0068693          	addi	a3,a3,-1024
     a68:	00d50e63          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a6c:	40070713          	addi	a4,a4,1024
     a70:	00e50a63          	beq	a0,a4,a84 <TIM_TimeBaseInit+0x4e>
     a74:	40015737          	lui	a4,0x40015
     a78:	c0070693          	addi	a3,a4,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     a7c:	00d50463          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a80:	00e51663          	bne	a0,a4,a8c <TIM_TimeBaseInit+0x56>
     a84:	21ba                	lhu	a4,2(a1)
     a86:	f8f7f793          	andi	a5,a5,-113
     a8a:	8fd9                	or	a5,a5,a4
     a8c:	40001737          	lui	a4,0x40001
     a90:	00e50c63          	beq	a0,a4,aa8 <TIM_TimeBaseInit+0x72>
     a94:	40070713          	addi	a4,a4,1024 # 40001400 <_eusrstack+0x1fff9400>
     a98:	00e50863          	beq	a0,a4,aa8 <TIM_TimeBaseInit+0x72>
     a9c:	cff7f793          	andi	a5,a5,-769
     aa0:	21fa                	lhu	a4,6(a1)
     aa2:	07c2                	slli	a5,a5,0x10
     aa4:	83c1                	srli	a5,a5,0x10
     aa6:	8fd9                	or	a5,a5,a4
     aa8:	a11e                	sh	a5,0(a0)
     aaa:	21de                	lhu	a5,4(a1)
     aac:	b55e                	sh	a5,44(a0)
     aae:	219e                	lhu	a5,0(a1)
     ab0:	b51e                	sh	a5,40(a0)
     ab2:	400137b7          	lui	a5,0x40013
     ab6:	c0078713          	addi	a4,a5,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     aba:	00e50e63          	beq	a0,a4,ad6 <TIM_TimeBaseInit+0xa0>
     abe:	40078793          	addi	a5,a5,1024
     ac2:	00f50a63          	beq	a0,a5,ad6 <TIM_TimeBaseInit+0xa0>
     ac6:	400157b7          	lui	a5,0x40015
     aca:	c0078713          	addi	a4,a5,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     ace:	00e50463          	beq	a0,a4,ad6 <TIM_TimeBaseInit+0xa0>
     ad2:	00f51463          	bne	a0,a5,ada <TIM_TimeBaseInit+0xa4>
     ad6:	259c                	lbu	a5,8(a1)
     ad8:	b91e                	sh	a5,48(a0)
     ada:	4785                	li	a5,1
     adc:	a95e                	sh	a5,20(a0)
     ade:	8082                	ret

00000ae0 <TIM_OC1Init>:
     ae0:	311e                	lhu	a5,32(a0)
     ae2:	2192                	lhu	a2,0(a1)
     ae4:	0025d803          	lhu	a6,2(a1) # 40021002 <_eusrstack+0x20019002>
     ae8:	07c2                	slli	a5,a5,0x10
     aea:	83c1                	srli	a5,a5,0x10
     aec:	9bf9                	andi	a5,a5,-2
     aee:	07c2                	slli	a5,a5,0x10
     af0:	83c1                	srli	a5,a5,0x10
     af2:	b11e                	sh	a5,32(a0)
     af4:	311e                	lhu	a5,32(a0)
     af6:	2156                	lhu	a3,4(a0)
     af8:	2d1a                	lhu	a4,24(a0)
     afa:	07c2                	slli	a5,a5,0x10
     afc:	83c1                	srli	a5,a5,0x10
     afe:	0742                	slli	a4,a4,0x10
     b00:	8341                	srli	a4,a4,0x10
     b02:	f8c77713          	andi	a4,a4,-116
     b06:	8f51                	or	a4,a4,a2
     b08:	2592                	lhu	a2,8(a1)
     b0a:	9bf5                	andi	a5,a5,-3
     b0c:	06c2                	slli	a3,a3,0x10
     b0e:	01066633          	or	a2,a2,a6
     b12:	8fd1                	or	a5,a5,a2
     b14:	40013637          	lui	a2,0x40013
     b18:	c0060813          	addi	a6,a2,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     b1c:	82c1                	srli	a3,a3,0x10
     b1e:	01050e63          	beq	a0,a6,b3a <TIM_OC1Init+0x5a>
     b22:	40060613          	addi	a2,a2,1024
     b26:	00c50a63          	beq	a0,a2,b3a <TIM_OC1Init+0x5a>
     b2a:	40015637          	lui	a2,0x40015
     b2e:	c0060813          	addi	a6,a2,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     b32:	01050463          	beq	a0,a6,b3a <TIM_OC1Init+0x5a>
     b36:	02c51063          	bne	a0,a2,b56 <TIM_OC1Init+0x76>
     b3a:	25b2                	lhu	a2,10(a1)
     b3c:	9bdd                	andi	a5,a5,-9
     b3e:	00e5d803          	lhu	a6,14(a1)
     b42:	8fd1                	or	a5,a5,a2
     b44:	21d2                	lhu	a2,4(a1)
     b46:	9bed                	andi	a5,a5,-5
     b48:	cff6f693          	andi	a3,a3,-769
     b4c:	8fd1                	or	a5,a5,a2
     b4e:	25d2                	lhu	a2,12(a1)
     b50:	01066633          	or	a2,a2,a6
     b54:	8ed1                	or	a3,a3,a2
     b56:	a156                	sh	a3,4(a0)
     b58:	ad1a                	sh	a4,24(a0)
     b5a:	21fa                	lhu	a4,6(a1)
     b5c:	b95a                	sh	a4,52(a0)
     b5e:	b11e                	sh	a5,32(a0)
     b60:	8082                	ret

00000b62 <TIM_Cmd>:
     b62:	211e                	lhu	a5,0(a0)
     b64:	c589                	beqz	a1,b6e <TIM_Cmd+0xc>
     b66:	0017e793          	ori	a5,a5,1
     b6a:	a11e                	sh	a5,0(a0)
     b6c:	8082                	ret
     b6e:	07c2                	slli	a5,a5,0x10
     b70:	83c1                	srli	a5,a5,0x10
     b72:	9bf9                	andi	a5,a5,-2
     b74:	07c2                	slli	a5,a5,0x10
     b76:	83c1                	srli	a5,a5,0x10
     b78:	bfcd                	j	b6a <TIM_Cmd+0x8>

00000b7a <TIM_ITConfig>:
     b7a:	255e                	lhu	a5,12(a0)
     b7c:	c601                	beqz	a2,b84 <TIM_ITConfig+0xa>
     b7e:	8ddd                	or	a1,a1,a5
     b80:	a54e                	sh	a1,12(a0)
     b82:	8082                	ret
     b84:	fff5c593          	not	a1,a1
     b88:	8dfd                	and	a1,a1,a5
     b8a:	bfdd                	j	b80 <TIM_ITConfig+0x6>

00000b8c <TIM_ARRPreloadConfig>:
     b8c:	211e                	lhu	a5,0(a0)
     b8e:	c589                	beqz	a1,b98 <TIM_ARRPreloadConfig+0xc>
     b90:	0807e793          	ori	a5,a5,128
     b94:	a11e                	sh	a5,0(a0)
     b96:	8082                	ret
     b98:	07c2                	slli	a5,a5,0x10
     b9a:	83c1                	srli	a5,a5,0x10
     b9c:	f7f7f793          	andi	a5,a5,-129
     ba0:	07c2                	slli	a5,a5,0x10
     ba2:	83c1                	srli	a5,a5,0x10
     ba4:	bfc5                	j	b94 <TIM_ARRPreloadConfig+0x8>

00000ba6 <TIM_OC1PreloadConfig>:
     ba6:	2d1e                	lhu	a5,24(a0)
     ba8:	07c2                	slli	a5,a5,0x10
     baa:	83c1                	srli	a5,a5,0x10
     bac:	9bdd                	andi	a5,a5,-9
     bae:	8ddd                	or	a1,a1,a5
     bb0:	ad0e                	sh	a1,24(a0)
     bb2:	8082                	ret

00000bb4 <TIM_SetCompare1>:
     bb4:	b94e                	sh	a1,52(a0)
     bb6:	8082                	ret

00000bb8 <TIM_GetITStatus>:
     bb8:	291e                	lhu	a5,16(a0)
     bba:	254a                	lhu	a0,12(a0)
     bbc:	8fed                	and	a5,a5,a1
     bbe:	0542                	slli	a0,a0,0x10
     bc0:	8141                	srli	a0,a0,0x10
     bc2:	c789                	beqz	a5,bcc <TIM_GetITStatus+0x14>
     bc4:	8d6d                	and	a0,a0,a1
     bc6:	00a03533          	snez	a0,a0
     bca:	8082                	ret
     bcc:	4501                	li	a0,0
     bce:	8082                	ret

00000bd0 <TIM_ClearITPendingBit>:
     bd0:	fff5c593          	not	a1,a1
     bd4:	05c2                	slli	a1,a1,0x10
     bd6:	81c1                	srli	a1,a1,0x10
     bd8:	a90e                	sh	a1,16(a0)
     bda:	8082                	ret

00000bdc <USART_Init>:
     bdc:	e14ff2ef          	jal	t0,1f0 <__riscv_save_0>
     be0:	2916                	lhu	a3,16(a0)
     be2:	77f5                	lui	a5,0xffffd
     be4:	17fd                	addi	a5,a5,-1
     be6:	8ff5                	and	a5,a5,a3
     be8:	21f6                	lhu	a3,6(a1)
     bea:	25da                	lhu	a4,12(a1)
     bec:	7179                	addi	sp,sp,-48
     bee:	8fd5                	or	a5,a5,a3
     bf0:	a91e                	sh	a5,16(a0)
     bf2:	2556                	lhu	a3,12(a0)
     bf4:	77fd                	lui	a5,0xfffff
     bf6:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <_eusrstack+0xdfff69f3>
     bfa:	8ff5                	and	a5,a5,a3
     bfc:	21d6                	lhu	a3,4(a1)
     bfe:	842a                	mv	s0,a0
     c00:	c62e                	sw	a1,12(sp)
     c02:	8fd5                	or	a5,a5,a3
     c04:	2596                	lhu	a3,8(a1)
     c06:	8fd5                	or	a5,a5,a3
     c08:	25b6                	lhu	a3,10(a1)
     c0a:	8fd5                	or	a5,a5,a3
     c0c:	a55e                	sh	a5,12(a0)
     c0e:	295e                	lhu	a5,20(a0)
     c10:	07c2                	slli	a5,a5,0x10
     c12:	83c1                	srli	a5,a5,0x10
     c14:	cff7f793          	andi	a5,a5,-769
     c18:	8fd9                	or	a5,a5,a4
     c1a:	a95e                	sh	a5,20(a0)
     c1c:	0868                	addi	a0,sp,28
     c1e:	3ef5                	jal	81a <RCC_GetClocksFreq>
     c20:	400147b7          	lui	a5,0x40014
     c24:	80078793          	addi	a5,a5,-2048 # 40013800 <_eusrstack+0x2000b800>
     c28:	45b2                	lw	a1,12(sp)
     c2a:	02f41e63          	bne	s0,a5,c66 <USART_Init+0x8a>
     c2e:	57a2                	lw	a5,40(sp)
     c30:	4765                	li	a4,25
     c32:	02e787b3          	mul	a5,a5,a4
     c36:	4198                	lw	a4,0(a1)
     c38:	06400693          	li	a3,100
     c3c:	070a                	slli	a4,a4,0x2
     c3e:	02e7d7b3          	divu	a5,a5,a4
     c42:	02d7d733          	divu	a4,a5,a3
     c46:	02d7f7b3          	remu	a5,a5,a3
     c4a:	0712                	slli	a4,a4,0x4
     c4c:	0792                	slli	a5,a5,0x4
     c4e:	03278793          	addi	a5,a5,50
     c52:	02d7d7b3          	divu	a5,a5,a3
     c56:	8bbd                	andi	a5,a5,15
     c58:	8fd9                	or	a5,a5,a4
     c5a:	07c2                	slli	a5,a5,0x10
     c5c:	83c1                	srli	a5,a5,0x10
     c5e:	a41e                	sh	a5,8(s0)
     c60:	6145                	addi	sp,sp,48
     c62:	db2ff06f          	j	214 <__riscv_restore_0>
     c66:	5792                	lw	a5,36(sp)
     c68:	b7e1                	j	c30 <USART_Init+0x54>

00000c6a <USART_Cmd>:
     c6a:	c591                	beqz	a1,c76 <USART_Cmd+0xc>
     c6c:	255e                	lhu	a5,12(a0)
     c6e:	6709                	lui	a4,0x2
     c70:	8fd9                	or	a5,a5,a4
     c72:	a55e                	sh	a5,12(a0)
     c74:	8082                	ret
     c76:	255a                	lhu	a4,12(a0)
     c78:	77f9                	lui	a5,0xffffe
     c7a:	17fd                	addi	a5,a5,-1
     c7c:	8ff9                	and	a5,a5,a4
     c7e:	bfd5                	j	c72 <USART_Cmd+0x8>

00000c80 <USART_SendData>:
     c80:	1ff5f593          	andi	a1,a1,511
     c84:	a14e                	sh	a1,4(a0)
     c86:	8082                	ret

00000c88 <USART_GetFlagStatus>:
     c88:	210a                	lhu	a0,0(a0)
     c8a:	8d6d                	and	a0,a0,a1
     c8c:	00a03533          	snez	a0,a0
     c90:	8082                	ret

00000c92 <Delay_us>:
     c92:	82a1c783          	lbu	a5,-2006(gp) # 200000d2 <fac_us>
     c96:	e000f637          	lui	a2,0xe000f
     c9a:	02a78533          	mul	a0,a5,a0
     c9e:	e000f7b7          	lui	a5,0xe000f
     ca2:	0087a803          	lw	a6,8(a5) # e000f008 <_eusrstack+0xc0007008>
     ca6:	00c7a883          	lw	a7,12(a5)
     caa:	00862303          	lw	t1,8(a2) # e000f008 <_eusrstack+0xc0007008>
     cae:	00c62383          	lw	t2,12(a2)
     cb2:	410306b3          	sub	a3,t1,a6
     cb6:	00d33733          	sltu	a4,t1,a3
     cba:	00771463          	bne	a4,t2,cc2 <Delay_us+0x30>
     cbe:	fea6e6e3          	bltu	a3,a0,caa <Delay_us+0x18>
     cc2:	8082                	ret

00000cc4 <Delay_ms>:
     cc4:	8281d783          	lhu	a5,-2008(gp) # 200000d0 <fac_ms>
     cc8:	e000f337          	lui	t1,0xe000f
     ccc:	02a78533          	mul	a0,a5,a0
     cd0:	e000f7b7          	lui	a5,0xe000f
     cd4:	0087a803          	lw	a6,8(a5) # e000f008 <_eusrstack+0xc0007008>
     cd8:	00c7a883          	lw	a7,12(a5)
     cdc:	41f55593          	srai	a1,a0,0x1f
     ce0:	00832603          	lw	a2,8(t1) # e000f008 <_eusrstack+0xc0007008>
     ce4:	00c32683          	lw	a3,12(t1)
     ce8:	41060733          	sub	a4,a2,a6
     cec:	00e637b3          	sltu	a5,a2,a4
     cf0:	40f687b3          	sub	a5,a3,a5
     cf4:	feb7e6e3          	bltu	a5,a1,ce0 <Delay_ms+0x1c>
     cf8:	00f59463          	bne	a1,a5,d00 <Delay_ms+0x3c>
     cfc:	fea762e3          	bltu	a4,a0,ce0 <Delay_ms+0x1c>
     d00:	8082                	ret

00000d02 <BrewingControl_Init>:
     d02:	84818793          	addi	a5,gp,-1976 # 200000f0 <g_brewing_ctrl>
     d06:	00000713          	li	a4,0
     d0a:	0007a023          	sw	zero,0(a5)
     d0e:	0007a223          	sw	zero,4(a5)
     d12:	0007a423          	sw	zero,8(a5)
     d16:	0007a623          	sw	zero,12(a5)
     d1a:	cb98                	sw	a4,16(a5)
     d1c:	00079a23          	sh	zero,20(a5)
     d20:	8082                	ret

00000d22 <BrewingControl_Start>:
     d22:	84818793          	addi	a5,gp,-1976 # 200000f0 <g_brewing_ctrl>
     d26:	4398                	lw	a4,0(a5)
     d28:	ef09                	bnez	a4,d42 <BrewingControl_Start+0x20>
     d2a:	cc6ff2ef          	jal	t0,1f0 <__riscv_save_0>
     d2e:	00003537          	lui	a0,0x3
     d32:	4705                	li	a4,1
     d34:	33050513          	addi	a0,a0,816 # 3330 <_read+0x9c>
     d38:	c398                	sw	a4,0(a5)
     d3a:	4ae010ef          	jal	ra,21e8 <puts>
     d3e:	cd6ff06f          	j	214 <__riscv_restore_0>
     d42:	8082                	ret

00000d44 <BrewingControl_KeyHandler>:
     d44:	cacff2ef          	jal	t0,1f0 <__riscv_save_0>
     d48:	84818413          	addi	s0,gp,-1976 # 200000f0 <g_brewing_ctrl>
     d4c:	401c                	lw	a5,0(s0)
     d4e:	470d                	li	a4,3
     d50:	04e78e63          	beq	a5,a4,dac <BrewingControl_KeyHandler+0x68>
     d54:	00f76763          	bltu	a4,a5,d62 <BrewingControl_KeyHandler+0x1e>
     d58:	4705                	li	a4,1
     d5a:	02e78763          	beq	a5,a4,d88 <BrewingControl_KeyHandler+0x44>
     d5e:	cb6ff06f          	j	214 <__riscv_restore_0>
     d62:	4695                	li	a3,5
     d64:	06d78c63          	beq	a5,a3,ddc <BrewingControl_KeyHandler+0x98>
     d68:	4719                	li	a4,6
     d6a:	fee79ae3          	bne	a5,a4,d5e <BrewingControl_KeyHandler+0x1a>
     d6e:	4791                	li	a5,4
     d70:	fef517e3          	bne	a0,a5,d5e <BrewingControl_KeyHandler+0x1a>
     d74:	479d                	li	a5,7
     d76:	4501                	li	a0,0
     d78:	c01c                	sw	a5,0(s0)
     d7a:	3eb000ef          	jal	ra,1964 <stir>
     d7e:	00003537          	lui	a0,0x3
     d82:	32450513          	addi	a0,a0,804 # 3324 <_read+0x90>
     d86:	a005                	j	da6 <BrewingControl_KeyHandler+0x62>
     d88:	fcf51be3          	bne	a0,a5,d5e <BrewingControl_KeyHandler+0x1a>
     d8c:	4789                	li	a5,2
     d8e:	c01c                	sw	a5,0(s0)
     d90:	8201a783          	lw	a5,-2016(gp) # 200000c8 <uwtick>
     d94:	4501                	li	a0,0
     d96:	4585                	li	a1,1
     d98:	c05c                	sw	a5,4(s0)
     d9a:	4c9000ef          	jal	ra,1a62 <WaterPump_Control>
     d9e:	00003537          	lui	a0,0x3
     da2:	2e450513          	addi	a0,a0,740 # 32e4 <_read+0x50>
     da6:	442010ef          	jal	ra,21e8 <puts>
     daa:	bf55                	j	d5e <BrewingControl_KeyHandler+0x1a>
     dac:	4789                	li	a5,2
     dae:	faf518e3          	bne	a0,a5,d5e <BrewingControl_KeyHandler+0x1a>
     db2:	4791                	li	a5,4
     db4:	83818513          	addi	a0,gp,-1992 # 200000e0 <ds18b20>
     db8:	c01c                	sw	a5,0(s0)
     dba:	2e01                	jal	10ca <DS18B20_ReadRealtimeTemp>
     dbc:	8381a783          	lw	a5,-1992(gp) # 200000e0 <ds18b20>
     dc0:	c81c                	sw	a5,16(s0)
     dc2:	2e81                	jal	1112 <Heater_Start>
     dc4:	4808                	lw	a0,16(s0)
     dc6:	1f4010ef          	jal	ra,1fba <__extendsfdf2>
     dca:	862a                	mv	a2,a0
     dcc:	00003537          	lui	a0,0x3
     dd0:	86ae                	mv	a3,a1
     dd2:	2f850513          	addi	a0,a0,760 # 32f8 <_read+0x64>
     dd6:	2fe010ef          	jal	ra,20d4 <iprintf>
     dda:	b751                	j	d5e <BrewingControl_KeyHandler+0x1a>
     ddc:	f8e511e3          	bne	a0,a4,d5e <BrewingControl_KeyHandler+0x1a>
     de0:	4799                	li	a5,6
     de2:	4505                	li	a0,1
     de4:	06400593          	li	a1,100
     de8:	c01c                	sw	a5,0(s0)
     dea:	31d000ef          	jal	ra,1906 <stir_360>
     dee:	00003537          	lui	a0,0x3
     df2:	31850513          	addi	a0,a0,792 # 3318 <_read+0x84>
     df6:	bf45                	j	da6 <BrewingControl_KeyHandler+0x62>

00000df8 <BrewingControl_Task>:
     df8:	bf8ff2ef          	jal	t0,1f0 <__riscv_save_0>
     dfc:	8201a483          	lw	s1,-2016(gp) # 200000c8 <uwtick>
     e00:	8481a783          	lw	a5,-1976(gp) # 200000f0 <g_brewing_ctrl>
     e04:	471d                	li	a4,7
     e06:	17f9                	addi	a5,a5,-2
     e08:	04f76063          	bltu	a4,a5,e48 <BrewingControl_Task+0x50>
     e0c:	670d                	lui	a4,0x3
     e0e:	078a                	slli	a5,a5,0x2
     e10:	2c470713          	addi	a4,a4,708 # 32c4 <_read+0x30>
     e14:	97ba                	add	a5,a5,a4
     e16:	439c                	lw	a5,0(a5)
     e18:	84818413          	addi	s0,gp,-1976 # 200000f0 <g_brewing_ctrl>
     e1c:	8782                	jr	a5
     e1e:	651d                	lui	a0,0x7
     e20:	53050513          	addi	a0,a0,1328 # 7530 <_data_lma+0x3f58>
     e24:	3a3000ef          	jal	ra,19c6 <delay_ms>
     e28:	405c                	lw	a5,4(s0)
     e2a:	4581                	li	a1,0
     e2c:	4501                	li	a0,0
     e2e:	8c9d                	sub	s1,s1,a5
     e30:	c404                	sw	s1,8(s0)
     e32:	431000ef          	jal	ra,1a62 <WaterPump_Control>
     e36:	440c                	lw	a1,8(s0)
     e38:	00003537          	lui	a0,0x3
     e3c:	478d                	li	a5,3
     e3e:	35050513          	addi	a0,a0,848 # 3350 <_read+0xbc>
     e42:	c01c                	sw	a5,0(s0)
     e44:	290010ef          	jal	ra,20d4 <iprintf>
     e48:	bccff06f          	j	214 <__riscv_restore_0>
     e4c:	651d                	lui	a0,0x7
     e4e:	53050513          	addi	a0,a0,1328 # 7530 <_data_lma+0x3f58>
     e52:	3d8d                	jal	cc4 <Delay_ms>
     e54:	2cd1                	jal	1128 <Heater_Stop>
     e56:	4795                	li	a5,5
     e58:	00003537          	lui	a0,0x3
     e5c:	c01c                	sw	a5,0(s0)
     e5e:	37c50513          	addi	a0,a0,892 # 337c <_read+0xe8>
     e62:	386010ef          	jal	ra,21e8 <puts>
     e66:	b7cd                	j	e48 <BrewingControl_Task+0x50>
     e68:	47a1                	li	a5,8
     e6a:	00003537          	lui	a0,0x3
     e6e:	c01c                	sw	a5,0(s0)
     e70:	c444                	sw	s1,12(s0)
     e72:	3a850513          	addi	a0,a0,936 # 33a8 <_read+0x114>
     e76:	b7f5                	j	e62 <BrewingControl_Task+0x6a>
     e78:	445c                	lw	a5,12(s0)
     e7a:	8c9d                	sub	s1,s1,a5
     e7c:	6789                	lui	a5,0x2
     e7e:	70f78793          	addi	a5,a5,1807 # 270f <_fwalk_reent+0xb>
     e82:	fc97f3e3          	bgeu	a5,s1,e48 <BrewingControl_Task+0x50>
     e86:	47a5                	li	a5,9
     e88:	4505                	li	a0,1
     e8a:	4585                	li	a1,1
     e8c:	c01c                	sw	a5,0(s0)
     e8e:	3d5000ef          	jal	ra,1a62 <WaterPump_Control>
     e92:	00003537          	lui	a0,0x3
     e96:	3b850513          	addi	a0,a0,952 # 33b8 <_read+0x124>
     e9a:	b7e1                	j	e62 <BrewingControl_Task+0x6a>
     e9c:	4585                	li	a1,1
     e9e:	4505                	li	a0,1
     ea0:	3c3000ef          	jal	ra,1a62 <WaterPump_Control>
     ea4:	77f9                	lui	a5,0xffffe
     ea6:	8f078793          	addi	a5,a5,-1808 # ffffd8f0 <_eusrstack+0xdfff58f0>
     eaa:	94be                	add	s1,s1,a5
     eac:	445c                	lw	a5,12(s0)
     eae:	8c9d                	sub	s1,s1,a5
     eb0:	441c                	lw	a5,8(s0)
     eb2:	f8f4ebe3          	bltu	s1,a5,e48 <BrewingControl_Task+0x50>
     eb6:	4505                	li	a0,1
     eb8:	4581                	li	a1,0
     eba:	3a9000ef          	jal	ra,1a62 <WaterPump_Control>
     ebe:	47a9                	li	a5,10
     ec0:	00003537          	lui	a0,0x3
     ec4:	c01c                	sw	a5,0(s0)
     ec6:	3d850513          	addi	a0,a0,984 # 33d8 <_read+0x144>
     eca:	bf61                	j	e62 <BrewingControl_Task+0x6a>

00000ecc <DS18B20_IO_OUT>:
     ecc:	b24ff2ef          	jal	t0,1f0 <__riscv_save_0>
     ed0:	1141                	addi	sp,sp,-16
     ed2:	4789                	li	a5,2
     ed4:	827c                	sh	a5,4(sp)
     ed6:	40011537          	lui	a0,0x40011
     eda:	47d1                	li	a5,20
     edc:	c63e                	sw	a5,12(sp)
     ede:	004c                	addi	a1,sp,4
     ee0:	478d                	li	a5,3
     ee2:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
     ee6:	c43e                	sw	a5,8(sp)
     ee8:	80dff0ef          	jal	ra,6f4 <GPIO_Init>
     eec:	0141                	addi	sp,sp,16
     eee:	b26ff06f          	j	214 <__riscv_restore_0>

00000ef2 <DS18B20_IO_IN>:
     ef2:	afeff2ef          	jal	t0,1f0 <__riscv_save_0>
     ef6:	1141                	addi	sp,sp,-16
     ef8:	4789                	li	a5,2
     efa:	40011537          	lui	a0,0x40011
     efe:	827c                	sh	a5,4(sp)
     f00:	004c                	addi	a1,sp,4
     f02:	04800793          	li	a5,72
     f06:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
     f0a:	c63e                	sw	a5,12(sp)
     f0c:	fe8ff0ef          	jal	ra,6f4 <GPIO_Init>
     f10:	0141                	addi	sp,sp,16
     f12:	b02ff06f          	j	214 <__riscv_restore_0>

00000f16 <DS18B20_Reset>:
     f16:	adaff2ef          	jal	t0,1f0 <__riscv_save_0>
     f1a:	3f4d                	jal	ecc <DS18B20_IO_OUT>
     f1c:	40011437          	lui	s0,0x40011
     f20:	4589                	li	a1,2
     f22:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
     f26:	89dff0ef          	jal	ra,7c2 <GPIO_ResetBits>
     f2a:	1e000513          	li	a0,480
     f2e:	3395                	jal	c92 <Delay_us>
     f30:	4589                	li	a1,2
     f32:	80040513          	addi	a0,s0,-2048
     f36:	889ff0ef          	jal	ra,7be <GPIO_SetBits>
     f3a:	04600513          	li	a0,70
     f3e:	3b91                	jal	c92 <Delay_us>
     f40:	3f4d                	jal	ef2 <DS18B20_IO_IN>
     f42:	ad2ff06f          	j	214 <__riscv_restore_0>

00000f46 <DS18B20_Check>:
     f46:	aaaff2ef          	jal	t0,1f0 <__riscv_save_0>
     f4a:	400114b7          	lui	s1,0x40011
     f4e:	06500413          	li	s0,101
     f52:	80048493          	addi	s1,s1,-2048 # 40010800 <_eusrstack+0x20008800>
     f56:	4589                	li	a1,2
     f58:	8526                	mv	a0,s1
     f5a:	85bff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
     f5e:	ed11                	bnez	a0,f7a <DS18B20_Check+0x34>
     f60:	400114b7          	lui	s1,0x40011
     f64:	0f100413          	li	s0,241
     f68:	80048493          	addi	s1,s1,-2048 # 40010800 <_eusrstack+0x20008800>
     f6c:	4589                	li	a1,2
     f6e:	8526                	mv	a0,s1
     f70:	845ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
     f74:	cd01                	beqz	a0,f8c <DS18B20_Check+0x46>
     f76:	4505                	li	a0,1
     f78:	a801                	j	f88 <DS18B20_Check+0x42>
     f7a:	147d                	addi	s0,s0,-1
     f7c:	4505                	li	a0,1
     f7e:	0ff47413          	andi	s0,s0,255
     f82:	3b01                	jal	c92 <Delay_us>
     f84:	f869                	bnez	s0,f56 <DS18B20_Check+0x10>
     f86:	4501                	li	a0,0
     f88:	a8cff06f          	j	214 <__riscv_restore_0>
     f8c:	147d                	addi	s0,s0,-1
     f8e:	4505                	li	a0,1
     f90:	0ff47413          	andi	s0,s0,255
     f94:	39fd                	jal	c92 <Delay_us>
     f96:	f879                	bnez	s0,f6c <DS18B20_Check+0x26>
     f98:	b7fd                	j	f86 <DS18B20_Check+0x40>

00000f9a <DS18B20_WriteBit>:
     f9a:	a56ff2ef          	jal	t0,1f0 <__riscv_save_0>
     f9e:	84aa                	mv	s1,a0
     fa0:	40011437          	lui	s0,0x40011
     fa4:	3725                	jal	ecc <DS18B20_IO_OUT>
     fa6:	4589                	li	a1,2
     fa8:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
     fac:	817ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
     fb0:	cc89                	beqz	s1,fca <DS18B20_WriteBit+0x30>
     fb2:	4529                	li	a0,10
     fb4:	39f9                	jal	c92 <Delay_us>
     fb6:	80040513          	addi	a0,s0,-2048
     fba:	4589                	li	a1,2
     fbc:	803ff0ef          	jal	ra,7be <GPIO_SetBits>
     fc0:	03700513          	li	a0,55
     fc4:	31f9                	jal	c92 <Delay_us>
     fc6:	a4eff06f          	j	214 <__riscv_restore_0>
     fca:	04100513          	li	a0,65
     fce:	31d1                	jal	c92 <Delay_us>
     fd0:	80040513          	addi	a0,s0,-2048
     fd4:	4589                	li	a1,2
     fd6:	fe8ff0ef          	jal	ra,7be <GPIO_SetBits>
     fda:	4515                	li	a0,5
     fdc:	b7e5                	j	fc4 <DS18B20_WriteBit+0x2a>

00000fde <DS18B20_ReadBit>:
     fde:	a12ff2ef          	jal	t0,1f0 <__riscv_save_0>
     fe2:	35ed                	jal	ecc <DS18B20_IO_OUT>
     fe4:	40011437          	lui	s0,0x40011
     fe8:	4589                	li	a1,2
     fea:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
     fee:	fd4ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
     ff2:	450d                	li	a0,3
     ff4:	3979                	jal	c92 <Delay_us>
     ff6:	4589                	li	a1,2
     ff8:	80040513          	addi	a0,s0,-2048
     ffc:	fc2ff0ef          	jal	ra,7be <GPIO_SetBits>
    1000:	3dcd                	jal	ef2 <DS18B20_IO_IN>
    1002:	4529                	li	a0,10
    1004:	3179                	jal	c92 <Delay_us>
    1006:	4589                	li	a1,2
    1008:	80040513          	addi	a0,s0,-2048
    100c:	fa8ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1010:	842a                	mv	s0,a0
    1012:	03500513          	li	a0,53
    1016:	39b5                	jal	c92 <Delay_us>
    1018:	8522                	mv	a0,s0
    101a:	9faff06f          	j	214 <__riscv_restore_0>

0000101e <DS18B20_WriteByte>:
    101e:	9d2ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1022:	84aa                	mv	s1,a0
    1024:	4421                	li	s0,8
    1026:	147d                	addi	s0,s0,-1
    1028:	0014f513          	andi	a0,s1,1
    102c:	0ff47413          	andi	s0,s0,255
    1030:	37ad                	jal	f9a <DS18B20_WriteBit>
    1032:	8085                	srli	s1,s1,0x1
    1034:	f86d                	bnez	s0,1026 <DS18B20_WriteByte+0x8>
    1036:	9deff06f          	j	214 <__riscv_restore_0>

0000103a <DS18B20_ReadByte>:
    103a:	9b6ff2ef          	jal	t0,1f0 <__riscv_save_0>
    103e:	44a1                	li	s1,8
    1040:	4401                	li	s0,0
    1042:	8005                	srli	s0,s0,0x1
    1044:	3f69                	jal	fde <DS18B20_ReadBit>
    1046:	c509                	beqz	a0,1050 <DS18B20_ReadByte+0x16>
    1048:	f8046413          	ori	s0,s0,-128
    104c:	0ff47413          	andi	s0,s0,255
    1050:	14fd                	addi	s1,s1,-1
    1052:	0ff4f493          	andi	s1,s1,255
    1056:	f4f5                	bnez	s1,1042 <DS18B20_ReadByte+0x8>
    1058:	8522                	mv	a0,s0
    105a:	9baff06f          	j	214 <__riscv_restore_0>

0000105e <DS18B20_StartConvert>:
    105e:	992ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1062:	3d55                	jal	f16 <DS18B20_Reset>
    1064:	35cd                	jal	f46 <DS18B20_Check>
    1066:	4785                	li	a5,1
    1068:	c901                	beqz	a0,1078 <DS18B20_StartConvert+0x1a>
    106a:	0cc00513          	li	a0,204
    106e:	3f45                	jal	101e <DS18B20_WriteByte>
    1070:	04400513          	li	a0,68
    1074:	376d                	jal	101e <DS18B20_WriteByte>
    1076:	4781                	li	a5,0
    1078:	853e                	mv	a0,a5
    107a:	99aff06f          	j	214 <__riscv_restore_0>

0000107e <DS18B20_ReadTempRaw>:
    107e:	972ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1082:	3ff1                	jal	105e <DS18B20_StartConvert>
    1084:	c519                	beqz	a0,1092 <DS18B20_ReadTempRaw+0x14>
    1086:	000037b7          	lui	a5,0x3
    108a:	3f47a503          	lw	a0,1012(a5) # 33f4 <_read+0x160>
    108e:	986ff06f          	j	214 <__riscv_restore_0>
    1092:	2ee00513          	li	a0,750
    1096:	313d                	jal	cc4 <Delay_ms>
    1098:	3dbd                	jal	f16 <DS18B20_Reset>
    109a:	3575                	jal	f46 <DS18B20_Check>
    109c:	d56d                	beqz	a0,1086 <DS18B20_ReadTempRaw+0x8>
    109e:	0cc00513          	li	a0,204
    10a2:	3fb5                	jal	101e <DS18B20_WriteByte>
    10a4:	0be00513          	li	a0,190
    10a8:	3f9d                	jal	101e <DS18B20_WriteByte>
    10aa:	3f41                	jal	103a <DS18B20_ReadByte>
    10ac:	842a                	mv	s0,a0
    10ae:	3771                	jal	103a <DS18B20_ReadByte>
    10b0:	0522                	slli	a0,a0,0x8
    10b2:	8d41                	or	a0,a0,s0
    10b4:	0542                	slli	a0,a0,0x10
    10b6:	8541                	srai	a0,a0,0x10
    10b8:	63d000ef          	jal	ra,1ef4 <__floatsisf>
    10bc:	000037b7          	lui	a5,0x3
    10c0:	3f87a583          	lw	a1,1016(a5) # 33f8 <_read+0x164>
    10c4:	3a1000ef          	jal	ra,1c64 <__mulsf3>
    10c8:	b7d9                	j	108e <DS18B20_ReadTempRaw+0x10>

000010ca <DS18B20_ReadRealtimeTemp>:
    10ca:	926ff2ef          	jal	t0,1f0 <__riscv_save_0>
    10ce:	842a                	mv	s0,a0
    10d0:	377d                	jal	107e <DS18B20_ReadTempRaw>
    10d2:	000037b7          	lui	a5,0x3
    10d6:	3f07a583          	lw	a1,1008(a5) # 33f0 <_read+0x15c>
    10da:	84aa                	mv	s1,a0
    10dc:	2fd000ef          	jal	ra,1bd8 <__lesf2>
    10e0:	02a05763          	blez	a0,110e <DS18B20_ReadRealtimeTemp+0x44>
    10e4:	4048                	lw	a0,4(s0)
    10e6:	c004                	sw	s1,0(s0)
    10e8:	85a6                	mv	a1,s1
    10ea:	2ef000ef          	jal	ra,1bd8 <__lesf2>
    10ee:	00a04d63          	bgtz	a0,1108 <DS18B20_ReadRealtimeTemp+0x3e>
    10f2:	241c                	lbu	a5,8(s0)
    10f4:	e789                	bnez	a5,10fe <DS18B20_ReadRealtimeTemp+0x34>
    10f6:	445c                	lw	a5,12(s0)
    10f8:	c399                	beqz	a5,10fe <DS18B20_ReadRealtimeTemp+0x34>
    10fa:	8526                	mv	a0,s1
    10fc:	9782                	jalr	a5
    10fe:	4785                	li	a5,1
    1100:	a41c                	sb	a5,8(s0)
    1102:	4501                	li	a0,0
    1104:	910ff06f          	j	214 <__riscv_restore_0>
    1108:	00040423          	sb	zero,8(s0)
    110c:	bfdd                	j	1102 <DS18B20_ReadRealtimeTemp+0x38>
    110e:	4509                	li	a0,2
    1110:	bfd5                	j	1104 <DS18B20_ReadRealtimeTemp+0x3a>

00001112 <Heater_Start>:
    1112:	8deff2ef          	jal	t0,1f0 <__riscv_save_0>
    1116:	40011537          	lui	a0,0x40011
    111a:	45c1                	li	a1,16
    111c:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1120:	ea2ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1124:	8f0ff06f          	j	214 <__riscv_restore_0>

00001128 <Heater_Stop>:
    1128:	8c8ff2ef          	jal	t0,1f0 <__riscv_save_0>
    112c:	40011537          	lui	a0,0x40011
    1130:	45c1                	li	a1,16
    1132:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1136:	e88ff0ef          	jal	ra,7be <GPIO_SetBits>
    113a:	8daff06f          	j	214 <__riscv_restore_0>

0000113e <Heater_Init>:
    113e:	8b2ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1142:	1141                	addi	sp,sp,-16
    1144:	4585                	li	a1,1
    1146:	4511                	li	a0,4
    1148:	c202                	sw	zero,4(sp)
    114a:	c402                	sw	zero,8(sp)
    114c:	c602                	sw	zero,12(sp)
    114e:	843ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1152:	47c1                	li	a5,16
    1154:	827c                	sh	a5,4(sp)
    1156:	40011537          	lui	a0,0x40011
    115a:	47c1                	li	a5,16
    115c:	004c                	addi	a1,sp,4
    115e:	c63e                	sw	a5,12(sp)
    1160:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1164:	478d                	li	a5,3
    1166:	c43e                	sw	a5,8(sp)
    1168:	d8cff0ef          	jal	ra,6f4 <GPIO_Init>
    116c:	3f75                	jal	1128 <Heater_Stop>
    116e:	0141                	addi	sp,sp,16
    1170:	8a4ff06f          	j	214 <__riscv_restore_0>

00001174 <key_init>:
    1174:	87cff2ef          	jal	t0,1f0 <__riscv_save_0>
    1178:	1141                	addi	sp,sp,-16
    117a:	4585                	li	a1,1
    117c:	02000513          	li	a0,32
    1180:	811ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1184:	6485                	lui	s1,0x1
    1186:	40011537          	lui	a0,0x40011
    118a:	440d                	li	s0,3
    118c:	a0048793          	addi	a5,s1,-1536 # a00 <SPI_Init+0x34>
    1190:	4941                	li	s2,16
    1192:	004c                	addi	a1,sp,4
    1194:	40050513          	addi	a0,a0,1024 # 40011400 <_eusrstack+0x20009400>
    1198:	827c                	sh	a5,4(sp)
    119a:	c64a                	sw	s2,12(sp)
    119c:	c422                	sw	s0,8(sp)
    119e:	d56ff0ef          	jal	ra,6f4 <GPIO_Init>
    11a2:	4585                	li	a1,1
    11a4:	04000513          	li	a0,64
    11a8:	fe8ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    11ac:	c64a                	sw	s2,12(sp)
    11ae:	40012937          	lui	s2,0x40012
    11b2:	77e9                	lui	a5,0xffffa
    11b4:	004c                	addi	a1,sp,4
    11b6:	80090513          	addi	a0,s2,-2048 # 40011800 <_eusrstack+0x20009800>
    11ba:	827c                	sh	a5,4(sp)
    11bc:	c422                	sw	s0,8(sp)
    11be:	d36ff0ef          	jal	ra,6f4 <GPIO_Init>
    11c2:	4585                	li	a1,1
    11c4:	04000513          	li	a0,64
    11c8:	fc8ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    11cc:	a8048493          	addi	s1,s1,-1408
    11d0:	8264                	sh	s1,4(sp)
    11d2:	004c                	addi	a1,sp,4
    11d4:	04800493          	li	s1,72
    11d8:	80090513          	addi	a0,s2,-2048
    11dc:	c626                	sw	s1,12(sp)
    11de:	c422                	sw	s0,8(sp)
    11e0:	d14ff0ef          	jal	ra,6f4 <GPIO_Init>
    11e4:	4585                	li	a1,1
    11e6:	4541                	li	a0,16
    11e8:	fa8ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    11ec:	02000793          	li	a5,32
    11f0:	004c                	addi	a1,sp,4
    11f2:	40011537          	lui	a0,0x40011
    11f6:	827c                	sh	a5,4(sp)
    11f8:	c626                	sw	s1,12(sp)
    11fa:	c422                	sw	s0,8(sp)
    11fc:	cf8ff0ef          	jal	ra,6f4 <GPIO_Init>
    1200:	0141                	addi	sp,sp,16
    1202:	812ff06f          	j	214 <__riscv_restore_0>

00001206 <key_read>:
    1206:	febfe2ef          	jal	t0,1f0 <__riscv_save_0>
    120a:	6405                	lui	s0,0x1
    120c:	400114b7          	lui	s1,0x40011
    1210:	80040593          	addi	a1,s0,-2048 # 800 <__stack_size>
    1214:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1218:	daaff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    121c:	40048513          	addi	a0,s1,1024
    1220:	20000593          	li	a1,512
    1224:	400124b7          	lui	s1,0x40012
    1228:	d96ff0ef          	jal	ra,7be <GPIO_SetBits>
    122c:	65a1                	lui	a1,0x8
    122e:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    1232:	d8cff0ef          	jal	ra,7be <GPIO_SetBits>
    1236:	6589                	lui	a1,0x2
    1238:	80048513          	addi	a0,s1,-2048
    123c:	d82ff0ef          	jal	ra,7be <GPIO_SetBits>
    1240:	80040593          	addi	a1,s0,-2048
    1244:	80048513          	addi	a0,s1,-2048
    1248:	d6cff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    124c:	00153413          	seqz	s0,a0
    1250:	20000593          	li	a1,512
    1254:	80048513          	addi	a0,s1,-2048
    1258:	d5cff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    125c:	040a                	slli	s0,s0,0x2
    125e:	e111                	bnez	a0,1262 <key_read+0x5c>
    1260:	440d                	li	s0,3
    1262:	40012537          	lui	a0,0x40012
    1266:	08000593          	li	a1,128
    126a:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    126e:	d46ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1272:	e111                	bnez	a0,1276 <key_read+0x70>
    1274:	4409                	li	s0,2
    1276:	02000593          	li	a1,32
    127a:	40011537          	lui	a0,0x40011
    127e:	d36ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1282:	e111                	bnez	a0,1286 <key_read+0x80>
    1284:	4405                	li	s0,1
    1286:	400114b7          	lui	s1,0x40011
    128a:	6905                	lui	s2,0x1
    128c:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1290:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    1294:	d2aff0ef          	jal	ra,7be <GPIO_SetBits>
    1298:	40048513          	addi	a0,s1,1024
    129c:	20000593          	li	a1,512
    12a0:	d22ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    12a4:	400124b7          	lui	s1,0x40012
    12a8:	65a1                	lui	a1,0x8
    12aa:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    12ae:	d10ff0ef          	jal	ra,7be <GPIO_SetBits>
    12b2:	6589                	lui	a1,0x2
    12b4:	80048513          	addi	a0,s1,-2048
    12b8:	d06ff0ef          	jal	ra,7be <GPIO_SetBits>
    12bc:	80090593          	addi	a1,s2,-2048
    12c0:	80048513          	addi	a0,s1,-2048
    12c4:	cf0ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    12c8:	e111                	bnez	a0,12cc <key_read+0xc6>
    12ca:	4421                	li	s0,8
    12cc:	40012537          	lui	a0,0x40012
    12d0:	20000593          	li	a1,512
    12d4:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    12d8:	cdcff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    12dc:	e111                	bnez	a0,12e0 <key_read+0xda>
    12de:	441d                	li	s0,7
    12e0:	40012537          	lui	a0,0x40012
    12e4:	08000593          	li	a1,128
    12e8:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    12ec:	cc8ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    12f0:	e111                	bnez	a0,12f4 <key_read+0xee>
    12f2:	4419                	li	s0,6
    12f4:	02000593          	li	a1,32
    12f8:	40011537          	lui	a0,0x40011
    12fc:	cb8ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1300:	e111                	bnez	a0,1304 <key_read+0xfe>
    1302:	4415                	li	s0,5
    1304:	400114b7          	lui	s1,0x40011
    1308:	6905                	lui	s2,0x1
    130a:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    130e:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    1312:	cacff0ef          	jal	ra,7be <GPIO_SetBits>
    1316:	40048513          	addi	a0,s1,1024
    131a:	20000593          	li	a1,512
    131e:	ca0ff0ef          	jal	ra,7be <GPIO_SetBits>
    1322:	400124b7          	lui	s1,0x40012
    1326:	65a1                	lui	a1,0x8
    1328:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    132c:	c96ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1330:	6589                	lui	a1,0x2
    1332:	80048513          	addi	a0,s1,-2048
    1336:	c88ff0ef          	jal	ra,7be <GPIO_SetBits>
    133a:	80090593          	addi	a1,s2,-2048
    133e:	80048513          	addi	a0,s1,-2048
    1342:	c72ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1346:	e111                	bnez	a0,134a <key_read+0x144>
    1348:	4431                	li	s0,12
    134a:	40012537          	lui	a0,0x40012
    134e:	20000593          	li	a1,512
    1352:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    1356:	c5eff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    135a:	e111                	bnez	a0,135e <key_read+0x158>
    135c:	442d                	li	s0,11
    135e:	40012537          	lui	a0,0x40012
    1362:	08000593          	li	a1,128
    1366:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    136a:	c4aff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    136e:	e111                	bnez	a0,1372 <key_read+0x16c>
    1370:	4429                	li	s0,10
    1372:	02000593          	li	a1,32
    1376:	40011537          	lui	a0,0x40011
    137a:	c3aff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    137e:	e111                	bnez	a0,1382 <key_read+0x17c>
    1380:	4425                	li	s0,9
    1382:	400114b7          	lui	s1,0x40011
    1386:	6905                	lui	s2,0x1
    1388:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    138c:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    1390:	c2eff0ef          	jal	ra,7be <GPIO_SetBits>
    1394:	40048513          	addi	a0,s1,1024
    1398:	20000593          	li	a1,512
    139c:	c22ff0ef          	jal	ra,7be <GPIO_SetBits>
    13a0:	400124b7          	lui	s1,0x40012
    13a4:	65a1                	lui	a1,0x8
    13a6:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    13aa:	c14ff0ef          	jal	ra,7be <GPIO_SetBits>
    13ae:	6589                	lui	a1,0x2
    13b0:	80048513          	addi	a0,s1,-2048
    13b4:	c0eff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    13b8:	80090593          	addi	a1,s2,-2048
    13bc:	80048513          	addi	a0,s1,-2048
    13c0:	bf4ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13c4:	e111                	bnez	a0,13c8 <key_read+0x1c2>
    13c6:	4441                	li	s0,16
    13c8:	40012537          	lui	a0,0x40012
    13cc:	20000593          	li	a1,512
    13d0:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    13d4:	be0ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13d8:	e111                	bnez	a0,13dc <key_read+0x1d6>
    13da:	443d                	li	s0,15
    13dc:	40012537          	lui	a0,0x40012
    13e0:	08000593          	li	a1,128
    13e4:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    13e8:	bccff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13ec:	e111                	bnez	a0,13f0 <key_read+0x1ea>
    13ee:	4439                	li	s0,14
    13f0:	02000593          	li	a1,32
    13f4:	40011537          	lui	a0,0x40011
    13f8:	bbcff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13fc:	e111                	bnez	a0,1400 <key_read+0x1fa>
    13fe:	4435                	li	s0,13
    1400:	8522                	mv	a0,s0
    1402:	e13fe06f          	j	214 <__riscv_restore_0>

00001406 <SPI_LCD_Init>:
    1406:	dd1fe2ef          	jal	t0,1d6 <__riscv_save_4>
    140a:	1101                	addi	sp,sp,-32
    140c:	4585                	li	a1,1
    140e:	02c00513          	li	a0,44
    1412:	d7eff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1416:	4585                	li	a1,1
    1418:	6521                	lui	a0,0x8
    141a:	40011437          	lui	s0,0x40011
    141e:	d90ff0ef          	jal	ra,9ae <RCC_APB1PeriphClockCmd>
    1422:	03800793          	li	a5,56
    1426:	4941                	li	s2,16
    1428:	448d                	li	s1,3
    142a:	858a                	mv	a1,sp
    142c:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    1430:	807c                	sh	a5,0(sp)
    1432:	c44a                	sw	s2,8(sp)
    1434:	c226                	sw	s1,4(sp)
    1436:	abeff0ef          	jal	ra,6f4 <GPIO_Init>
    143a:	40040513          	addi	a0,s0,1024
    143e:	45a1                	li	a1,8
    1440:	b7eff0ef          	jal	ra,7be <GPIO_SetBits>
    1444:	40040513          	addi	a0,s0,1024
    1448:	45c1                	li	a1,16
    144a:	b74ff0ef          	jal	ra,7be <GPIO_SetBits>
    144e:	40040513          	addi	a0,s0,1024
    1452:	02000593          	li	a1,32
    1456:	b68ff0ef          	jal	ra,7be <GPIO_SetBits>
    145a:	77e1                	lui	a5,0xffff8
    145c:	858a                	mv	a1,sp
    145e:	80040513          	addi	a0,s0,-2048
    1462:	807c                	sh	a5,0(sp)
    1464:	c44a                	sw	s2,8(sp)
    1466:	c226                	sw	s1,4(sp)
    1468:	a8cff0ef          	jal	ra,6f4 <GPIO_Init>
    146c:	80040513          	addi	a0,s0,-2048
    1470:	65a1                	lui	a1,0x8
    1472:	b4cff0ef          	jal	ra,7be <GPIO_SetBits>
    1476:	47a1                	li	a5,8
    1478:	49e1                	li	s3,24
    147a:	858a                	mv	a1,sp
    147c:	c0040513          	addi	a0,s0,-1024
    1480:	807c                	sh	a5,0(sp)
    1482:	c44e                	sw	s3,8(sp)
    1484:	c226                	sw	s1,4(sp)
    1486:	a6eff0ef          	jal	ra,6f4 <GPIO_Init>
    148a:	858a                	mv	a1,sp
    148c:	c0040513          	addi	a0,s0,-1024
    1490:	01211023          	sh	s2,0(sp)
    1494:	c44a                	sw	s2,8(sp)
    1496:	a5eff0ef          	jal	ra,6f4 <GPIO_Init>
    149a:	02000793          	li	a5,32
    149e:	858a                	mv	a1,sp
    14a0:	c0040513          	addi	a0,s0,-1024
    14a4:	807c                	sh	a5,0(sp)
    14a6:	c44e                	sw	s3,8(sp)
    14a8:	c226                	sw	s1,4(sp)
    14aa:	a4aff0ef          	jal	ra,6f4 <GPIO_Init>
    14ae:	010407b7          	lui	a5,0x1040
    14b2:	c63e                	sw	a5,12(sp)
    14b4:	020007b7          	lui	a5,0x2000
    14b8:	40004437          	lui	s0,0x40004
    14bc:	ca3e                	sw	a5,20(sp)
    14be:	47a1                	li	a5,8
    14c0:	cc3e                	sw	a5,24(sp)
    14c2:	006c                	addi	a1,sp,12
    14c4:	479d                	li	a5,7
    14c6:	c0040513          	addi	a0,s0,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    14ca:	86fc                	sh	a5,28(sp)
    14cc:	c802                	sw	zero,16(sp)
    14ce:	cfeff0ef          	jal	ra,9cc <SPI_Init>
    14d2:	4585                	li	a1,1
    14d4:	c0040513          	addi	a0,s0,-1024
    14d8:	d32ff0ef          	jal	ra,a0a <SPI_Cmd>
    14dc:	6105                	addi	sp,sp,32
    14de:	d2dfe06f          	j	20a <__riscv_restore_4>

000014e2 <SPI3_IRQHandler>:
    14e2:	30200073          	mret

000014e6 <spi_readwrite>:
    14e6:	cf1fe2ef          	jal	t0,1d6 <__riscv_save_4>
    14ea:	400044b7          	lui	s1,0x40004
    14ee:	892a                	mv	s2,a0
    14f0:	0c900413          	li	s0,201
    14f4:	c0048993          	addi	s3,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    14f8:	4589                	li	a1,2
    14fa:	854e                	mv	a0,s3
    14fc:	d30ff0ef          	jal	ra,a2c <SPI_I2S_GetFlagStatus>
    1500:	c905                	beqz	a0,1530 <spi_readwrite+0x4a>
    1502:	85ca                	mv	a1,s2
    1504:	c0048513          	addi	a0,s1,-1024
    1508:	400044b7          	lui	s1,0x40004
    150c:	d18ff0ef          	jal	ra,a24 <SPI_I2S_SendData>
    1510:	0c900413          	li	s0,201
    1514:	c0048913          	addi	s2,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    1518:	4585                	li	a1,1
    151a:	854a                	mv	a0,s2
    151c:	d10ff0ef          	jal	ra,a2c <SPI_I2S_GetFlagStatus>
    1520:	cd19                	beqz	a0,153e <spi_readwrite+0x58>
    1522:	c0048513          	addi	a0,s1,-1024
    1526:	d02ff0ef          	jal	ra,a28 <SPI_I2S_ReceiveData>
    152a:	0ff57513          	andi	a0,a0,255
    152e:	a031                	j	153a <spi_readwrite+0x54>
    1530:	147d                	addi	s0,s0,-1
    1532:	0ff47413          	andi	s0,s0,255
    1536:	f069                	bnez	s0,14f8 <spi_readwrite+0x12>
    1538:	4501                	li	a0,0
    153a:	cd1fe06f          	j	20a <__riscv_restore_4>
    153e:	147d                	addi	s0,s0,-1
    1540:	0ff47413          	andi	s0,s0,255
    1544:	f871                	bnez	s0,1518 <spi_readwrite+0x32>
    1546:	bfcd                	j	1538 <spi_readwrite+0x52>

00001548 <LCD_WR_DATA8>:
    1548:	ca9fe2ef          	jal	t0,1f0 <__riscv_save_0>
    154c:	40011437          	lui	s0,0x40011
    1550:	84aa                	mv	s1,a0
    1552:	45c1                	li	a1,16
    1554:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    1558:	a6aff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    155c:	45a1                	li	a1,8
    155e:	40040513          	addi	a0,s0,1024
    1562:	a5cff0ef          	jal	ra,7be <GPIO_SetBits>
    1566:	8526                	mv	a0,s1
    1568:	3fbd                	jal	14e6 <spi_readwrite>
    156a:	45c1                	li	a1,16
    156c:	40040513          	addi	a0,s0,1024
    1570:	a4eff0ef          	jal	ra,7be <GPIO_SetBits>
    1574:	ca1fe06f          	j	214 <__riscv_restore_0>

00001578 <LCD_WR_DATA>:
    1578:	c79fe2ef          	jal	t0,1f0 <__riscv_save_0>
    157c:	40011437          	lui	s0,0x40011
    1580:	84aa                	mv	s1,a0
    1582:	45c1                	li	a1,16
    1584:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    1588:	a3aff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    158c:	45a1                	li	a1,8
    158e:	40040513          	addi	a0,s0,1024
    1592:	a2cff0ef          	jal	ra,7be <GPIO_SetBits>
    1596:	0084d513          	srli	a0,s1,0x8
    159a:	37b1                	jal	14e6 <spi_readwrite>
    159c:	0ff4f513          	andi	a0,s1,255
    15a0:	3799                	jal	14e6 <spi_readwrite>
    15a2:	45c1                	li	a1,16
    15a4:	40040513          	addi	a0,s0,1024
    15a8:	a16ff0ef          	jal	ra,7be <GPIO_SetBits>
    15ac:	c69fe06f          	j	214 <__riscv_restore_0>

000015b0 <LCD_WR_REG>:
    15b0:	c41fe2ef          	jal	t0,1f0 <__riscv_save_0>
    15b4:	40011437          	lui	s0,0x40011
    15b8:	84aa                	mv	s1,a0
    15ba:	45c1                	li	a1,16
    15bc:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    15c0:	a02ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    15c4:	45a1                	li	a1,8
    15c6:	40040513          	addi	a0,s0,1024
    15ca:	9f8ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    15ce:	8526                	mv	a0,s1
    15d0:	3f19                	jal	14e6 <spi_readwrite>
    15d2:	45c1                	li	a1,16
    15d4:	40040513          	addi	a0,s0,1024
    15d8:	9e6ff0ef          	jal	ra,7be <GPIO_SetBits>
    15dc:	c39fe06f          	j	214 <__riscv_restore_0>

000015e0 <LCD_Address_Set>:
    15e0:	c11fe2ef          	jal	t0,1f0 <__riscv_save_0>
    15e4:	1141                	addi	sp,sp,-16
    15e6:	842a                	mv	s0,a0
    15e8:	02a00513          	li	a0,42
    15ec:	c236                	sw	a3,4(sp)
    15ee:	c62e                	sw	a1,12(sp)
    15f0:	c432                	sw	a2,8(sp)
    15f2:	3f7d                	jal	15b0 <LCD_WR_REG>
    15f4:	00240513          	addi	a0,s0,2
    15f8:	0542                	slli	a0,a0,0x10
    15fa:	8141                	srli	a0,a0,0x10
    15fc:	3fb5                	jal	1578 <LCD_WR_DATA>
    15fe:	4622                	lw	a2,8(sp)
    1600:	0609                	addi	a2,a2,2
    1602:	01061513          	slli	a0,a2,0x10
    1606:	8141                	srli	a0,a0,0x10
    1608:	3f85                	jal	1578 <LCD_WR_DATA>
    160a:	02b00513          	li	a0,43
    160e:	374d                	jal	15b0 <LCD_WR_REG>
    1610:	45b2                	lw	a1,12(sp)
    1612:	058d                	addi	a1,a1,3
    1614:	01059513          	slli	a0,a1,0x10
    1618:	8141                	srli	a0,a0,0x10
    161a:	3fb9                	jal	1578 <LCD_WR_DATA>
    161c:	4692                	lw	a3,4(sp)
    161e:	068d                	addi	a3,a3,3
    1620:	01069513          	slli	a0,a3,0x10
    1624:	8141                	srli	a0,a0,0x10
    1626:	3f89                	jal	1578 <LCD_WR_DATA>
    1628:	02c00513          	li	a0,44
    162c:	3751                	jal	15b0 <LCD_WR_REG>
    162e:	0141                	addi	sp,sp,16
    1630:	be5fe06f          	j	214 <__riscv_restore_0>

00001634 <LCD_Init>:
    1634:	bbdfe2ef          	jal	t0,1f0 <__riscv_save_0>
    1638:	33f9                	jal	1406 <SPI_LCD_Init>
    163a:	40011437          	lui	s0,0x40011
    163e:	45c1                	li	a1,16
    1640:	c0040513          	addi	a0,s0,-1024 # 40010c00 <_eusrstack+0x20008c00>
    1644:	97eff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1648:	06400513          	li	a0,100
    164c:	2961                	jal	1ae4 <Delay_Ms>
    164e:	45c1                	li	a1,16
    1650:	c0040513          	addi	a0,s0,-1024
    1654:	96aff0ef          	jal	ra,7be <GPIO_SetBits>
    1658:	06400513          	li	a0,100
    165c:	2161                	jal	1ae4 <Delay_Ms>
    165e:	02000593          	li	a1,32
    1662:	40040513          	addi	a0,s0,1024
    1666:	958ff0ef          	jal	ra,7be <GPIO_SetBits>
    166a:	06400513          	li	a0,100
    166e:	299d                	jal	1ae4 <Delay_Ms>
    1670:	4545                	li	a0,17
    1672:	3f3d                	jal	15b0 <LCD_WR_REG>
    1674:	07800513          	li	a0,120
    1678:	21b5                	jal	1ae4 <Delay_Ms>
    167a:	0b100513          	li	a0,177
    167e:	3f0d                	jal	15b0 <LCD_WR_REG>
    1680:	4515                	li	a0,5
    1682:	35d9                	jal	1548 <LCD_WR_DATA8>
    1684:	03c00513          	li	a0,60
    1688:	35c1                	jal	1548 <LCD_WR_DATA8>
    168a:	03c00513          	li	a0,60
    168e:	3d6d                	jal	1548 <LCD_WR_DATA8>
    1690:	0b200513          	li	a0,178
    1694:	3f31                	jal	15b0 <LCD_WR_REG>
    1696:	4515                	li	a0,5
    1698:	3d45                	jal	1548 <LCD_WR_DATA8>
    169a:	03c00513          	li	a0,60
    169e:	356d                	jal	1548 <LCD_WR_DATA8>
    16a0:	03c00513          	li	a0,60
    16a4:	3555                	jal	1548 <LCD_WR_DATA8>
    16a6:	0b300513          	li	a0,179
    16aa:	3719                	jal	15b0 <LCD_WR_REG>
    16ac:	4515                	li	a0,5
    16ae:	3d69                	jal	1548 <LCD_WR_DATA8>
    16b0:	03c00513          	li	a0,60
    16b4:	3d51                	jal	1548 <LCD_WR_DATA8>
    16b6:	03c00513          	li	a0,60
    16ba:	3579                	jal	1548 <LCD_WR_DATA8>
    16bc:	4515                	li	a0,5
    16be:	3569                	jal	1548 <LCD_WR_DATA8>
    16c0:	03c00513          	li	a0,60
    16c4:	3551                	jal	1548 <LCD_WR_DATA8>
    16c6:	03c00513          	li	a0,60
    16ca:	3dbd                	jal	1548 <LCD_WR_DATA8>
    16cc:	0b400513          	li	a0,180
    16d0:	35c5                	jal	15b0 <LCD_WR_REG>
    16d2:	450d                	li	a0,3
    16d4:	3d95                	jal	1548 <LCD_WR_DATA8>
    16d6:	03a00513          	li	a0,58
    16da:	3dd9                	jal	15b0 <LCD_WR_REG>
    16dc:	4515                	li	a0,5
    16de:	35ad                	jal	1548 <LCD_WR_DATA8>
    16e0:	0c000513          	li	a0,192
    16e4:	35f1                	jal	15b0 <LCD_WR_REG>
    16e6:	0a200513          	li	a0,162
    16ea:	3db9                	jal	1548 <LCD_WR_DATA8>
    16ec:	4509                	li	a0,2
    16ee:	3da9                	jal	1548 <LCD_WR_DATA8>
    16f0:	08400513          	li	a0,132
    16f4:	3d91                	jal	1548 <LCD_WR_DATA8>
    16f6:	0c100513          	li	a0,193
    16fa:	3d5d                	jal	15b0 <LCD_WR_REG>
    16fc:	0c500513          	li	a0,197
    1700:	35a1                	jal	1548 <LCD_WR_DATA8>
    1702:	0c200513          	li	a0,194
    1706:	356d                	jal	15b0 <LCD_WR_REG>
    1708:	4535                	li	a0,13
    170a:	3d3d                	jal	1548 <LCD_WR_DATA8>
    170c:	4501                	li	a0,0
    170e:	3d2d                	jal	1548 <LCD_WR_DATA8>
    1710:	0c300513          	li	a0,195
    1714:	3d71                	jal	15b0 <LCD_WR_REG>
    1716:	08d00513          	li	a0,141
    171a:	353d                	jal	1548 <LCD_WR_DATA8>
    171c:	02a00513          	li	a0,42
    1720:	3525                	jal	1548 <LCD_WR_DATA8>
    1722:	0c400513          	li	a0,196
    1726:	3569                	jal	15b0 <LCD_WR_REG>
    1728:	08d00513          	li	a0,141
    172c:	3d31                	jal	1548 <LCD_WR_DATA8>
    172e:	0ee00513          	li	a0,238
    1732:	3d19                	jal	1548 <LCD_WR_DATA8>
    1734:	0c500513          	li	a0,197
    1738:	3da5                	jal	15b0 <LCD_WR_REG>
    173a:	4529                	li	a0,10
    173c:	3531                	jal	1548 <LCD_WR_DATA8>
    173e:	03600513          	li	a0,54
    1742:	35bd                	jal	15b0 <LCD_WR_REG>
    1744:	0c800513          	li	a0,200
    1748:	3501                	jal	1548 <LCD_WR_DATA8>
    174a:	0e000513          	li	a0,224
    174e:	358d                	jal	15b0 <LCD_WR_REG>
    1750:	4549                	li	a0,18
    1752:	3bdd                	jal	1548 <LCD_WR_DATA8>
    1754:	4571                	li	a0,28
    1756:	3bcd                	jal	1548 <LCD_WR_DATA8>
    1758:	4541                	li	a0,16
    175a:	33fd                	jal	1548 <LCD_WR_DATA8>
    175c:	4561                	li	a0,24
    175e:	33ed                	jal	1548 <LCD_WR_DATA8>
    1760:	03300513          	li	a0,51
    1764:	33d5                	jal	1548 <LCD_WR_DATA8>
    1766:	02c00513          	li	a0,44
    176a:	3bf9                	jal	1548 <LCD_WR_DATA8>
    176c:	02500513          	li	a0,37
    1770:	3be1                	jal	1548 <LCD_WR_DATA8>
    1772:	02800513          	li	a0,40
    1776:	3bc9                	jal	1548 <LCD_WR_DATA8>
    1778:	02800513          	li	a0,40
    177c:	33f1                	jal	1548 <LCD_WR_DATA8>
    177e:	02700513          	li	a0,39
    1782:	33d9                	jal	1548 <LCD_WR_DATA8>
    1784:	02f00513          	li	a0,47
    1788:	33c1                	jal	1548 <LCD_WR_DATA8>
    178a:	03c00513          	li	a0,60
    178e:	3b6d                	jal	1548 <LCD_WR_DATA8>
    1790:	4501                	li	a0,0
    1792:	3b5d                	jal	1548 <LCD_WR_DATA8>
    1794:	450d                	li	a0,3
    1796:	3b4d                	jal	1548 <LCD_WR_DATA8>
    1798:	450d                	li	a0,3
    179a:	337d                	jal	1548 <LCD_WR_DATA8>
    179c:	4541                	li	a0,16
    179e:	336d                	jal	1548 <LCD_WR_DATA8>
    17a0:	0e100513          	li	a0,225
    17a4:	3531                	jal	15b0 <LCD_WR_REG>
    17a6:	4549                	li	a0,18
    17a8:	3345                	jal	1548 <LCD_WR_DATA8>
    17aa:	4571                	li	a0,28
    17ac:	3b71                	jal	1548 <LCD_WR_DATA8>
    17ae:	4541                	li	a0,16
    17b0:	3b61                	jal	1548 <LCD_WR_DATA8>
    17b2:	4561                	li	a0,24
    17b4:	3b51                	jal	1548 <LCD_WR_DATA8>
    17b6:	02d00513          	li	a0,45
    17ba:	3379                	jal	1548 <LCD_WR_DATA8>
    17bc:	02800513          	li	a0,40
    17c0:	3361                	jal	1548 <LCD_WR_DATA8>
    17c2:	02300513          	li	a0,35
    17c6:	3349                	jal	1548 <LCD_WR_DATA8>
    17c8:	02800513          	li	a0,40
    17cc:	3bb5                	jal	1548 <LCD_WR_DATA8>
    17ce:	02800513          	li	a0,40
    17d2:	3b9d                	jal	1548 <LCD_WR_DATA8>
    17d4:	02600513          	li	a0,38
    17d8:	3b85                	jal	1548 <LCD_WR_DATA8>
    17da:	02f00513          	li	a0,47
    17de:	33ad                	jal	1548 <LCD_WR_DATA8>
    17e0:	03b00513          	li	a0,59
    17e4:	3395                	jal	1548 <LCD_WR_DATA8>
    17e6:	4501                	li	a0,0
    17e8:	3385                	jal	1548 <LCD_WR_DATA8>
    17ea:	450d                	li	a0,3
    17ec:	3bb1                	jal	1548 <LCD_WR_DATA8>
    17ee:	450d                	li	a0,3
    17f0:	3ba1                	jal	1548 <LCD_WR_DATA8>
    17f2:	4541                	li	a0,16
    17f4:	3b91                	jal	1548 <LCD_WR_DATA8>
    17f6:	02000513          	li	a0,32
    17fa:	3b5d                	jal	15b0 <LCD_WR_REG>
    17fc:	454d                	li	a0,19
    17fe:	3b4d                	jal	15b0 <LCD_WR_REG>
    1800:	02a00513          	li	a0,42
    1804:	3375                	jal	15b0 <LCD_WR_REG>
    1806:	4501                	li	a0,0
    1808:	3381                	jal	1548 <LCD_WR_DATA8>
    180a:	4501                	li	a0,0
    180c:	3b35                	jal	1548 <LCD_WR_DATA8>
    180e:	4501                	li	a0,0
    1810:	3b25                	jal	1548 <LCD_WR_DATA8>
    1812:	07f00513          	li	a0,127
    1816:	3b0d                	jal	1548 <LCD_WR_DATA8>
    1818:	02b00513          	li	a0,43
    181c:	3b51                	jal	15b0 <LCD_WR_REG>
    181e:	4501                	li	a0,0
    1820:	3325                	jal	1548 <LCD_WR_DATA8>
    1822:	4501                	li	a0,0
    1824:	3315                	jal	1548 <LCD_WR_DATA8>
    1826:	4501                	li	a0,0
    1828:	3305                	jal	1548 <LCD_WR_DATA8>
    182a:	07f00513          	li	a0,127
    182e:	3b29                	jal	1548 <LCD_WR_DATA8>
    1830:	02900513          	li	a0,41
    1834:	3bb5                	jal	15b0 <LCD_WR_REG>
    1836:	9dffe06f          	j	214 <__riscv_restore_0>

0000183a <LCD_Fill>:
    183a:	99dfe2ef          	jal	t0,1d6 <__riscv_save_4>
    183e:	89b2                	mv	s3,a2
    1840:	8936                	mv	s2,a3
    1842:	167d                	addi	a2,a2,-1
    1844:	16fd                	addi	a3,a3,-1
    1846:	06c2                	slli	a3,a3,0x10
    1848:	0642                	slli	a2,a2,0x10
    184a:	82c1                	srli	a3,a3,0x10
    184c:	8241                	srli	a2,a2,0x10
    184e:	8a2a                	mv	s4,a0
    1850:	842e                	mv	s0,a1
    1852:	8aba                	mv	s5,a4
    1854:	d8dff0ef          	jal	ra,15e0 <LCD_Address_Set>
    1858:	03246063          	bltu	s0,s2,1878 <LCD_Fill+0x3e>
    185c:	9affe06f          	j	20a <__riscv_restore_4>
    1860:	0485                	addi	s1,s1,1
    1862:	8556                	mv	a0,s5
    1864:	04c2                	slli	s1,s1,0x10
    1866:	d13ff0ef          	jal	ra,1578 <LCD_WR_DATA>
    186a:	80c1                	srli	s1,s1,0x10
    186c:	ff34eae3          	bltu	s1,s3,1860 <LCD_Fill+0x26>
    1870:	0405                	addi	s0,s0,1
    1872:	0442                	slli	s0,s0,0x10
    1874:	8041                	srli	s0,s0,0x10
    1876:	b7cd                	j	1858 <LCD_Fill+0x1e>
    1878:	84d2                	mv	s1,s4
    187a:	bfcd                	j	186c <LCD_Fill+0x32>

0000187c <TIM2_PWM_Init>:
    187c:	975fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1880:	7179                	addi	sp,sp,-48
    1882:	4585                	li	a1,1
    1884:	4505                	li	a0,1
    1886:	928ff0ef          	jal	ra,9ae <RCC_APB1PeriphClockCmd>
    188a:	05f00793          	li	a5,95
    188e:	c43e                	sw	a5,8(sp)
    1890:	6795                	lui	a5,0x5
    1892:	e1f78793          	addi	a5,a5,-481 # 4e1f <_data_lma+0x1847>
    1896:	002c                	addi	a1,sp,8
    1898:	40000537          	lui	a0,0x40000
    189c:	c63e                	sw	a5,12(sp)
    189e:	998ff0ef          	jal	ra,a36 <TIM_TimeBaseInit>
    18a2:	67c1                	lui	a5,0x10
    18a4:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xca88>
    18a8:	d03e                	sw	a5,32(sp)
    18aa:	100c                	addi	a1,sp,32
    18ac:	5dc00793          	li	a5,1500
    18b0:	40000537          	lui	a0,0x40000
    18b4:	02f11323          	sh	a5,38(sp)
    18b8:	02011423          	sh	zero,40(sp)
    18bc:	a24ff0ef          	jal	ra,ae0 <TIM_OC1Init>
    18c0:	45a1                	li	a1,8
    18c2:	40000537          	lui	a0,0x40000
    18c6:	ae0ff0ef          	jal	ra,ba6 <TIM_OC1PreloadConfig>
    18ca:	4585                	li	a1,1
    18cc:	40000537          	lui	a0,0x40000
    18d0:	abcff0ef          	jal	ra,b8c <TIM_ARRPreloadConfig>
    18d4:	4585                	li	a1,1
    18d6:	40000537          	lui	a0,0x40000
    18da:	a88ff0ef          	jal	ra,b62 <TIM_Cmd>
    18de:	4585                	li	a1,1
    18e0:	4511                	li	a0,4
    18e2:	8aeff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    18e6:	4785                	li	a5,1
    18e8:	82fc                	sh	a5,20(sp)
    18ea:	40011537          	lui	a0,0x40011
    18ee:	47e1                	li	a5,24
    18f0:	ce3e                	sw	a5,28(sp)
    18f2:	084c                	addi	a1,sp,20
    18f4:	478d                	li	a5,3
    18f6:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    18fa:	cc3e                	sw	a5,24(sp)
    18fc:	df9fe0ef          	jal	ra,6f4 <GPIO_Init>
    1900:	6145                	addi	sp,sp,48
    1902:	913fe06f          	j	214 <__riscv_restore_0>

00001906 <stir_360>:
    1906:	8ebfe2ef          	jal	t0,1f0 <__riscv_save_0>
    190a:	4785                	li	a5,1
    190c:	02f51963          	bne	a0,a5,193e <stir_360+0x38>
    1910:	06400713          	li	a4,100
    1914:	0ff5f793          	andi	a5,a1,255
    1918:	00b77463          	bgeu	a4,a1,1920 <stir_360+0x1a>
    191c:	06400793          	li	a5,100
    1920:	45a9                	li	a1,10
    1922:	02b785b3          	mul	a1,a5,a1
    1926:	5dc00793          	li	a5,1500
    192a:	8f8d                	sub	a5,a5,a1
    192c:	07c2                	slli	a5,a5,0x10
    192e:	83c1                	srli	a5,a5,0x10
    1930:	85be                	mv	a1,a5
    1932:	40000537          	lui	a0,0x40000
    1936:	a7eff0ef          	jal	ra,bb4 <TIM_SetCompare1>
    193a:	8dbfe06f          	j	214 <__riscv_restore_0>
    193e:	4709                	li	a4,2
    1940:	5dc00793          	li	a5,1500
    1944:	fee516e3          	bne	a0,a4,1930 <stir_360+0x2a>
    1948:	06400713          	li	a4,100
    194c:	0ff5f793          	andi	a5,a1,255
    1950:	00b77463          	bgeu	a4,a1,1958 <stir_360+0x52>
    1954:	06400793          	li	a5,100
    1958:	45a9                	li	a1,10
    195a:	02b787b3          	mul	a5,a5,a1
    195e:	5dc78793          	addi	a5,a5,1500
    1962:	b7f9                	j	1930 <stir_360+0x2a>

00001964 <stir>:
    1964:	88dfe2ef          	jal	t0,1f0 <__riscv_save_0>
    1968:	c519                	beqz	a0,1976 <stir+0x12>
    196a:	03200593          	li	a1,50
    196e:	4505                	li	a0,1
    1970:	3f59                	jal	1906 <stir_360>
    1972:	8a3fe06f          	j	214 <__riscv_restore_0>
    1976:	5dc00593          	li	a1,1500
    197a:	40000537          	lui	a0,0x40000
    197e:	a36ff0ef          	jal	ra,bb4 <TIM_SetCompare1>
    1982:	bfc5                	j	1972 <stir+0xe>

00001984 <WaterLevel_Init>:
    1984:	86dfe2ef          	jal	t0,1f0 <__riscv_save_0>
    1988:	1141                	addi	sp,sp,-16
    198a:	4585                	li	a1,1
    198c:	4541                	li	a0,16
    198e:	c202                	sw	zero,4(sp)
    1990:	c402                	sw	zero,8(sp)
    1992:	c602                	sw	zero,12(sp)
    1994:	ffdfe0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1998:	47c1                	li	a5,16
    199a:	827c                	sh	a5,4(sp)
    199c:	478d                	li	a5,3
    199e:	c43e                	sw	a5,8(sp)
    19a0:	004c                	addi	a1,sp,4
    19a2:	04800793          	li	a5,72
    19a6:	40011537          	lui	a0,0x40011
    19aa:	c63e                	sw	a5,12(sp)
    19ac:	d49fe0ef          	jal	ra,6f4 <GPIO_Init>
    19b0:	0141                	addi	sp,sp,16
    19b2:	863fe06f          	j	214 <__riscv_restore_0>

000019b6 <delay_us>:
    19b6:	050e                	slli	a0,a0,0x3
    19b8:	4781                	li	a5,0
    19ba:	00f51363          	bne	a0,a5,19c0 <delay_us+0xa>
    19be:	8082                	ret
    19c0:	0001                	nop
    19c2:	0785                	addi	a5,a5,1
    19c4:	bfdd                	j	19ba <delay_us+0x4>

000019c6 <delay_ms>:
    19c6:	82bfe2ef          	jal	t0,1f0 <__riscv_save_0>
    19ca:	84aa                	mv	s1,a0
    19cc:	4401                	li	s0,0
    19ce:	00941463          	bne	s0,s1,19d6 <delay_ms+0x10>
    19d2:	843fe06f          	j	214 <__riscv_restore_0>
    19d6:	3e800513          	li	a0,1000
    19da:	3ff1                	jal	19b6 <delay_us>
    19dc:	0405                	addi	s0,s0,1
    19de:	bfc5                	j	19ce <delay_ms+0x8>

000019e0 <WaterPump_Init>:
    19e0:	811fe2ef          	jal	t0,1f0 <__riscv_save_0>
    19e4:	1141                	addi	sp,sp,-16
    19e6:	4585                	li	a1,1
    19e8:	4541                	li	a0,16
    19ea:	fa7fe0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    19ee:	6789                	lui	a5,0x2
    19f0:	440d                	li	s0,3
    19f2:	44c1                	li	s1,16
    19f4:	004c                	addi	a1,sp,4
    19f6:	40011537          	lui	a0,0x40011
    19fa:	827c                	sh	a5,4(sp)
    19fc:	c626                	sw	s1,12(sp)
    19fe:	c422                	sw	s0,8(sp)
    1a00:	cf5fe0ef          	jal	ra,6f4 <GPIO_Init>
    1a04:	6589                	lui	a1,0x2
    1a06:	40011537          	lui	a0,0x40011
    1a0a:	db9fe0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1a0e:	4585                	li	a1,1
    1a10:	4541                	li	a0,16
    1a12:	f7ffe0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1a16:	6785                	lui	a5,0x1
    1a18:	80078793          	addi	a5,a5,-2048 # 800 <__stack_size>
    1a1c:	004c                	addi	a1,sp,4
    1a1e:	40011537          	lui	a0,0x40011
    1a22:	827c                	sh	a5,4(sp)
    1a24:	c626                	sw	s1,12(sp)
    1a26:	c422                	sw	s0,8(sp)
    1a28:	ccdfe0ef          	jal	ra,6f4 <GPIO_Init>
    1a2c:	6585                	lui	a1,0x1
    1a2e:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1a32:	40011537          	lui	a0,0x40011
    1a36:	d8dfe0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1a3a:	86018793          	addi	a5,gp,-1952 # 20000108 <g_pump_ctrl>
    1a3e:	4705                	li	a4,1
    1a40:	0007a023          	sw	zero,0(a5)
    1a44:	a3da                	sh	a4,4(a5)
    1a46:	0007a423          	sw	zero,8(a5)
    1a4a:	0007a623          	sw	zero,12(a5)
    1a4e:	0007a823          	sw	zero,16(a5)
    1a52:	abda                	sh	a4,20(a5)
    1a54:	0007ac23          	sw	zero,24(a5)
    1a58:	0007ae23          	sw	zero,28(a5)
    1a5c:	0141                	addi	sp,sp,16
    1a5e:	fb6fe06f          	j	214 <__riscv_restore_0>

00001a62 <WaterPump_Control>:
    1a62:	4705                	li	a4,1
    1a64:	04a76a63          	bltu	a4,a0,1ab8 <WaterPump_Control+0x56>
    1a68:	f88fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1a6c:	86018793          	addi	a5,gp,-1952 # 20000108 <g_pump_ctrl>
    1a70:	00451493          	slli	s1,a0,0x4
    1a74:	97a6                	add	a5,a5,s1
    1a76:	23dc                	lbu	a5,4(a5)
    1a78:	86018413          	addi	s0,gp,-1952 # 20000108 <g_pump_ctrl>
    1a7c:	cf99                	beqz	a5,1a9a <WaterPump_Control+0x38>
    1a7e:	02e59063          	bne	a1,a4,1a9e <WaterPump_Control+0x3c>
    1a82:	6589                	lui	a1,0x2
    1a84:	c501                	beqz	a0,1a8c <WaterPump_Control+0x2a>
    1a86:	6585                	lui	a1,0x1
    1a88:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1a8c:	40011537          	lui	a0,0x40011
    1a90:	d2ffe0ef          	jal	ra,7be <GPIO_SetBits>
    1a94:	9426                	add	s0,s0,s1
    1a96:	4785                	li	a5,1
    1a98:	c01c                	sw	a5,0(s0)
    1a9a:	f7afe06f          	j	214 <__riscv_restore_0>
    1a9e:	6589                	lui	a1,0x2
    1aa0:	c501                	beqz	a0,1aa8 <WaterPump_Control+0x46>
    1aa2:	6585                	lui	a1,0x1
    1aa4:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1aa8:	40011537          	lui	a0,0x40011
    1aac:	9426                	add	s0,s0,s1
    1aae:	d15fe0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1ab2:	00042023          	sw	zero,0(s0)
    1ab6:	b7d5                	j	1a9a <WaterPump_Control+0x38>
    1ab8:	8082                	ret

00001aba <Delay_Init>:
    1aba:	200007b7          	lui	a5,0x20000
    1abe:	0a87a783          	lw	a5,168(a5) # 200000a8 <SystemCoreClock>
    1ac2:	007a1737          	lui	a4,0x7a1
    1ac6:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc28>
    1aca:	02e7d7b3          	divu	a5,a5,a4
    1ace:	0ff7f793          	andi	a5,a5,255
    1ad2:	82f18723          	sb	a5,-2002(gp) # 200000d6 <p_us>
    1ad6:	3e800713          	li	a4,1000
    1ada:	02e787b3          	mul	a5,a5,a4
    1ade:	82f19623          	sh	a5,-2004(gp) # 200000d4 <p_ms>
    1ae2:	8082                	ret

00001ae4 <Delay_Ms>:
    1ae4:	e000f7b7          	lui	a5,0xe000f
    1ae8:	43d8                	lw	a4,4(a5)
    1aea:	4681                	li	a3,0
    1aec:	9b79                	andi	a4,a4,-2
    1aee:	c3d8                	sw	a4,4(a5)
    1af0:	82c1d703          	lhu	a4,-2004(gp) # 200000d4 <p_ms>
    1af4:	02a70633          	mul	a2,a4,a0
    1af8:	cb90                	sw	a2,16(a5)
    1afa:	cbd4                	sw	a3,20(a5)
    1afc:	4398                	lw	a4,0(a5)
    1afe:	01076713          	ori	a4,a4,16
    1b02:	c398                	sw	a4,0(a5)
    1b04:	4398                	lw	a4,0(a5)
    1b06:	02176713          	ori	a4,a4,33
    1b0a:	c398                	sw	a4,0(a5)
    1b0c:	43d8                	lw	a4,4(a5)
    1b0e:	8b05                	andi	a4,a4,1
    1b10:	df75                	beqz	a4,1b0c <Delay_Ms+0x28>
    1b12:	4398                	lw	a4,0(a5)
    1b14:	9b79                	andi	a4,a4,-2
    1b16:	c398                	sw	a4,0(a5)
    1b18:	8082                	ret

00001b1a <USART_Printf_Init>:
    1b1a:	ed6fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1b1e:	842a                	mv	s0,a0
    1b20:	6511                	lui	a0,0x4
    1b22:	1101                	addi	sp,sp,-32
    1b24:	4585                	li	a1,1
    1b26:	0511                	addi	a0,a0,4
    1b28:	e69fe0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1b2c:	20000793          	li	a5,512
    1b30:	827c                	sh	a5,4(sp)
    1b32:	40011537          	lui	a0,0x40011
    1b36:	478d                	li	a5,3
    1b38:	c43e                	sw	a5,8(sp)
    1b3a:	004c                	addi	a1,sp,4
    1b3c:	47e1                	li	a5,24
    1b3e:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1b42:	c63e                	sw	a5,12(sp)
    1b44:	bb1fe0ef          	jal	ra,6f4 <GPIO_Init>
    1b48:	c822                	sw	s0,16(sp)
    1b4a:	40014437          	lui	s0,0x40014
    1b4e:	000807b7          	lui	a5,0x80
    1b52:	080c                	addi	a1,sp,16
    1b54:	80040513          	addi	a0,s0,-2048 # 40013800 <_eusrstack+0x2000b800>
    1b58:	cc3e                	sw	a5,24(sp)
    1b5a:	ca02                	sw	zero,20(sp)
    1b5c:	00011e23          	sh	zero,28(sp)
    1b60:	87cff0ef          	jal	ra,bdc <USART_Init>
    1b64:	4585                	li	a1,1
    1b66:	80040513          	addi	a0,s0,-2048
    1b6a:	900ff0ef          	jal	ra,c6a <USART_Cmd>
    1b6e:	6105                	addi	sp,sp,32
    1b70:	ea4fe06f          	j	214 <__riscv_restore_0>

00001b74 <_write>:
    1b74:	e62fe2ef          	jal	t0,1d6 <__riscv_save_4>
    1b78:	400144b7          	lui	s1,0x40014
    1b7c:	89ae                	mv	s3,a1
    1b7e:	8932                	mv	s2,a2
    1b80:	4401                	li	s0,0
    1b82:	80048493          	addi	s1,s1,-2048 # 40013800 <_eusrstack+0x2000b800>
    1b86:	01244563          	blt	s0,s2,1b90 <_write+0x1c>
    1b8a:	854a                	mv	a0,s2
    1b8c:	e7efe06f          	j	20a <__riscv_restore_4>
    1b90:	04000593          	li	a1,64
    1b94:	8526                	mv	a0,s1
    1b96:	8f2ff0ef          	jal	ra,c88 <USART_GetFlagStatus>
    1b9a:	d97d                	beqz	a0,1b90 <_write+0x1c>
    1b9c:	008987b3          	add	a5,s3,s0
    1ba0:	00078583          	lb	a1,0(a5) # 80000 <_data_lma+0x7ca28>
    1ba4:	8526                	mv	a0,s1
    1ba6:	0405                	addi	s0,s0,1
    1ba8:	05c2                	slli	a1,a1,0x10
    1baa:	81c1                	srli	a1,a1,0x10
    1bac:	8d4ff0ef          	jal	ra,c80 <USART_SendData>
    1bb0:	bfd9                	j	1b86 <_write+0x12>

00001bb2 <_sbrk>:
    1bb2:	80818713          	addi	a4,gp,-2040 # 200000b0 <curbrk.5274>
    1bb6:	431c                	lw	a5,0(a4)
    1bb8:	88418693          	addi	a3,gp,-1916 # 2000012c <_ebss>
    1bbc:	953e                	add	a0,a0,a5
    1bbe:	00d56b63          	bltu	a0,a3,1bd4 <_sbrk+0x22>
    1bc2:	200086b7          	lui	a3,0x20008
    1bc6:	80068693          	addi	a3,a3,-2048 # 20007800 <_heap_end>
    1bca:	00a6e563          	bltu	a3,a0,1bd4 <_sbrk+0x22>
    1bce:	c308                	sw	a0,0(a4)
    1bd0:	853e                	mv	a0,a5
    1bd2:	8082                	ret
    1bd4:	57fd                	li	a5,-1
    1bd6:	bfed                	j	1bd0 <_sbrk+0x1e>

00001bd8 <__lesf2>:
    1bd8:	01755693          	srli	a3,a0,0x17
    1bdc:	008007b7          	lui	a5,0x800
    1be0:	17fd                	addi	a5,a5,-1
    1be2:	0175d613          	srli	a2,a1,0x17
    1be6:	0ff6f693          	andi	a3,a3,255
    1bea:	0ff00813          	li	a6,255
    1bee:	00a7f8b3          	and	a7,a5,a0
    1bf2:	01f55713          	srli	a4,a0,0x1f
    1bf6:	8fed                	and	a5,a5,a1
    1bf8:	0ff67613          	andi	a2,a2,255
    1bfc:	81fd                	srli	a1,a1,0x1f
    1bfe:	03068763          	beq	a3,a6,1c2c <__lesf2+0x54>
    1c02:	01060963          	beq	a2,a6,1c14 <__lesf2+0x3c>
    1c06:	ea85                	bnez	a3,1c36 <__lesf2+0x5e>
    1c08:	ea11                	bnez	a2,1c1c <__lesf2+0x44>
    1c0a:	eb89                	bnez	a5,1c1c <__lesf2+0x44>
    1c0c:	4501                	li	a0,0
    1c0e:	00089b63          	bnez	a7,1c24 <__lesf2+0x4c>
    1c12:	8082                	ret
    1c14:	4509                	li	a0,2
    1c16:	fff5                	bnez	a5,1c12 <__lesf2+0x3a>
    1c18:	dae5                	beqz	a3,1c08 <__lesf2+0x30>
    1c1a:	a831                	j	1c36 <__lesf2+0x5e>
    1c1c:	02088c63          	beqz	a7,1c54 <__lesf2+0x7c>
    1c20:	04b70063          	beq	a4,a1,1c60 <__lesf2+0x88>
    1c24:	4505                	li	a0,1
    1c26:	d775                	beqz	a4,1c12 <__lesf2+0x3a>
    1c28:	557d                	li	a0,-1
    1c2a:	8082                	ret
    1c2c:	4509                	li	a0,2
    1c2e:	02089863          	bnez	a7,1c5e <__lesf2+0x86>
    1c32:	02d60463          	beq	a2,a3,1c5a <__lesf2+0x82>
    1c36:	e211                	bnez	a2,1c3a <__lesf2+0x62>
    1c38:	d7f5                	beqz	a5,1c24 <__lesf2+0x4c>
    1c3a:	feb715e3          	bne	a4,a1,1c24 <__lesf2+0x4c>
    1c3e:	fed643e3          	blt	a2,a3,1c24 <__lesf2+0x4c>
    1c42:	00c6c763          	blt	a3,a2,1c50 <__lesf2+0x78>
    1c46:	fd17efe3          	bltu	a5,a7,1c24 <__lesf2+0x4c>
    1c4a:	4501                	li	a0,0
    1c4c:	fcf8f3e3          	bgeu	a7,a5,1c12 <__lesf2+0x3a>
    1c50:	e319                	bnez	a4,1c56 <__lesf2+0x7e>
    1c52:	bfd9                	j	1c28 <__lesf2+0x50>
    1c54:	d9f1                	beqz	a1,1c28 <__lesf2+0x50>
    1c56:	4505                	li	a0,1
    1c58:	8082                	ret
    1c5a:	d3e5                	beqz	a5,1c3a <__lesf2+0x62>
    1c5c:	8082                	ret
    1c5e:	8082                	ret
    1c60:	4681                	li	a3,0
    1c62:	b7c5                	j	1c42 <__lesf2+0x6a>

00001c64 <__mulsf3>:
    1c64:	7179                	addi	sp,sp,-48
    1c66:	d422                	sw	s0,40(sp)
    1c68:	01755413          	srli	s0,a0,0x17
    1c6c:	ce4e                	sw	s3,28(sp)
    1c6e:	cc52                	sw	s4,24(sp)
    1c70:	00951993          	slli	s3,a0,0x9
    1c74:	d606                	sw	ra,44(sp)
    1c76:	d226                	sw	s1,36(sp)
    1c78:	d04a                	sw	s2,32(sp)
    1c7a:	ca56                	sw	s5,20(sp)
    1c7c:	c85a                	sw	s6,16(sp)
    1c7e:	0ff47413          	andi	s0,s0,255
    1c82:	0099d993          	srli	s3,s3,0x9
    1c86:	01f55a13          	srli	s4,a0,0x1f
    1c8a:	c469                	beqz	s0,1d54 <__mulsf3+0xf0>
    1c8c:	0ff00793          	li	a5,255
    1c90:	0ef40863          	beq	s0,a5,1d80 <__mulsf3+0x11c>
    1c94:	00399793          	slli	a5,s3,0x3
    1c98:	04000737          	lui	a4,0x4000
    1c9c:	00e7e9b3          	or	s3,a5,a4
    1ca0:	f8140413          	addi	s0,s0,-127
    1ca4:	4481                	li	s1,0
    1ca6:	4b01                	li	s6,0
    1ca8:	0175d713          	srli	a4,a1,0x17
    1cac:	00959a93          	slli	s5,a1,0x9
    1cb0:	0ff77713          	andi	a4,a4,255
    1cb4:	009ada93          	srli	s5,s5,0x9
    1cb8:	01f5d913          	srli	s2,a1,0x1f
    1cbc:	cf45                	beqz	a4,1d74 <__mulsf3+0x110>
    1cbe:	0ff00793          	li	a5,255
    1cc2:	02f70c63          	beq	a4,a5,1cfa <__mulsf3+0x96>
    1cc6:	0a8e                	slli	s5,s5,0x3
    1cc8:	f8170713          	addi	a4,a4,-127 # 3ffff81 <_data_lma+0x3ffc9a9>
    1ccc:	040007b7          	lui	a5,0x4000
    1cd0:	00faeab3          	or	s5,s5,a5
    1cd4:	943a                	add	s0,s0,a4
    1cd6:	4601                	li	a2,0
    1cd8:	012a4533          	xor	a0,s4,s2
    1cdc:	47bd                	li	a5,15
    1cde:	86aa                	mv	a3,a0
    1ce0:	00140593          	addi	a1,s0,1
    1ce4:	1097e063          	bltu	a5,s1,1de4 <__mulsf3+0x180>
    1ce8:	00001717          	auipc	a4,0x1
    1cec:	71470713          	addi	a4,a4,1812 # 33fc <_read+0x168>
    1cf0:	048a                	slli	s1,s1,0x2
    1cf2:	94ba                	add	s1,s1,a4
    1cf4:	409c                	lw	a5,0(s1)
    1cf6:	97ba                	add	a5,a5,a4
    1cf8:	8782                	jr	a5
    1cfa:	0ff40413          	addi	s0,s0,255
    1cfe:	0c0a9663          	bnez	s5,1dca <__mulsf3+0x166>
    1d02:	0024e493          	ori	s1,s1,2
    1d06:	4609                	li	a2,2
    1d08:	bfc1                	j	1cd8 <__mulsf3+0x74>
    1d0a:	4501                	li	a0,0
    1d0c:	0ff00713          	li	a4,255
    1d10:	004007b7          	lui	a5,0x400
    1d14:	50b2                	lw	ra,44(sp)
    1d16:	5422                	lw	s0,40(sp)
    1d18:	07a6                	slli	a5,a5,0x9
    1d1a:	075e                	slli	a4,a4,0x17
    1d1c:	83a5                	srli	a5,a5,0x9
    1d1e:	057e                	slli	a0,a0,0x1f
    1d20:	8fd9                	or	a5,a5,a4
    1d22:	5492                	lw	s1,36(sp)
    1d24:	5902                	lw	s2,32(sp)
    1d26:	49f2                	lw	s3,28(sp)
    1d28:	4a62                	lw	s4,24(sp)
    1d2a:	4ad2                	lw	s5,20(sp)
    1d2c:	4b42                	lw	s6,16(sp)
    1d2e:	8d5d                	or	a0,a0,a5
    1d30:	6145                	addi	sp,sp,48
    1d32:	8082                	ret
    1d34:	86ca                	mv	a3,s2
    1d36:	89d6                	mv	s3,s5
    1d38:	8b32                	mv	s6,a2
    1d3a:	4789                	li	a5,2
    1d3c:	08fb0f63          	beq	s6,a5,1dda <__mulsf3+0x176>
    1d40:	478d                	li	a5,3
    1d42:	fcfb04e3          	beq	s6,a5,1d0a <__mulsf3+0xa6>
    1d46:	4785                	li	a5,1
    1d48:	8536                	mv	a0,a3
    1d4a:	1afb1063          	bne	s6,a5,1eea <__mulsf3+0x286>
    1d4e:	4701                	li	a4,0
    1d50:	4781                	li	a5,0
    1d52:	b7c9                	j	1d14 <__mulsf3+0xb0>
    1d54:	04099d63          	bnez	s3,1dae <__mulsf3+0x14a>
    1d58:	0175d713          	srli	a4,a1,0x17
    1d5c:	00959a93          	slli	s5,a1,0x9
    1d60:	0ff77713          	andi	a4,a4,255
    1d64:	4491                	li	s1,4
    1d66:	4401                	li	s0,0
    1d68:	4b05                	li	s6,1
    1d6a:	009ada93          	srli	s5,s5,0x9
    1d6e:	01f5d913          	srli	s2,a1,0x1f
    1d72:	f731                	bnez	a4,1cbe <__mulsf3+0x5a>
    1d74:	000a9d63          	bnez	s5,1d8e <__mulsf3+0x12a>
    1d78:	0014e493          	ori	s1,s1,1
    1d7c:	4605                	li	a2,1
    1d7e:	bfa9                	j	1cd8 <__mulsf3+0x74>
    1d80:	02099263          	bnez	s3,1da4 <__mulsf3+0x140>
    1d84:	44a1                	li	s1,8
    1d86:	0ff00413          	li	s0,255
    1d8a:	4b09                	li	s6,2
    1d8c:	bf31                	j	1ca8 <__mulsf3+0x44>
    1d8e:	8556                	mv	a0,s5
    1d90:	2cd9                	jal	2066 <__clzsi2>
    1d92:	ffb50793          	addi	a5,a0,-5
    1d96:	8c09                	sub	s0,s0,a0
    1d98:	00fa9ab3          	sll	s5,s5,a5
    1d9c:	f8a40413          	addi	s0,s0,-118
    1da0:	4601                	li	a2,0
    1da2:	bf1d                	j	1cd8 <__mulsf3+0x74>
    1da4:	44b1                	li	s1,12
    1da6:	0ff00413          	li	s0,255
    1daa:	4b0d                	li	s6,3
    1dac:	bdf5                	j	1ca8 <__mulsf3+0x44>
    1dae:	854e                	mv	a0,s3
    1db0:	c62e                	sw	a1,12(sp)
    1db2:	2c55                	jal	2066 <__clzsi2>
    1db4:	ffb50793          	addi	a5,a0,-5
    1db8:	f8a00413          	li	s0,-118
    1dbc:	00f999b3          	sll	s3,s3,a5
    1dc0:	8c09                	sub	s0,s0,a0
    1dc2:	4481                	li	s1,0
    1dc4:	4b01                	li	s6,0
    1dc6:	45b2                	lw	a1,12(sp)
    1dc8:	b5c5                	j	1ca8 <__mulsf3+0x44>
    1dca:	0034e493          	ori	s1,s1,3
    1dce:	460d                	li	a2,3
    1dd0:	b721                	j	1cd8 <__mulsf3+0x74>
    1dd2:	4789                	li	a5,2
    1dd4:	86d2                	mv	a3,s4
    1dd6:	f6fb15e3          	bne	s6,a5,1d40 <__mulsf3+0xdc>
    1dda:	8536                	mv	a0,a3
    1ddc:	0ff00713          	li	a4,255
    1de0:	4781                	li	a5,0
    1de2:	bf0d                	j	1d14 <__mulsf3+0xb0>
    1de4:	6341                	lui	t1,0x10
    1de6:	fff30693          	addi	a3,t1,-1 # ffff <_data_lma+0xca27>
    1dea:	0109d613          	srli	a2,s3,0x10
    1dee:	010ad893          	srli	a7,s5,0x10
    1df2:	00d9f7b3          	and	a5,s3,a3
    1df6:	00dafab3          	and	s5,s5,a3
    1dfa:	03578833          	mul	a6,a5,s5
    1dfe:	02f889b3          	mul	s3,a7,a5
    1e02:	01085713          	srli	a4,a6,0x10
    1e06:	03560ab3          	mul	s5,a2,s5
    1e0a:	99d6                	add	s3,s3,s5
    1e0c:	974e                	add	a4,a4,s3
    1e0e:	03160633          	mul	a2,a2,a7
    1e12:	01577363          	bgeu	a4,s5,1e18 <__mulsf3+0x1b4>
    1e16:	961a                	add	a2,a2,t1
    1e18:	67c1                	lui	a5,0x10
    1e1a:	17fd                	addi	a5,a5,-1
    1e1c:	00f776b3          	and	a3,a4,a5
    1e20:	00f87833          	and	a6,a6,a5
    1e24:	06c2                	slli	a3,a3,0x10
    1e26:	96c2                	add	a3,a3,a6
    1e28:	00669993          	slli	s3,a3,0x6
    1e2c:	01075793          	srli	a5,a4,0x10
    1e30:	013039b3          	snez	s3,s3
    1e34:	82e9                	srli	a3,a3,0x1a
    1e36:	97b2                	add	a5,a5,a2
    1e38:	079a                	slli	a5,a5,0x6
    1e3a:	00d9e6b3          	or	a3,s3,a3
    1e3e:	00d7e9b3          	or	s3,a5,a3
    1e42:	00499793          	slli	a5,s3,0x4
    1e46:	0007d963          	bgez	a5,1e58 <__mulsf3+0x1f4>
    1e4a:	0019d713          	srli	a4,s3,0x1
    1e4e:	0019f793          	andi	a5,s3,1
    1e52:	00f769b3          	or	s3,a4,a5
    1e56:	842e                	mv	s0,a1
    1e58:	07f40713          	addi	a4,s0,127
    1e5c:	04e05063          	blez	a4,1e9c <__mulsf3+0x238>
    1e60:	0079f793          	andi	a5,s3,7
    1e64:	c799                	beqz	a5,1e72 <__mulsf3+0x20e>
    1e66:	00f9f793          	andi	a5,s3,15
    1e6a:	4691                	li	a3,4
    1e6c:	00d78363          	beq	a5,a3,1e72 <__mulsf3+0x20e>
    1e70:	0991                	addi	s3,s3,4
    1e72:	00499793          	slli	a5,s3,0x4
    1e76:	0007d963          	bgez	a5,1e88 <__mulsf3+0x224>
    1e7a:	f80007b7          	lui	a5,0xf8000
    1e7e:	17fd                	addi	a5,a5,-1
    1e80:	00f9f9b3          	and	s3,s3,a5
    1e84:	08040713          	addi	a4,s0,128
    1e88:	0fe00793          	li	a5,254
    1e8c:	04e7cb63          	blt	a5,a4,1ee2 <__mulsf3+0x27e>
    1e90:	00699793          	slli	a5,s3,0x6
    1e94:	83a5                	srli	a5,a5,0x9
    1e96:	0ff77713          	andi	a4,a4,255
    1e9a:	bdad                	j	1d14 <__mulsf3+0xb0>
    1e9c:	4785                	li	a5,1
    1e9e:	40e786b3          	sub	a3,a5,a4
    1ea2:	c711                	beqz	a4,1eae <__mulsf3+0x24a>
    1ea4:	466d                	li	a2,27
    1ea6:	4701                	li	a4,0
    1ea8:	4781                	li	a5,0
    1eaa:	e6d645e3          	blt	a2,a3,1d14 <__mulsf3+0xb0>
    1eae:	09e40713          	addi	a4,s0,158
    1eb2:	00e99733          	sll	a4,s3,a4
    1eb6:	00e03733          	snez	a4,a4
    1eba:	00d9d7b3          	srl	a5,s3,a3
    1ebe:	8fd9                	or	a5,a5,a4
    1ec0:	0077f713          	andi	a4,a5,7
    1ec4:	c719                	beqz	a4,1ed2 <__mulsf3+0x26e>
    1ec6:	00f7f713          	andi	a4,a5,15
    1eca:	4691                	li	a3,4
    1ecc:	00d70363          	beq	a4,a3,1ed2 <__mulsf3+0x26e>
    1ed0:	0791                	addi	a5,a5,4
    1ed2:	00579713          	slli	a4,a5,0x5
    1ed6:	00074c63          	bltz	a4,1eee <__mulsf3+0x28a>
    1eda:	079a                	slli	a5,a5,0x6
    1edc:	83a5                	srli	a5,a5,0x9
    1ede:	4701                	li	a4,0
    1ee0:	bd15                	j	1d14 <__mulsf3+0xb0>
    1ee2:	0ff00713          	li	a4,255
    1ee6:	4781                	li	a5,0
    1ee8:	b535                	j	1d14 <__mulsf3+0xb0>
    1eea:	842e                	mv	s0,a1
    1eec:	b7b5                	j	1e58 <__mulsf3+0x1f4>
    1eee:	4705                	li	a4,1
    1ef0:	4781                	li	a5,0
    1ef2:	b50d                	j	1d14 <__mulsf3+0xb0>

00001ef4 <__floatsisf>:
    1ef4:	1141                	addi	sp,sp,-16
    1ef6:	c606                	sw	ra,12(sp)
    1ef8:	c422                	sw	s0,8(sp)
    1efa:	c226                	sw	s1,4(sp)
    1efc:	cd0d                	beqz	a0,1f36 <__floatsisf+0x42>
    1efe:	41f55793          	srai	a5,a0,0x1f
    1f02:	00a7c433          	xor	s0,a5,a0
    1f06:	8c1d                	sub	s0,s0,a5
    1f08:	84aa                	mv	s1,a0
    1f0a:	8522                	mv	a0,s0
    1f0c:	2aa9                	jal	2066 <__clzsi2>
    1f0e:	09e00793          	li	a5,158
    1f12:	40a78733          	sub	a4,a5,a0
    1f16:	09600793          	li	a5,150
    1f1a:	80fd                	srli	s1,s1,0x1f
    1f1c:	02e7cc63          	blt	a5,a4,1f54 <__floatsisf+0x60>
    1f20:	46a1                	li	a3,8
    1f22:	0ff77793          	andi	a5,a4,255
    1f26:	00a6d563          	bge	a3,a0,1f30 <__floatsisf+0x3c>
    1f2a:	1561                	addi	a0,a0,-8
    1f2c:	00a41433          	sll	s0,s0,a0
    1f30:	0426                	slli	s0,s0,0x9
    1f32:	8025                	srli	s0,s0,0x9
    1f34:	a021                	j	1f3c <__floatsisf+0x48>
    1f36:	4481                	li	s1,0
    1f38:	4781                	li	a5,0
    1f3a:	4401                	li	s0,0
    1f3c:	0426                	slli	s0,s0,0x9
    1f3e:	00945513          	srli	a0,s0,0x9
    1f42:	40b2                	lw	ra,12(sp)
    1f44:	4422                	lw	s0,8(sp)
    1f46:	07de                	slli	a5,a5,0x17
    1f48:	04fe                	slli	s1,s1,0x1f
    1f4a:	8d5d                	or	a0,a0,a5
    1f4c:	8d45                	or	a0,a0,s1
    1f4e:	4492                	lw	s1,4(sp)
    1f50:	0141                	addi	sp,sp,16
    1f52:	8082                	ret
    1f54:	09900793          	li	a5,153
    1f58:	00e7dd63          	bge	a5,a4,1f72 <__floatsisf+0x7e>
    1f5c:	01b50793          	addi	a5,a0,27
    1f60:	4695                	li	a3,5
    1f62:	00f417b3          	sll	a5,s0,a5
    1f66:	8e89                	sub	a3,a3,a0
    1f68:	00d45433          	srl	s0,s0,a3
    1f6c:	00f037b3          	snez	a5,a5
    1f70:	8c5d                	or	s0,s0,a5
    1f72:	4795                	li	a5,5
    1f74:	00a7d663          	bge	a5,a0,1f80 <__floatsisf+0x8c>
    1f78:	ffb50793          	addi	a5,a0,-5
    1f7c:	00f41433          	sll	s0,s0,a5
    1f80:	fc0006b7          	lui	a3,0xfc000
    1f84:	16fd                	addi	a3,a3,-1
    1f86:	00747793          	andi	a5,s0,7
    1f8a:	00d47633          	and	a2,s0,a3
    1f8e:	c385                	beqz	a5,1fae <__floatsisf+0xba>
    1f90:	00f47793          	andi	a5,s0,15
    1f94:	4591                	li	a1,4
    1f96:	00b78c63          	beq	a5,a1,1fae <__floatsisf+0xba>
    1f9a:	0611                	addi	a2,a2,4
    1f9c:	00561793          	slli	a5,a2,0x5
    1fa0:	0007d763          	bgez	a5,1fae <__floatsisf+0xba>
    1fa4:	09f00793          	li	a5,159
    1fa8:	8e75                	and	a2,a2,a3
    1faa:	40a78733          	sub	a4,a5,a0
    1fae:	00661413          	slli	s0,a2,0x6
    1fb2:	8025                	srli	s0,s0,0x9
    1fb4:	0ff77793          	andi	a5,a4,255
    1fb8:	b751                	j	1f3c <__floatsisf+0x48>

00001fba <__extendsfdf2>:
    1fba:	01755713          	srli	a4,a0,0x17
    1fbe:	0ff77713          	andi	a4,a4,255
    1fc2:	1141                	addi	sp,sp,-16
    1fc4:	00170793          	addi	a5,a4,1
    1fc8:	c422                	sw	s0,8(sp)
    1fca:	c226                	sw	s1,4(sp)
    1fcc:	00951413          	slli	s0,a0,0x9
    1fd0:	c606                	sw	ra,12(sp)
    1fd2:	0fe7f793          	andi	a5,a5,254
    1fd6:	8025                	srli	s0,s0,0x9
    1fd8:	01f55493          	srli	s1,a0,0x1f
    1fdc:	c785                	beqz	a5,2004 <__extendsfdf2+0x4a>
    1fde:	00345793          	srli	a5,s0,0x3
    1fe2:	38070713          	addi	a4,a4,896
    1fe6:	0476                	slli	s0,s0,0x1d
    1fe8:	07b2                	slli	a5,a5,0xc
    1fea:	0752                	slli	a4,a4,0x14
    1fec:	83b1                	srli	a5,a5,0xc
    1fee:	01f49513          	slli	a0,s1,0x1f
    1ff2:	8fd9                	or	a5,a5,a4
    1ff4:	8fc9                	or	a5,a5,a0
    1ff6:	40b2                	lw	ra,12(sp)
    1ff8:	8522                	mv	a0,s0
    1ffa:	4422                	lw	s0,8(sp)
    1ffc:	4492                	lw	s1,4(sp)
    1ffe:	85be                	mv	a1,a5
    2000:	0141                	addi	sp,sp,16
    2002:	8082                	ret
    2004:	eb05                	bnez	a4,2034 <__extendsfdf2+0x7a>
    2006:	c439                	beqz	s0,2054 <__extendsfdf2+0x9a>
    2008:	8522                	mv	a0,s0
    200a:	28b1                	jal	2066 <__clzsi2>
    200c:	47a9                	li	a5,10
    200e:	04a7c663          	blt	a5,a0,205a <__extendsfdf2+0xa0>
    2012:	472d                	li	a4,11
    2014:	8f09                	sub	a4,a4,a0
    2016:	01550793          	addi	a5,a0,21
    201a:	00e45733          	srl	a4,s0,a4
    201e:	00f41433          	sll	s0,s0,a5
    2022:	00c71793          	slli	a5,a4,0xc
    2026:	38900713          	li	a4,905
    202a:	8f09                	sub	a4,a4,a0
    202c:	83b1                	srli	a5,a5,0xc
    202e:	7ff77713          	andi	a4,a4,2047
    2032:	bf5d                	j	1fe8 <__extendsfdf2+0x2e>
    2034:	cc01                	beqz	s0,204c <__extendsfdf2+0x92>
    2036:	00345713          	srli	a4,s0,0x3
    203a:	000807b7          	lui	a5,0x80
    203e:	8fd9                	or	a5,a5,a4
    2040:	07b2                	slli	a5,a5,0xc
    2042:	0476                	slli	s0,s0,0x1d
    2044:	83b1                	srli	a5,a5,0xc
    2046:	7ff00713          	li	a4,2047
    204a:	bf79                	j	1fe8 <__extendsfdf2+0x2e>
    204c:	7ff00713          	li	a4,2047
    2050:	4781                	li	a5,0
    2052:	bf59                	j	1fe8 <__extendsfdf2+0x2e>
    2054:	4701                	li	a4,0
    2056:	4781                	li	a5,0
    2058:	bf41                	j	1fe8 <__extendsfdf2+0x2e>
    205a:	ff550713          	addi	a4,a0,-11
    205e:	00e41733          	sll	a4,s0,a4
    2062:	4401                	li	s0,0
    2064:	bf7d                	j	2022 <__extendsfdf2+0x68>

00002066 <__clzsi2>:
    2066:	67c1                	lui	a5,0x10
    2068:	02f57c63          	bgeu	a0,a5,20a0 <__clzsi2+0x3a>
    206c:	0ff00793          	li	a5,255
    2070:	02000713          	li	a4,32
    2074:	00a7eb63          	bltu	a5,a0,208a <__clzsi2+0x24>
    2078:	00001797          	auipc	a5,0x1
    207c:	3c478793          	addi	a5,a5,964 # 343c <__clz_tab>
    2080:	97aa                	add	a5,a5,a0
    2082:	2388                	lbu	a0,0(a5)
    2084:	40a70533          	sub	a0,a4,a0
    2088:	8082                	ret
    208a:	8121                	srli	a0,a0,0x8
    208c:	00001797          	auipc	a5,0x1
    2090:	3b078793          	addi	a5,a5,944 # 343c <__clz_tab>
    2094:	97aa                	add	a5,a5,a0
    2096:	2388                	lbu	a0,0(a5)
    2098:	4761                	li	a4,24
    209a:	40a70533          	sub	a0,a4,a0
    209e:	8082                	ret
    20a0:	010007b7          	lui	a5,0x1000
    20a4:	00f56d63          	bltu	a0,a5,20be <__clzsi2+0x58>
    20a8:	8161                	srli	a0,a0,0x18
    20aa:	00001797          	auipc	a5,0x1
    20ae:	39278793          	addi	a5,a5,914 # 343c <__clz_tab>
    20b2:	97aa                	add	a5,a5,a0
    20b4:	2388                	lbu	a0,0(a5)
    20b6:	4721                	li	a4,8
    20b8:	40a70533          	sub	a0,a4,a0
    20bc:	8082                	ret
    20be:	8141                	srli	a0,a0,0x10
    20c0:	00001797          	auipc	a5,0x1
    20c4:	37c78793          	addi	a5,a5,892 # 343c <__clz_tab>
    20c8:	97aa                	add	a5,a5,a0
    20ca:	2388                	lbu	a0,0(a5)
    20cc:	4741                	li	a4,16
    20ce:	40a70533          	sub	a0,a4,a0
    20d2:	8082                	ret

000020d4 <iprintf>:
    20d4:	7139                	addi	sp,sp,-64
    20d6:	da3e                	sw	a5,52(sp)
    20d8:	d22e                	sw	a1,36(sp)
    20da:	d432                	sw	a2,40(sp)
    20dc:	d636                	sw	a3,44(sp)
    20de:	d83a                	sw	a4,48(sp)
    20e0:	dc42                	sw	a6,56(sp)
    20e2:	de46                	sw	a7,60(sp)
    20e4:	80c18793          	addi	a5,gp,-2036 # 200000b4 <_impure_ptr>
    20e8:	cc22                	sw	s0,24(sp)
    20ea:	4380                	lw	s0,0(a5)
    20ec:	ca26                	sw	s1,20(sp)
    20ee:	ce06                	sw	ra,28(sp)
    20f0:	84aa                	mv	s1,a0
    20f2:	c409                	beqz	s0,20fc <iprintf+0x28>
    20f4:	4c1c                	lw	a5,24(s0)
    20f6:	e399                	bnez	a5,20fc <iprintf+0x28>
    20f8:	8522                	mv	a0,s0
    20fa:	29fd                	jal	25f8 <__sinit>
    20fc:	440c                	lw	a1,8(s0)
    20fe:	1054                	addi	a3,sp,36
    2100:	8626                	mv	a2,s1
    2102:	8522                	mv	a0,s0
    2104:	c636                	sw	a3,12(sp)
    2106:	139000ef          	jal	ra,2a3e <_vfiprintf_r>
    210a:	40f2                	lw	ra,28(sp)
    210c:	4462                	lw	s0,24(sp)
    210e:	44d2                	lw	s1,20(sp)
    2110:	6121                	addi	sp,sp,64
    2112:	8082                	ret

00002114 <_puts_r>:
    2114:	1101                	addi	sp,sp,-32
    2116:	ca26                	sw	s1,20(sp)
    2118:	c84a                	sw	s2,16(sp)
    211a:	ce06                	sw	ra,28(sp)
    211c:	cc22                	sw	s0,24(sp)
    211e:	c64e                	sw	s3,12(sp)
    2120:	c452                	sw	s4,8(sp)
    2122:	84aa                	mv	s1,a0
    2124:	892e                	mv	s2,a1
    2126:	c501                	beqz	a0,212e <_puts_r+0x1a>
    2128:	4d1c                	lw	a5,24(a0)
    212a:	e391                	bnez	a5,212e <_puts_r+0x1a>
    212c:	21f1                	jal	25f8 <__sinit>
    212e:	4c9c                	lw	a5,24(s1)
    2130:	4480                	lw	s0,8(s1)
    2132:	e399                	bnez	a5,2138 <_puts_r+0x24>
    2134:	8526                	mv	a0,s1
    2136:	21c9                	jal	25f8 <__sinit>
    2138:	00001797          	auipc	a5,0x1
    213c:	42478793          	addi	a5,a5,1060 # 355c <__sf_fake_stdin>
    2140:	02f41b63          	bne	s0,a5,2176 <_puts_r+0x62>
    2144:	40c0                	lw	s0,4(s1)
    2146:	245e                	lhu	a5,12(s0)
    2148:	8ba1                	andi	a5,a5,8
    214a:	c7b1                	beqz	a5,2196 <_puts_r+0x82>
    214c:	481c                	lw	a5,16(s0)
    214e:	c7a1                	beqz	a5,2196 <_puts_r+0x82>
    2150:	59fd                	li	s3,-1
    2152:	4a29                	li	s4,10
    2154:	441c                	lw	a5,8(s0)
    2156:	00094583          	lbu	a1,0(s2)
    215a:	17fd                	addi	a5,a5,-1
    215c:	e9b1                	bnez	a1,21b0 <_puts_r+0x9c>
    215e:	c41c                	sw	a5,8(s0)
    2160:	0607dd63          	bgez	a5,21da <_puts_r+0xc6>
    2164:	8622                	mv	a2,s0
    2166:	45a9                	li	a1,10
    2168:	8526                	mv	a0,s1
    216a:	2069                	jal	21f4 <__swbuf_r>
    216c:	57fd                	li	a5,-1
    216e:	02f50863          	beq	a0,a5,219e <_puts_r+0x8a>
    2172:	4529                	li	a0,10
    2174:	a035                	j	21a0 <_puts_r+0x8c>
    2176:	00001797          	auipc	a5,0x1
    217a:	40678793          	addi	a5,a5,1030 # 357c <__sf_fake_stdout>
    217e:	00f41463          	bne	s0,a5,2186 <_puts_r+0x72>
    2182:	4480                	lw	s0,8(s1)
    2184:	b7c9                	j	2146 <_puts_r+0x32>
    2186:	00001797          	auipc	a5,0x1
    218a:	3b678793          	addi	a5,a5,950 # 353c <__sf_fake_stderr>
    218e:	faf41ce3          	bne	s0,a5,2146 <_puts_r+0x32>
    2192:	44c0                	lw	s0,12(s1)
    2194:	bf4d                	j	2146 <_puts_r+0x32>
    2196:	85a2                	mv	a1,s0
    2198:	8526                	mv	a0,s1
    219a:	2a19                	jal	22b0 <__swsetup_r>
    219c:	d955                	beqz	a0,2150 <_puts_r+0x3c>
    219e:	557d                	li	a0,-1
    21a0:	40f2                	lw	ra,28(sp)
    21a2:	4462                	lw	s0,24(sp)
    21a4:	44d2                	lw	s1,20(sp)
    21a6:	4942                	lw	s2,16(sp)
    21a8:	49b2                	lw	s3,12(sp)
    21aa:	4a22                	lw	s4,8(sp)
    21ac:	6105                	addi	sp,sp,32
    21ae:	8082                	ret
    21b0:	c41c                	sw	a5,8(s0)
    21b2:	0905                	addi	s2,s2,1
    21b4:	0007d763          	bgez	a5,21c2 <_puts_r+0xae>
    21b8:	4c18                	lw	a4,24(s0)
    21ba:	00e7ca63          	blt	a5,a4,21ce <_puts_r+0xba>
    21be:	01458863          	beq	a1,s4,21ce <_puts_r+0xba>
    21c2:	401c                	lw	a5,0(s0)
    21c4:	00178713          	addi	a4,a5,1
    21c8:	c018                	sw	a4,0(s0)
    21ca:	a38c                	sb	a1,0(a5)
    21cc:	b761                	j	2154 <_puts_r+0x40>
    21ce:	8622                	mv	a2,s0
    21d0:	8526                	mv	a0,s1
    21d2:	200d                	jal	21f4 <__swbuf_r>
    21d4:	f93510e3          	bne	a0,s3,2154 <_puts_r+0x40>
    21d8:	b7d9                	j	219e <_puts_r+0x8a>
    21da:	401c                	lw	a5,0(s0)
    21dc:	00178713          	addi	a4,a5,1
    21e0:	c018                	sw	a4,0(s0)
    21e2:	4729                	li	a4,10
    21e4:	a398                	sb	a4,0(a5)
    21e6:	b771                	j	2172 <_puts_r+0x5e>

000021e8 <puts>:
    21e8:	80c18793          	addi	a5,gp,-2036 # 200000b4 <_impure_ptr>
    21ec:	85aa                	mv	a1,a0
    21ee:	4388                	lw	a0,0(a5)
    21f0:	f25ff06f          	j	2114 <_puts_r>

000021f4 <__swbuf_r>:
    21f4:	1101                	addi	sp,sp,-32
    21f6:	cc22                	sw	s0,24(sp)
    21f8:	ca26                	sw	s1,20(sp)
    21fa:	c84a                	sw	s2,16(sp)
    21fc:	ce06                	sw	ra,28(sp)
    21fe:	c64e                	sw	s3,12(sp)
    2200:	84aa                	mv	s1,a0
    2202:	892e                	mv	s2,a1
    2204:	8432                	mv	s0,a2
    2206:	c501                	beqz	a0,220e <__swbuf_r+0x1a>
    2208:	4d1c                	lw	a5,24(a0)
    220a:	e391                	bnez	a5,220e <__swbuf_r+0x1a>
    220c:	26f5                	jal	25f8 <__sinit>
    220e:	00001797          	auipc	a5,0x1
    2212:	34e78793          	addi	a5,a5,846 # 355c <__sf_fake_stdin>
    2216:	06f41763          	bne	s0,a5,2284 <__swbuf_r+0x90>
    221a:	40c0                	lw	s0,4(s1)
    221c:	4c1c                	lw	a5,24(s0)
    221e:	c41c                	sw	a5,8(s0)
    2220:	245e                	lhu	a5,12(s0)
    2222:	8ba1                	andi	a5,a5,8
    2224:	c3c1                	beqz	a5,22a4 <__swbuf_r+0xb0>
    2226:	481c                	lw	a5,16(s0)
    2228:	cfb5                	beqz	a5,22a4 <__swbuf_r+0xb0>
    222a:	481c                	lw	a5,16(s0)
    222c:	4008                	lw	a0,0(s0)
    222e:	0ff97993          	andi	s3,s2,255
    2232:	0ff97913          	andi	s2,s2,255
    2236:	8d1d                	sub	a0,a0,a5
    2238:	485c                	lw	a5,20(s0)
    223a:	00f54663          	blt	a0,a5,2246 <__swbuf_r+0x52>
    223e:	85a2                	mv	a1,s0
    2240:	8526                	mv	a0,s1
    2242:	2c69                	jal	24dc <_fflush_r>
    2244:	e525                	bnez	a0,22ac <__swbuf_r+0xb8>
    2246:	441c                	lw	a5,8(s0)
    2248:	0505                	addi	a0,a0,1
    224a:	17fd                	addi	a5,a5,-1
    224c:	c41c                	sw	a5,8(s0)
    224e:	401c                	lw	a5,0(s0)
    2250:	00178713          	addi	a4,a5,1
    2254:	c018                	sw	a4,0(s0)
    2256:	01378023          	sb	s3,0(a5)
    225a:	485c                	lw	a5,20(s0)
    225c:	00a78863          	beq	a5,a0,226c <__swbuf_r+0x78>
    2260:	245e                	lhu	a5,12(s0)
    2262:	8b85                	andi	a5,a5,1
    2264:	cb81                	beqz	a5,2274 <__swbuf_r+0x80>
    2266:	47a9                	li	a5,10
    2268:	00f91663          	bne	s2,a5,2274 <__swbuf_r+0x80>
    226c:	85a2                	mv	a1,s0
    226e:	8526                	mv	a0,s1
    2270:	24b5                	jal	24dc <_fflush_r>
    2272:	ed0d                	bnez	a0,22ac <__swbuf_r+0xb8>
    2274:	40f2                	lw	ra,28(sp)
    2276:	4462                	lw	s0,24(sp)
    2278:	854a                	mv	a0,s2
    227a:	44d2                	lw	s1,20(sp)
    227c:	4942                	lw	s2,16(sp)
    227e:	49b2                	lw	s3,12(sp)
    2280:	6105                	addi	sp,sp,32
    2282:	8082                	ret
    2284:	00001797          	auipc	a5,0x1
    2288:	2f878793          	addi	a5,a5,760 # 357c <__sf_fake_stdout>
    228c:	00f41463          	bne	s0,a5,2294 <__swbuf_r+0xa0>
    2290:	4480                	lw	s0,8(s1)
    2292:	b769                	j	221c <__swbuf_r+0x28>
    2294:	00001797          	auipc	a5,0x1
    2298:	2a878793          	addi	a5,a5,680 # 353c <__sf_fake_stderr>
    229c:	f8f410e3          	bne	s0,a5,221c <__swbuf_r+0x28>
    22a0:	44c0                	lw	s0,12(s1)
    22a2:	bfad                	j	221c <__swbuf_r+0x28>
    22a4:	85a2                	mv	a1,s0
    22a6:	8526                	mv	a0,s1
    22a8:	2021                	jal	22b0 <__swsetup_r>
    22aa:	d141                	beqz	a0,222a <__swbuf_r+0x36>
    22ac:	597d                	li	s2,-1
    22ae:	b7d9                	j	2274 <__swbuf_r+0x80>

000022b0 <__swsetup_r>:
    22b0:	1141                	addi	sp,sp,-16
    22b2:	80c18793          	addi	a5,gp,-2036 # 200000b4 <_impure_ptr>
    22b6:	c226                	sw	s1,4(sp)
    22b8:	4384                	lw	s1,0(a5)
    22ba:	c422                	sw	s0,8(sp)
    22bc:	c04a                	sw	s2,0(sp)
    22be:	c606                	sw	ra,12(sp)
    22c0:	892a                	mv	s2,a0
    22c2:	842e                	mv	s0,a1
    22c4:	c489                	beqz	s1,22ce <__swsetup_r+0x1e>
    22c6:	4c9c                	lw	a5,24(s1)
    22c8:	e399                	bnez	a5,22ce <__swsetup_r+0x1e>
    22ca:	8526                	mv	a0,s1
    22cc:	2635                	jal	25f8 <__sinit>
    22ce:	00001797          	auipc	a5,0x1
    22d2:	28e78793          	addi	a5,a5,654 # 355c <__sf_fake_stdin>
    22d6:	02f41b63          	bne	s0,a5,230c <__swsetup_r+0x5c>
    22da:	40c0                	lw	s0,4(s1)
    22dc:	00c41703          	lh	a4,12(s0)
    22e0:	01071793          	slli	a5,a4,0x10
    22e4:	83c1                	srli	a5,a5,0x10
    22e6:	0087f693          	andi	a3,a5,8
    22ea:	eaad                	bnez	a3,235c <__swsetup_r+0xac>
    22ec:	0107f693          	andi	a3,a5,16
    22f0:	ee95                	bnez	a3,232c <__swsetup_r+0x7c>
    22f2:	47a5                	li	a5,9
    22f4:	00f92023          	sw	a5,0(s2)
    22f8:	04076713          	ori	a4,a4,64
    22fc:	a45a                	sh	a4,12(s0)
    22fe:	557d                	li	a0,-1
    2300:	40b2                	lw	ra,12(sp)
    2302:	4422                	lw	s0,8(sp)
    2304:	4492                	lw	s1,4(sp)
    2306:	4902                	lw	s2,0(sp)
    2308:	0141                	addi	sp,sp,16
    230a:	8082                	ret
    230c:	00001797          	auipc	a5,0x1
    2310:	27078793          	addi	a5,a5,624 # 357c <__sf_fake_stdout>
    2314:	00f41463          	bne	s0,a5,231c <__swsetup_r+0x6c>
    2318:	4480                	lw	s0,8(s1)
    231a:	b7c9                	j	22dc <__swsetup_r+0x2c>
    231c:	00001797          	auipc	a5,0x1
    2320:	22078793          	addi	a5,a5,544 # 353c <__sf_fake_stderr>
    2324:	faf41ce3          	bne	s0,a5,22dc <__swsetup_r+0x2c>
    2328:	44c0                	lw	s0,12(s1)
    232a:	bf4d                	j	22dc <__swsetup_r+0x2c>
    232c:	8b91                	andi	a5,a5,4
    232e:	c39d                	beqz	a5,2354 <__swsetup_r+0xa4>
    2330:	584c                	lw	a1,52(s0)
    2332:	c989                	beqz	a1,2344 <__swsetup_r+0x94>
    2334:	04440793          	addi	a5,s0,68
    2338:	00f58463          	beq	a1,a5,2340 <__swsetup_r+0x90>
    233c:	854a                	mv	a0,s2
    233e:	2b29                	jal	2858 <_free_r>
    2340:	02042a23          	sw	zero,52(s0)
    2344:	245e                	lhu	a5,12(s0)
    2346:	00042223          	sw	zero,4(s0)
    234a:	fdb7f793          	andi	a5,a5,-37
    234e:	a45e                	sh	a5,12(s0)
    2350:	481c                	lw	a5,16(s0)
    2352:	c01c                	sw	a5,0(s0)
    2354:	245e                	lhu	a5,12(s0)
    2356:	0087e793          	ori	a5,a5,8
    235a:	a45e                	sh	a5,12(s0)
    235c:	481c                	lw	a5,16(s0)
    235e:	eb99                	bnez	a5,2374 <__swsetup_r+0xc4>
    2360:	245e                	lhu	a5,12(s0)
    2362:	20000713          	li	a4,512
    2366:	2807f793          	andi	a5,a5,640
    236a:	00e78563          	beq	a5,a4,2374 <__swsetup_r+0xc4>
    236e:	85a2                	mv	a1,s0
    2370:	854a                	mv	a0,s2
    2372:	2991                	jal	27c6 <__smakebuf_r>
    2374:	245e                	lhu	a5,12(s0)
    2376:	0017f713          	andi	a4,a5,1
    237a:	c31d                	beqz	a4,23a0 <__swsetup_r+0xf0>
    237c:	485c                	lw	a5,20(s0)
    237e:	00042423          	sw	zero,8(s0)
    2382:	40f007b3          	neg	a5,a5
    2386:	cc1c                	sw	a5,24(s0)
    2388:	481c                	lw	a5,16(s0)
    238a:	4501                	li	a0,0
    238c:	fbb5                	bnez	a5,2300 <__swsetup_r+0x50>
    238e:	00c41783          	lh	a5,12(s0)
    2392:	0807f713          	andi	a4,a5,128
    2396:	d72d                	beqz	a4,2300 <__swsetup_r+0x50>
    2398:	0407e793          	ori	a5,a5,64
    239c:	a45e                	sh	a5,12(s0)
    239e:	b785                	j	22fe <__swsetup_r+0x4e>
    23a0:	8b89                	andi	a5,a5,2
    23a2:	4701                	li	a4,0
    23a4:	e391                	bnez	a5,23a8 <__swsetup_r+0xf8>
    23a6:	4858                	lw	a4,20(s0)
    23a8:	c418                	sw	a4,8(s0)
    23aa:	bff9                	j	2388 <__swsetup_r+0xd8>

000023ac <__sflush_r>:
    23ac:	25de                	lhu	a5,12(a1)
    23ae:	1101                	addi	sp,sp,-32
    23b0:	cc22                	sw	s0,24(sp)
    23b2:	ca26                	sw	s1,20(sp)
    23b4:	ce06                	sw	ra,28(sp)
    23b6:	c84a                	sw	s2,16(sp)
    23b8:	c64e                	sw	s3,12(sp)
    23ba:	0087f713          	andi	a4,a5,8
    23be:	84aa                	mv	s1,a0
    23c0:	842e                	mv	s0,a1
    23c2:	eb79                	bnez	a4,2498 <__sflush_r+0xec>
    23c4:	41d8                	lw	a4,4(a1)
    23c6:	00e04d63          	bgtz	a4,23e0 <__sflush_r+0x34>
    23ca:	41b8                	lw	a4,64(a1)
    23cc:	00e04a63          	bgtz	a4,23e0 <__sflush_r+0x34>
    23d0:	4501                	li	a0,0
    23d2:	40f2                	lw	ra,28(sp)
    23d4:	4462                	lw	s0,24(sp)
    23d6:	44d2                	lw	s1,20(sp)
    23d8:	4942                	lw	s2,16(sp)
    23da:	49b2                	lw	s3,12(sp)
    23dc:	6105                	addi	sp,sp,32
    23de:	8082                	ret
    23e0:	5458                	lw	a4,44(s0)
    23e2:	d77d                	beqz	a4,23d0 <__sflush_r+0x24>
    23e4:	0004a903          	lw	s2,0(s1)
    23e8:	01379693          	slli	a3,a5,0x13
    23ec:	0004a023          	sw	zero,0(s1)
    23f0:	0606db63          	bgez	a3,2466 <__sflush_r+0xba>
    23f4:	4870                	lw	a2,84(s0)
    23f6:	245e                	lhu	a5,12(s0)
    23f8:	8b91                	andi	a5,a5,4
    23fa:	c799                	beqz	a5,2408 <__sflush_r+0x5c>
    23fc:	405c                	lw	a5,4(s0)
    23fe:	8e1d                	sub	a2,a2,a5
    2400:	585c                	lw	a5,52(s0)
    2402:	c399                	beqz	a5,2408 <__sflush_r+0x5c>
    2404:	403c                	lw	a5,64(s0)
    2406:	8e1d                	sub	a2,a2,a5
    2408:	545c                	lw	a5,44(s0)
    240a:	500c                	lw	a1,32(s0)
    240c:	4681                	li	a3,0
    240e:	8526                	mv	a0,s1
    2410:	9782                	jalr	a5
    2412:	57fd                	li	a5,-1
    2414:	245a                	lhu	a4,12(s0)
    2416:	00f51d63          	bne	a0,a5,2430 <__sflush_r+0x84>
    241a:	4094                	lw	a3,0(s1)
    241c:	47f5                	li	a5,29
    241e:	06d7e863          	bltu	a5,a3,248e <__sflush_r+0xe2>
    2422:	204007b7          	lui	a5,0x20400
    2426:	0785                	addi	a5,a5,1
    2428:	00d7d7b3          	srl	a5,a5,a3
    242c:	8b85                	andi	a5,a5,1
    242e:	c3a5                	beqz	a5,248e <__sflush_r+0xe2>
    2430:	481c                	lw	a5,16(s0)
    2432:	00042223          	sw	zero,4(s0)
    2436:	c01c                	sw	a5,0(s0)
    2438:	01371793          	slli	a5,a4,0x13
    243c:	0007d863          	bgez	a5,244c <__sflush_r+0xa0>
    2440:	57fd                	li	a5,-1
    2442:	00f51463          	bne	a0,a5,244a <__sflush_r+0x9e>
    2446:	409c                	lw	a5,0(s1)
    2448:	e391                	bnez	a5,244c <__sflush_r+0xa0>
    244a:	c868                	sw	a0,84(s0)
    244c:	584c                	lw	a1,52(s0)
    244e:	0124a023          	sw	s2,0(s1)
    2452:	ddbd                	beqz	a1,23d0 <__sflush_r+0x24>
    2454:	04440793          	addi	a5,s0,68
    2458:	00f58463          	beq	a1,a5,2460 <__sflush_r+0xb4>
    245c:	8526                	mv	a0,s1
    245e:	2eed                	jal	2858 <_free_r>
    2460:	02042a23          	sw	zero,52(s0)
    2464:	b7b5                	j	23d0 <__sflush_r+0x24>
    2466:	500c                	lw	a1,32(s0)
    2468:	4601                	li	a2,0
    246a:	4685                	li	a3,1
    246c:	8526                	mv	a0,s1
    246e:	9702                	jalr	a4
    2470:	57fd                	li	a5,-1
    2472:	862a                	mv	a2,a0
    2474:	f8f511e3          	bne	a0,a5,23f6 <__sflush_r+0x4a>
    2478:	409c                	lw	a5,0(s1)
    247a:	dfb5                	beqz	a5,23f6 <__sflush_r+0x4a>
    247c:	4775                	li	a4,29
    247e:	00e78563          	beq	a5,a4,2488 <__sflush_r+0xdc>
    2482:	4759                	li	a4,22
    2484:	04e79363          	bne	a5,a4,24ca <__sflush_r+0x11e>
    2488:	0124a023          	sw	s2,0(s1)
    248c:	b791                	j	23d0 <__sflush_r+0x24>
    248e:	04076713          	ori	a4,a4,64
    2492:	a45a                	sh	a4,12(s0)
    2494:	557d                	li	a0,-1
    2496:	bf35                	j	23d2 <__sflush_r+0x26>
    2498:	0105a983          	lw	s3,16(a1)
    249c:	f2098ae3          	beqz	s3,23d0 <__sflush_r+0x24>
    24a0:	0005a903          	lw	s2,0(a1)
    24a4:	8b8d                	andi	a5,a5,3
    24a6:	0135a023          	sw	s3,0(a1)
    24aa:	41390933          	sub	s2,s2,s3
    24ae:	4701                	li	a4,0
    24b0:	e391                	bnez	a5,24b4 <__sflush_r+0x108>
    24b2:	49d8                	lw	a4,20(a1)
    24b4:	c418                	sw	a4,8(s0)
    24b6:	f1205de3          	blez	s2,23d0 <__sflush_r+0x24>
    24ba:	541c                	lw	a5,40(s0)
    24bc:	500c                	lw	a1,32(s0)
    24be:	86ca                	mv	a3,s2
    24c0:	864e                	mv	a2,s3
    24c2:	8526                	mv	a0,s1
    24c4:	9782                	jalr	a5
    24c6:	00a04763          	bgtz	a0,24d4 <__sflush_r+0x128>
    24ca:	245e                	lhu	a5,12(s0)
    24cc:	0407e793          	ori	a5,a5,64
    24d0:	a45e                	sh	a5,12(s0)
    24d2:	b7c9                	j	2494 <__sflush_r+0xe8>
    24d4:	99aa                	add	s3,s3,a0
    24d6:	40a90933          	sub	s2,s2,a0
    24da:	bff1                	j	24b6 <__sflush_r+0x10a>

000024dc <_fflush_r>:
    24dc:	499c                	lw	a5,16(a1)
    24de:	c3a5                	beqz	a5,253e <_fflush_r+0x62>
    24e0:	1101                	addi	sp,sp,-32
    24e2:	cc22                	sw	s0,24(sp)
    24e4:	ce06                	sw	ra,28(sp)
    24e6:	842a                	mv	s0,a0
    24e8:	c511                	beqz	a0,24f4 <_fflush_r+0x18>
    24ea:	4d1c                	lw	a5,24(a0)
    24ec:	e781                	bnez	a5,24f4 <_fflush_r+0x18>
    24ee:	c62e                	sw	a1,12(sp)
    24f0:	2221                	jal	25f8 <__sinit>
    24f2:	45b2                	lw	a1,12(sp)
    24f4:	00001797          	auipc	a5,0x1
    24f8:	06878793          	addi	a5,a5,104 # 355c <__sf_fake_stdin>
    24fc:	00f59c63          	bne	a1,a5,2514 <_fflush_r+0x38>
    2500:	404c                	lw	a1,4(s0)
    2502:	00c59783          	lh	a5,12(a1)
    2506:	c79d                	beqz	a5,2534 <_fflush_r+0x58>
    2508:	8522                	mv	a0,s0
    250a:	4462                	lw	s0,24(sp)
    250c:	40f2                	lw	ra,28(sp)
    250e:	6105                	addi	sp,sp,32
    2510:	e9dff06f          	j	23ac <__sflush_r>
    2514:	00001797          	auipc	a5,0x1
    2518:	06878793          	addi	a5,a5,104 # 357c <__sf_fake_stdout>
    251c:	00f59463          	bne	a1,a5,2524 <_fflush_r+0x48>
    2520:	440c                	lw	a1,8(s0)
    2522:	b7c5                	j	2502 <_fflush_r+0x26>
    2524:	00001797          	auipc	a5,0x1
    2528:	01878793          	addi	a5,a5,24 # 353c <__sf_fake_stderr>
    252c:	fcf59be3          	bne	a1,a5,2502 <_fflush_r+0x26>
    2530:	444c                	lw	a1,12(s0)
    2532:	bfc1                	j	2502 <_fflush_r+0x26>
    2534:	40f2                	lw	ra,28(sp)
    2536:	4462                	lw	s0,24(sp)
    2538:	4501                	li	a0,0
    253a:	6105                	addi	sp,sp,32
    253c:	8082                	ret
    253e:	4501                	li	a0,0
    2540:	8082                	ret

00002542 <std>:
    2542:	1141                	addi	sp,sp,-16
    2544:	c422                	sw	s0,8(sp)
    2546:	c606                	sw	ra,12(sp)
    2548:	842a                	mv	s0,a0
    254a:	a54e                	sh	a1,12(a0)
    254c:	a572                	sh	a2,14(a0)
    254e:	00052023          	sw	zero,0(a0)
    2552:	00052223          	sw	zero,4(a0)
    2556:	00052423          	sw	zero,8(a0)
    255a:	06052223          	sw	zero,100(a0)
    255e:	00052823          	sw	zero,16(a0)
    2562:	00052a23          	sw	zero,20(a0)
    2566:	00052c23          	sw	zero,24(a0)
    256a:	4621                	li	a2,8
    256c:	4581                	li	a1,0
    256e:	05c50513          	addi	a0,a0,92
    2572:	caffd0ef          	jal	ra,220 <memset>
    2576:	00001797          	auipc	a5,0x1
    257a:	b2878793          	addi	a5,a5,-1240 # 309e <__sread>
    257e:	d05c                	sw	a5,36(s0)
    2580:	00001797          	auipc	a5,0x1
    2584:	b4a78793          	addi	a5,a5,-1206 # 30ca <__swrite>
    2588:	d41c                	sw	a5,40(s0)
    258a:	00001797          	auipc	a5,0x1
    258e:	b8878793          	addi	a5,a5,-1144 # 3112 <__sseek>
    2592:	d45c                	sw	a5,44(s0)
    2594:	00001797          	auipc	a5,0x1
    2598:	bae78793          	addi	a5,a5,-1106 # 3142 <__sclose>
    259c:	d000                	sw	s0,32(s0)
    259e:	d81c                	sw	a5,48(s0)
    25a0:	40b2                	lw	ra,12(sp)
    25a2:	4422                	lw	s0,8(sp)
    25a4:	0141                	addi	sp,sp,16
    25a6:	8082                	ret

000025a8 <_cleanup_r>:
    25a8:	00000597          	auipc	a1,0x0
    25ac:	f3458593          	addi	a1,a1,-204 # 24dc <_fflush_r>
    25b0:	aa91                	j	2704 <_fwalk_reent>

000025b2 <__sfmoreglue>:
    25b2:	1141                	addi	sp,sp,-16
    25b4:	c226                	sw	s1,4(sp)
    25b6:	06800613          	li	a2,104
    25ba:	fff58493          	addi	s1,a1,-1
    25be:	02c484b3          	mul	s1,s1,a2
    25c2:	c04a                	sw	s2,0(sp)
    25c4:	892e                	mv	s2,a1
    25c6:	c422                	sw	s0,8(sp)
    25c8:	c606                	sw	ra,12(sp)
    25ca:	07448593          	addi	a1,s1,116
    25ce:	2e0d                	jal	2900 <_malloc_r>
    25d0:	842a                	mv	s0,a0
    25d2:	cd01                	beqz	a0,25ea <__sfmoreglue+0x38>
    25d4:	00052023          	sw	zero,0(a0)
    25d8:	01252223          	sw	s2,4(a0)
    25dc:	0531                	addi	a0,a0,12
    25de:	c408                	sw	a0,8(s0)
    25e0:	06848613          	addi	a2,s1,104
    25e4:	4581                	li	a1,0
    25e6:	c3bfd0ef          	jal	ra,220 <memset>
    25ea:	8522                	mv	a0,s0
    25ec:	40b2                	lw	ra,12(sp)
    25ee:	4422                	lw	s0,8(sp)
    25f0:	4492                	lw	s1,4(sp)
    25f2:	4902                	lw	s2,0(sp)
    25f4:	0141                	addi	sp,sp,16
    25f6:	8082                	ret

000025f8 <__sinit>:
    25f8:	4d1c                	lw	a5,24(a0)
    25fa:	e7a5                	bnez	a5,2662 <__sinit+0x6a>
    25fc:	1141                	addi	sp,sp,-16
    25fe:	c606                	sw	ra,12(sp)
    2600:	c422                	sw	s0,8(sp)
    2602:	00000797          	auipc	a5,0x0
    2606:	fa678793          	addi	a5,a5,-90 # 25a8 <_cleanup_r>
    260a:	d51c                	sw	a5,40(a0)
    260c:	81018793          	addi	a5,gp,-2032 # 200000b8 <_global_impure_ptr>
    2610:	439c                	lw	a5,0(a5)
    2612:	04052423          	sw	zero,72(a0)
    2616:	04052623          	sw	zero,76(a0)
    261a:	04052823          	sw	zero,80(a0)
    261e:	00f51463          	bne	a0,a5,2626 <__sinit+0x2e>
    2622:	4785                	li	a5,1
    2624:	cd1c                	sw	a5,24(a0)
    2626:	842a                	mv	s0,a0
    2628:	2835                	jal	2664 <__sfp>
    262a:	c048                	sw	a0,4(s0)
    262c:	8522                	mv	a0,s0
    262e:	281d                	jal	2664 <__sfp>
    2630:	c408                	sw	a0,8(s0)
    2632:	8522                	mv	a0,s0
    2634:	2805                	jal	2664 <__sfp>
    2636:	c448                	sw	a0,12(s0)
    2638:	4048                	lw	a0,4(s0)
    263a:	4601                	li	a2,0
    263c:	4591                	li	a1,4
    263e:	f05ff0ef          	jal	ra,2542 <std>
    2642:	4408                	lw	a0,8(s0)
    2644:	4605                	li	a2,1
    2646:	45a5                	li	a1,9
    2648:	efbff0ef          	jal	ra,2542 <std>
    264c:	4448                	lw	a0,12(s0)
    264e:	4609                	li	a2,2
    2650:	45c9                	li	a1,18
    2652:	ef1ff0ef          	jal	ra,2542 <std>
    2656:	4785                	li	a5,1
    2658:	cc1c                	sw	a5,24(s0)
    265a:	40b2                	lw	ra,12(sp)
    265c:	4422                	lw	s0,8(sp)
    265e:	0141                	addi	sp,sp,16
    2660:	8082                	ret
    2662:	8082                	ret

00002664 <__sfp>:
    2664:	1141                	addi	sp,sp,-16
    2666:	81018793          	addi	a5,gp,-2032 # 200000b8 <_global_impure_ptr>
    266a:	c226                	sw	s1,4(sp)
    266c:	4384                	lw	s1,0(a5)
    266e:	c04a                	sw	s2,0(sp)
    2670:	c606                	sw	ra,12(sp)
    2672:	4c9c                	lw	a5,24(s1)
    2674:	c422                	sw	s0,8(sp)
    2676:	892a                	mv	s2,a0
    2678:	e781                	bnez	a5,2680 <__sfp+0x1c>
    267a:	8526                	mv	a0,s1
    267c:	f7dff0ef          	jal	ra,25f8 <__sinit>
    2680:	04848493          	addi	s1,s1,72
    2684:	4480                	lw	s0,8(s1)
    2686:	40dc                	lw	a5,4(s1)
    2688:	17fd                	addi	a5,a5,-1
    268a:	0007d663          	bgez	a5,2696 <__sfp+0x32>
    268e:	409c                	lw	a5,0(s1)
    2690:	cfb9                	beqz	a5,26ee <__sfp+0x8a>
    2692:	4084                	lw	s1,0(s1)
    2694:	bfc5                	j	2684 <__sfp+0x20>
    2696:	00c41703          	lh	a4,12(s0)
    269a:	e739                	bnez	a4,26e8 <__sfp+0x84>
    269c:	77c1                	lui	a5,0xffff0
    269e:	0785                	addi	a5,a5,1
    26a0:	06042223          	sw	zero,100(s0)
    26a4:	00042023          	sw	zero,0(s0)
    26a8:	00042223          	sw	zero,4(s0)
    26ac:	00042423          	sw	zero,8(s0)
    26b0:	c45c                	sw	a5,12(s0)
    26b2:	00042823          	sw	zero,16(s0)
    26b6:	00042a23          	sw	zero,20(s0)
    26ba:	00042c23          	sw	zero,24(s0)
    26be:	4621                	li	a2,8
    26c0:	4581                	li	a1,0
    26c2:	05c40513          	addi	a0,s0,92
    26c6:	b5bfd0ef          	jal	ra,220 <memset>
    26ca:	02042a23          	sw	zero,52(s0)
    26ce:	02042c23          	sw	zero,56(s0)
    26d2:	04042423          	sw	zero,72(s0)
    26d6:	04042623          	sw	zero,76(s0)
    26da:	8522                	mv	a0,s0
    26dc:	40b2                	lw	ra,12(sp)
    26de:	4422                	lw	s0,8(sp)
    26e0:	4492                	lw	s1,4(sp)
    26e2:	4902                	lw	s2,0(sp)
    26e4:	0141                	addi	sp,sp,16
    26e6:	8082                	ret
    26e8:	06840413          	addi	s0,s0,104
    26ec:	bf71                	j	2688 <__sfp+0x24>
    26ee:	4591                	li	a1,4
    26f0:	854a                	mv	a0,s2
    26f2:	ec1ff0ef          	jal	ra,25b2 <__sfmoreglue>
    26f6:	c088                	sw	a0,0(s1)
    26f8:	fd49                	bnez	a0,2692 <__sfp+0x2e>
    26fa:	47b1                	li	a5,12
    26fc:	00f92023          	sw	a5,0(s2)
    2700:	4401                	li	s0,0
    2702:	bfe1                	j	26da <__sfp+0x76>

00002704 <_fwalk_reent>:
    2704:	7179                	addi	sp,sp,-48
    2706:	d422                	sw	s0,40(sp)
    2708:	d04a                	sw	s2,32(sp)
    270a:	cc52                	sw	s4,24(sp)
    270c:	ca56                	sw	s5,20(sp)
    270e:	c85a                	sw	s6,16(sp)
    2710:	c65e                	sw	s7,12(sp)
    2712:	d606                	sw	ra,44(sp)
    2714:	d226                	sw	s1,36(sp)
    2716:	ce4e                	sw	s3,28(sp)
    2718:	8a2a                	mv	s4,a0
    271a:	8aae                	mv	s5,a1
    271c:	04850413          	addi	s0,a0,72
    2720:	4901                	li	s2,0
    2722:	4b05                	li	s6,1
    2724:	5bfd                	li	s7,-1
    2726:	ec09                	bnez	s0,2740 <_fwalk_reent+0x3c>
    2728:	50b2                	lw	ra,44(sp)
    272a:	5422                	lw	s0,40(sp)
    272c:	854a                	mv	a0,s2
    272e:	5492                	lw	s1,36(sp)
    2730:	5902                	lw	s2,32(sp)
    2732:	49f2                	lw	s3,28(sp)
    2734:	4a62                	lw	s4,24(sp)
    2736:	4ad2                	lw	s5,20(sp)
    2738:	4b42                	lw	s6,16(sp)
    273a:	4bb2                	lw	s7,12(sp)
    273c:	6145                	addi	sp,sp,48
    273e:	8082                	ret
    2740:	4404                	lw	s1,8(s0)
    2742:	00442983          	lw	s3,4(s0)
    2746:	19fd                	addi	s3,s3,-1
    2748:	0009d463          	bgez	s3,2750 <_fwalk_reent+0x4c>
    274c:	4000                	lw	s0,0(s0)
    274e:	bfe1                	j	2726 <_fwalk_reent+0x22>
    2750:	24de                	lhu	a5,12(s1)
    2752:	00fb7b63          	bgeu	s6,a5,2768 <_fwalk_reent+0x64>
    2756:	00e49783          	lh	a5,14(s1)
    275a:	01778763          	beq	a5,s7,2768 <_fwalk_reent+0x64>
    275e:	85a6                	mv	a1,s1
    2760:	8552                	mv	a0,s4
    2762:	9a82                	jalr	s5
    2764:	00a96933          	or	s2,s2,a0
    2768:	06848493          	addi	s1,s1,104
    276c:	bfe9                	j	2746 <_fwalk_reent+0x42>

0000276e <__swhatbuf_r>:
    276e:	7119                	addi	sp,sp,-128
    2770:	daa6                	sw	s1,116(sp)
    2772:	84ae                	mv	s1,a1
    2774:	00e59583          	lh	a1,14(a1)
    2778:	dca2                	sw	s0,120(sp)
    277a:	de86                	sw	ra,124(sp)
    277c:	8432                	mv	s0,a2
    277e:	0005db63          	bgez	a1,2794 <__swhatbuf_r+0x26>
    2782:	24de                	lhu	a5,12(s1)
    2784:	0006a023          	sw	zero,0(a3) # fc000000 <_eusrstack+0xdbff8000>
    2788:	0807f793          	andi	a5,a5,128
    278c:	e785                	bnez	a5,27b4 <__swhatbuf_r+0x46>
    278e:	40000793          	li	a5,1024
    2792:	a01d                	j	27b8 <__swhatbuf_r+0x4a>
    2794:	0830                	addi	a2,sp,24
    2796:	c636                	sw	a3,12(sp)
    2798:	207000ef          	jal	ra,319e <_fstat_r>
    279c:	46b2                	lw	a3,12(sp)
    279e:	fe0542e3          	bltz	a0,2782 <__swhatbuf_r+0x14>
    27a2:	4772                	lw	a4,28(sp)
    27a4:	67bd                	lui	a5,0xf
    27a6:	8ff9                	and	a5,a5,a4
    27a8:	7779                	lui	a4,0xffffe
    27aa:	97ba                	add	a5,a5,a4
    27ac:	0017b793          	seqz	a5,a5
    27b0:	c29c                	sw	a5,0(a3)
    27b2:	bff1                	j	278e <__swhatbuf_r+0x20>
    27b4:	04000793          	li	a5,64
    27b8:	c01c                	sw	a5,0(s0)
    27ba:	50f6                	lw	ra,124(sp)
    27bc:	5466                	lw	s0,120(sp)
    27be:	54d6                	lw	s1,116(sp)
    27c0:	4501                	li	a0,0
    27c2:	6109                	addi	sp,sp,128
    27c4:	8082                	ret

000027c6 <__smakebuf_r>:
    27c6:	25de                	lhu	a5,12(a1)
    27c8:	1101                	addi	sp,sp,-32
    27ca:	cc22                	sw	s0,24(sp)
    27cc:	ce06                	sw	ra,28(sp)
    27ce:	ca26                	sw	s1,20(sp)
    27d0:	c84a                	sw	s2,16(sp)
    27d2:	8b89                	andi	a5,a5,2
    27d4:	842e                	mv	s0,a1
    27d6:	cf89                	beqz	a5,27f0 <__smakebuf_r+0x2a>
    27d8:	04740793          	addi	a5,s0,71
    27dc:	c01c                	sw	a5,0(s0)
    27de:	c81c                	sw	a5,16(s0)
    27e0:	4785                	li	a5,1
    27e2:	c85c                	sw	a5,20(s0)
    27e4:	40f2                	lw	ra,28(sp)
    27e6:	4462                	lw	s0,24(sp)
    27e8:	44d2                	lw	s1,20(sp)
    27ea:	4942                	lw	s2,16(sp)
    27ec:	6105                	addi	sp,sp,32
    27ee:	8082                	ret
    27f0:	0074                	addi	a3,sp,12
    27f2:	0030                	addi	a2,sp,8
    27f4:	84aa                	mv	s1,a0
    27f6:	f79ff0ef          	jal	ra,276e <__swhatbuf_r>
    27fa:	45a2                	lw	a1,8(sp)
    27fc:	892a                	mv	s2,a0
    27fe:	8526                	mv	a0,s1
    2800:	2201                	jal	2900 <_malloc_r>
    2802:	e919                	bnez	a0,2818 <__smakebuf_r+0x52>
    2804:	00c41783          	lh	a5,12(s0)
    2808:	2007f713          	andi	a4,a5,512
    280c:	ff61                	bnez	a4,27e4 <__smakebuf_r+0x1e>
    280e:	9bf1                	andi	a5,a5,-4
    2810:	0027e793          	ori	a5,a5,2
    2814:	a45e                	sh	a5,12(s0)
    2816:	b7c9                	j	27d8 <__smakebuf_r+0x12>
    2818:	00000797          	auipc	a5,0x0
    281c:	d9078793          	addi	a5,a5,-624 # 25a8 <_cleanup_r>
    2820:	d49c                	sw	a5,40(s1)
    2822:	245e                	lhu	a5,12(s0)
    2824:	c008                	sw	a0,0(s0)
    2826:	c808                	sw	a0,16(s0)
    2828:	0807e793          	ori	a5,a5,128
    282c:	a45e                	sh	a5,12(s0)
    282e:	47a2                	lw	a5,8(sp)
    2830:	c85c                	sw	a5,20(s0)
    2832:	47b2                	lw	a5,12(sp)
    2834:	cf81                	beqz	a5,284c <__smakebuf_r+0x86>
    2836:	00e41583          	lh	a1,14(s0)
    283a:	8526                	mv	a0,s1
    283c:	18d000ef          	jal	ra,31c8 <_isatty_r>
    2840:	c511                	beqz	a0,284c <__smakebuf_r+0x86>
    2842:	245e                	lhu	a5,12(s0)
    2844:	9bf1                	andi	a5,a5,-4
    2846:	0017e793          	ori	a5,a5,1
    284a:	a45e                	sh	a5,12(s0)
    284c:	245e                	lhu	a5,12(s0)
    284e:	00f96933          	or	s2,s2,a5
    2852:	01241623          	sh	s2,12(s0)
    2856:	b779                	j	27e4 <__smakebuf_r+0x1e>

00002858 <_free_r>:
    2858:	c1dd                	beqz	a1,28fe <_free_r+0xa6>
    285a:	ffc5a783          	lw	a5,-4(a1)
    285e:	1141                	addi	sp,sp,-16
    2860:	c422                	sw	s0,8(sp)
    2862:	c606                	sw	ra,12(sp)
    2864:	c226                	sw	s1,4(sp)
    2866:	ffc58413          	addi	s0,a1,-4
    286a:	0007d363          	bgez	a5,2870 <_free_r+0x18>
    286e:	943e                	add	s0,s0,a5
    2870:	84aa                	mv	s1,a0
    2872:	1c3000ef          	jal	ra,3234 <__malloc_lock>
    2876:	83018793          	addi	a5,gp,-2000 # 200000d8 <__malloc_free_list>
    287a:	439c                	lw	a5,0(a5)
    287c:	ef81                	bnez	a5,2894 <_free_r+0x3c>
    287e:	00042223          	sw	zero,4(s0)
    2882:	8281a823          	sw	s0,-2000(gp) # 200000d8 <__malloc_free_list>
    2886:	4422                	lw	s0,8(sp)
    2888:	40b2                	lw	ra,12(sp)
    288a:	8526                	mv	a0,s1
    288c:	4492                	lw	s1,4(sp)
    288e:	0141                	addi	sp,sp,16
    2890:	1a70006f          	j	3236 <__malloc_unlock>
    2894:	00f47e63          	bgeu	s0,a5,28b0 <_free_r+0x58>
    2898:	4014                	lw	a3,0(s0)
    289a:	00d40733          	add	a4,s0,a3
    289e:	00e79663          	bne	a5,a4,28aa <_free_r+0x52>
    28a2:	4398                	lw	a4,0(a5)
    28a4:	43dc                	lw	a5,4(a5)
    28a6:	9736                	add	a4,a4,a3
    28a8:	c018                	sw	a4,0(s0)
    28aa:	c05c                	sw	a5,4(s0)
    28ac:	bfd9                	j	2882 <_free_r+0x2a>
    28ae:	87ba                	mv	a5,a4
    28b0:	43d8                	lw	a4,4(a5)
    28b2:	c319                	beqz	a4,28b8 <_free_r+0x60>
    28b4:	fee47de3          	bgeu	s0,a4,28ae <_free_r+0x56>
    28b8:	4394                	lw	a3,0(a5)
    28ba:	00d78633          	add	a2,a5,a3
    28be:	00861f63          	bne	a2,s0,28dc <_free_r+0x84>
    28c2:	4010                	lw	a2,0(s0)
    28c4:	96b2                	add	a3,a3,a2
    28c6:	c394                	sw	a3,0(a5)
    28c8:	00d78633          	add	a2,a5,a3
    28cc:	fac71de3          	bne	a4,a2,2886 <_free_r+0x2e>
    28d0:	4310                	lw	a2,0(a4)
    28d2:	4358                	lw	a4,4(a4)
    28d4:	96b2                	add	a3,a3,a2
    28d6:	c394                	sw	a3,0(a5)
    28d8:	c3d8                	sw	a4,4(a5)
    28da:	b775                	j	2886 <_free_r+0x2e>
    28dc:	00c47563          	bgeu	s0,a2,28e6 <_free_r+0x8e>
    28e0:	47b1                	li	a5,12
    28e2:	c09c                	sw	a5,0(s1)
    28e4:	b74d                	j	2886 <_free_r+0x2e>
    28e6:	4010                	lw	a2,0(s0)
    28e8:	00c406b3          	add	a3,s0,a2
    28ec:	00d71663          	bne	a4,a3,28f8 <_free_r+0xa0>
    28f0:	4314                	lw	a3,0(a4)
    28f2:	4358                	lw	a4,4(a4)
    28f4:	96b2                	add	a3,a3,a2
    28f6:	c014                	sw	a3,0(s0)
    28f8:	c058                	sw	a4,4(s0)
    28fa:	c3c0                	sw	s0,4(a5)
    28fc:	b769                	j	2886 <_free_r+0x2e>
    28fe:	8082                	ret

00002900 <_malloc_r>:
    2900:	1101                	addi	sp,sp,-32
    2902:	ca26                	sw	s1,20(sp)
    2904:	00358493          	addi	s1,a1,3
    2908:	98f1                	andi	s1,s1,-4
    290a:	ce06                	sw	ra,28(sp)
    290c:	cc22                	sw	s0,24(sp)
    290e:	c84a                	sw	s2,16(sp)
    2910:	c64e                	sw	s3,12(sp)
    2912:	04a1                	addi	s1,s1,8
    2914:	47b1                	li	a5,12
    2916:	04f4f363          	bgeu	s1,a5,295c <_malloc_r+0x5c>
    291a:	44b1                	li	s1,12
    291c:	04b4e263          	bltu	s1,a1,2960 <_malloc_r+0x60>
    2920:	892a                	mv	s2,a0
    2922:	113000ef          	jal	ra,3234 <__malloc_lock>
    2926:	83018793          	addi	a5,gp,-2000 # 200000d8 <__malloc_free_list>
    292a:	4398                	lw	a4,0(a5)
    292c:	843a                	mv	s0,a4
    292e:	e039                	bnez	s0,2974 <_malloc_r+0x74>
    2930:	83418793          	addi	a5,gp,-1996 # 200000dc <__malloc_sbrk_start>
    2934:	439c                	lw	a5,0(a5)
    2936:	e791                	bnez	a5,2942 <_malloc_r+0x42>
    2938:	4581                	li	a1,0
    293a:	854a                	mv	a0,s2
    293c:	2f25                	jal	3074 <_sbrk_r>
    293e:	82a1aa23          	sw	a0,-1996(gp) # 200000dc <__malloc_sbrk_start>
    2942:	85a6                	mv	a1,s1
    2944:	854a                	mv	a0,s2
    2946:	273d                	jal	3074 <_sbrk_r>
    2948:	59fd                	li	s3,-1
    294a:	07351963          	bne	a0,s3,29bc <_malloc_r+0xbc>
    294e:	47b1                	li	a5,12
    2950:	00f92023          	sw	a5,0(s2)
    2954:	854a                	mv	a0,s2
    2956:	0e1000ef          	jal	ra,3236 <__malloc_unlock>
    295a:	a029                	j	2964 <_malloc_r+0x64>
    295c:	fc04d0e3          	bgez	s1,291c <_malloc_r+0x1c>
    2960:	47b1                	li	a5,12
    2962:	c11c                	sw	a5,0(a0)
    2964:	4501                	li	a0,0
    2966:	40f2                	lw	ra,28(sp)
    2968:	4462                	lw	s0,24(sp)
    296a:	44d2                	lw	s1,20(sp)
    296c:	4942                	lw	s2,16(sp)
    296e:	49b2                	lw	s3,12(sp)
    2970:	6105                	addi	sp,sp,32
    2972:	8082                	ret
    2974:	401c                	lw	a5,0(s0)
    2976:	8f85                	sub	a5,a5,s1
    2978:	0207cf63          	bltz	a5,29b6 <_malloc_r+0xb6>
    297c:	46ad                	li	a3,11
    297e:	00f6f663          	bgeu	a3,a5,298a <_malloc_r+0x8a>
    2982:	c01c                	sw	a5,0(s0)
    2984:	943e                	add	s0,s0,a5
    2986:	c004                	sw	s1,0(s0)
    2988:	a031                	j	2994 <_malloc_r+0x94>
    298a:	405c                	lw	a5,4(s0)
    298c:	02871363          	bne	a4,s0,29b2 <_malloc_r+0xb2>
    2990:	82f1a823          	sw	a5,-2000(gp) # 200000d8 <__malloc_free_list>
    2994:	854a                	mv	a0,s2
    2996:	0a1000ef          	jal	ra,3236 <__malloc_unlock>
    299a:	00b40513          	addi	a0,s0,11
    299e:	00440793          	addi	a5,s0,4
    29a2:	9961                	andi	a0,a0,-8
    29a4:	40f50733          	sub	a4,a0,a5
    29a8:	df5d                	beqz	a4,2966 <_malloc_r+0x66>
    29aa:	943a                	add	s0,s0,a4
    29ac:	8f89                	sub	a5,a5,a0
    29ae:	c01c                	sw	a5,0(s0)
    29b0:	bf5d                	j	2966 <_malloc_r+0x66>
    29b2:	c35c                	sw	a5,4(a4)
    29b4:	b7c5                	j	2994 <_malloc_r+0x94>
    29b6:	8722                	mv	a4,s0
    29b8:	4040                	lw	s0,4(s0)
    29ba:	bf95                	j	292e <_malloc_r+0x2e>
    29bc:	00350413          	addi	s0,a0,3
    29c0:	9871                	andi	s0,s0,-4
    29c2:	fc8502e3          	beq	a0,s0,2986 <_malloc_r+0x86>
    29c6:	40a405b3          	sub	a1,s0,a0
    29ca:	854a                	mv	a0,s2
    29cc:	2565                	jal	3074 <_sbrk_r>
    29ce:	fb351ce3          	bne	a0,s3,2986 <_malloc_r+0x86>
    29d2:	bfb5                	j	294e <_malloc_r+0x4e>

000029d4 <__sfputc_r>:
    29d4:	461c                	lw	a5,8(a2)
    29d6:	17fd                	addi	a5,a5,-1
    29d8:	c61c                	sw	a5,8(a2)
    29da:	0007da63          	bgez	a5,29ee <__sfputc_r+0x1a>
    29de:	4e18                	lw	a4,24(a2)
    29e0:	00e7c563          	blt	a5,a4,29ea <__sfputc_r+0x16>
    29e4:	47a9                	li	a5,10
    29e6:	00f59463          	bne	a1,a5,29ee <__sfputc_r+0x1a>
    29ea:	80bff06f          	j	21f4 <__swbuf_r>
    29ee:	421c                	lw	a5,0(a2)
    29f0:	852e                	mv	a0,a1
    29f2:	00178713          	addi	a4,a5,1
    29f6:	c218                	sw	a4,0(a2)
    29f8:	a38c                	sb	a1,0(a5)
    29fa:	8082                	ret

000029fc <__sfputs_r>:
    29fc:	1101                	addi	sp,sp,-32
    29fe:	cc22                	sw	s0,24(sp)
    2a00:	ca26                	sw	s1,20(sp)
    2a02:	c84a                	sw	s2,16(sp)
    2a04:	c64e                	sw	s3,12(sp)
    2a06:	c452                	sw	s4,8(sp)
    2a08:	ce06                	sw	ra,28(sp)
    2a0a:	892a                	mv	s2,a0
    2a0c:	89ae                	mv	s3,a1
    2a0e:	8432                	mv	s0,a2
    2a10:	00d604b3          	add	s1,a2,a3
    2a14:	5a7d                	li	s4,-1
    2a16:	00941463          	bne	s0,s1,2a1e <__sfputs_r+0x22>
    2a1a:	4501                	li	a0,0
    2a1c:	a809                	j	2a2e <__sfputs_r+0x32>
    2a1e:	200c                	lbu	a1,0(s0)
    2a20:	864e                	mv	a2,s3
    2a22:	854a                	mv	a0,s2
    2a24:	fb1ff0ef          	jal	ra,29d4 <__sfputc_r>
    2a28:	0405                	addi	s0,s0,1
    2a2a:	ff4516e3          	bne	a0,s4,2a16 <__sfputs_r+0x1a>
    2a2e:	40f2                	lw	ra,28(sp)
    2a30:	4462                	lw	s0,24(sp)
    2a32:	44d2                	lw	s1,20(sp)
    2a34:	4942                	lw	s2,16(sp)
    2a36:	49b2                	lw	s3,12(sp)
    2a38:	4a22                	lw	s4,8(sp)
    2a3a:	6105                	addi	sp,sp,32
    2a3c:	8082                	ret

00002a3e <_vfiprintf_r>:
    2a3e:	7135                	addi	sp,sp,-160
    2a40:	cd22                	sw	s0,152(sp)
    2a42:	cb26                	sw	s1,148(sp)
    2a44:	c94a                	sw	s2,144(sp)
    2a46:	c74e                	sw	s3,140(sp)
    2a48:	cf06                	sw	ra,156(sp)
    2a4a:	c552                	sw	s4,136(sp)
    2a4c:	c356                	sw	s5,132(sp)
    2a4e:	c15a                	sw	s6,128(sp)
    2a50:	dede                	sw	s7,124(sp)
    2a52:	dce2                	sw	s8,120(sp)
    2a54:	dae6                	sw	s9,116(sp)
    2a56:	89aa                	mv	s3,a0
    2a58:	84ae                	mv	s1,a1
    2a5a:	8932                	mv	s2,a2
    2a5c:	8436                	mv	s0,a3
    2a5e:	c509                	beqz	a0,2a68 <_vfiprintf_r+0x2a>
    2a60:	4d1c                	lw	a5,24(a0)
    2a62:	e399                	bnez	a5,2a68 <_vfiprintf_r+0x2a>
    2a64:	b95ff0ef          	jal	ra,25f8 <__sinit>
    2a68:	00001797          	auipc	a5,0x1
    2a6c:	af478793          	addi	a5,a5,-1292 # 355c <__sf_fake_stdin>
    2a70:	0cf49863          	bne	s1,a5,2b40 <_vfiprintf_r+0x102>
    2a74:	0049a483          	lw	s1,4(s3)
    2a78:	24de                	lhu	a5,12(s1)
    2a7a:	8ba1                	andi	a5,a5,8
    2a7c:	c7e5                	beqz	a5,2b64 <_vfiprintf_r+0x126>
    2a7e:	489c                	lw	a5,16(s1)
    2a80:	c3f5                	beqz	a5,2b64 <_vfiprintf_r+0x126>
    2a82:	02000793          	li	a5,32
    2a86:	02f104a3          	sb	a5,41(sp)
    2a8a:	03000793          	li	a5,48
    2a8e:	d202                	sw	zero,36(sp)
    2a90:	02f10523          	sb	a5,42(sp)
    2a94:	c622                	sw	s0,12(sp)
    2a96:	02500b93          	li	s7,37
    2a9a:	00001a97          	auipc	s5,0x1
    2a9e:	b02a8a93          	addi	s5,s5,-1278 # 359c <__sf_fake_stdout+0x20>
    2aa2:	4c05                	li	s8,1
    2aa4:	4b29                	li	s6,10
    2aa6:	844a                	mv	s0,s2
    2aa8:	201c                	lbu	a5,0(s0)
    2aaa:	c399                	beqz	a5,2ab0 <_vfiprintf_r+0x72>
    2aac:	0d779f63          	bne	a5,s7,2b8a <_vfiprintf_r+0x14c>
    2ab0:	41240cb3          	sub	s9,s0,s2
    2ab4:	000c8e63          	beqz	s9,2ad0 <_vfiprintf_r+0x92>
    2ab8:	86e6                	mv	a3,s9
    2aba:	864a                	mv	a2,s2
    2abc:	85a6                	mv	a1,s1
    2abe:	854e                	mv	a0,s3
    2ac0:	f3dff0ef          	jal	ra,29fc <__sfputs_r>
    2ac4:	57fd                	li	a5,-1
    2ac6:	1cf50f63          	beq	a0,a5,2ca4 <_vfiprintf_r+0x266>
    2aca:	5692                	lw	a3,36(sp)
    2acc:	96e6                	add	a3,a3,s9
    2ace:	d236                	sw	a3,36(sp)
    2ad0:	201c                	lbu	a5,0(s0)
    2ad2:	1c078963          	beqz	a5,2ca4 <_vfiprintf_r+0x266>
    2ad6:	57fd                	li	a5,-1
    2ad8:	00140913          	addi	s2,s0,1
    2adc:	c802                	sw	zero,16(sp)
    2ade:	ce02                	sw	zero,28(sp)
    2ae0:	ca3e                	sw	a5,20(sp)
    2ae2:	cc02                	sw	zero,24(sp)
    2ae4:	040109a3          	sb	zero,83(sp)
    2ae8:	d482                	sw	zero,104(sp)
    2aea:	00094583          	lbu	a1,0(s2)
    2aee:	4615                	li	a2,5
    2af0:	8556                	mv	a0,s5
    2af2:	272d                	jal	321c <memchr>
    2af4:	00190413          	addi	s0,s2,1
    2af8:	47c2                	lw	a5,16(sp)
    2afa:	e951                	bnez	a0,2b8e <_vfiprintf_r+0x150>
    2afc:	0107f713          	andi	a4,a5,16
    2b00:	c709                	beqz	a4,2b0a <_vfiprintf_r+0xcc>
    2b02:	02000713          	li	a4,32
    2b06:	04e109a3          	sb	a4,83(sp)
    2b0a:	0087f713          	andi	a4,a5,8
    2b0e:	c709                	beqz	a4,2b18 <_vfiprintf_r+0xda>
    2b10:	02b00713          	li	a4,43
    2b14:	04e109a3          	sb	a4,83(sp)
    2b18:	00094683          	lbu	a3,0(s2)
    2b1c:	02a00713          	li	a4,42
    2b20:	06e68f63          	beq	a3,a4,2b9e <_vfiprintf_r+0x160>
    2b24:	47f2                	lw	a5,28(sp)
    2b26:	844a                	mv	s0,s2
    2b28:	4681                	li	a3,0
    2b2a:	4625                	li	a2,9
    2b2c:	2018                	lbu	a4,0(s0)
    2b2e:	00140593          	addi	a1,s0,1
    2b32:	fd070713          	addi	a4,a4,-48 # ffffdfd0 <_eusrstack+0xdfff5fd0>
    2b36:	0ae67763          	bgeu	a2,a4,2be4 <_vfiprintf_r+0x1a6>
    2b3a:	cab5                	beqz	a3,2bae <_vfiprintf_r+0x170>
    2b3c:	ce3e                	sw	a5,28(sp)
    2b3e:	a885                	j	2bae <_vfiprintf_r+0x170>
    2b40:	00001797          	auipc	a5,0x1
    2b44:	a3c78793          	addi	a5,a5,-1476 # 357c <__sf_fake_stdout>
    2b48:	00f49563          	bne	s1,a5,2b52 <_vfiprintf_r+0x114>
    2b4c:	0089a483          	lw	s1,8(s3)
    2b50:	b725                	j	2a78 <_vfiprintf_r+0x3a>
    2b52:	00001797          	auipc	a5,0x1
    2b56:	9ea78793          	addi	a5,a5,-1558 # 353c <__sf_fake_stderr>
    2b5a:	f0f49fe3          	bne	s1,a5,2a78 <_vfiprintf_r+0x3a>
    2b5e:	00c9a483          	lw	s1,12(s3)
    2b62:	bf19                	j	2a78 <_vfiprintf_r+0x3a>
    2b64:	85a6                	mv	a1,s1
    2b66:	854e                	mv	a0,s3
    2b68:	f48ff0ef          	jal	ra,22b0 <__swsetup_r>
    2b6c:	d919                	beqz	a0,2a82 <_vfiprintf_r+0x44>
    2b6e:	557d                	li	a0,-1
    2b70:	40fa                	lw	ra,156(sp)
    2b72:	446a                	lw	s0,152(sp)
    2b74:	44da                	lw	s1,148(sp)
    2b76:	494a                	lw	s2,144(sp)
    2b78:	49ba                	lw	s3,140(sp)
    2b7a:	4a2a                	lw	s4,136(sp)
    2b7c:	4a9a                	lw	s5,132(sp)
    2b7e:	4b0a                	lw	s6,128(sp)
    2b80:	5bf6                	lw	s7,124(sp)
    2b82:	5c66                	lw	s8,120(sp)
    2b84:	5cd6                	lw	s9,116(sp)
    2b86:	610d                	addi	sp,sp,160
    2b88:	8082                	ret
    2b8a:	0405                	addi	s0,s0,1
    2b8c:	bf31                	j	2aa8 <_vfiprintf_r+0x6a>
    2b8e:	41550533          	sub	a0,a0,s5
    2b92:	00ac1533          	sll	a0,s8,a0
    2b96:	8fc9                	or	a5,a5,a0
    2b98:	c83e                	sw	a5,16(sp)
    2b9a:	8922                	mv	s2,s0
    2b9c:	b7b9                	j	2aea <_vfiprintf_r+0xac>
    2b9e:	4732                	lw	a4,12(sp)
    2ba0:	00470693          	addi	a3,a4,4
    2ba4:	4318                	lw	a4,0(a4)
    2ba6:	c636                	sw	a3,12(sp)
    2ba8:	02074763          	bltz	a4,2bd6 <_vfiprintf_r+0x198>
    2bac:	ce3a                	sw	a4,28(sp)
    2bae:	2018                	lbu	a4,0(s0)
    2bb0:	02e00793          	li	a5,46
    2bb4:	04f71d63          	bne	a4,a5,2c0e <_vfiprintf_r+0x1d0>
    2bb8:	3018                	lbu	a4,1(s0)
    2bba:	02a00793          	li	a5,42
    2bbe:	02f71b63          	bne	a4,a5,2bf4 <_vfiprintf_r+0x1b6>
    2bc2:	47b2                	lw	a5,12(sp)
    2bc4:	0409                	addi	s0,s0,2
    2bc6:	00478713          	addi	a4,a5,4
    2bca:	439c                	lw	a5,0(a5)
    2bcc:	c63a                	sw	a4,12(sp)
    2bce:	0207c163          	bltz	a5,2bf0 <_vfiprintf_r+0x1b2>
    2bd2:	ca3e                	sw	a5,20(sp)
    2bd4:	a82d                	j	2c0e <_vfiprintf_r+0x1d0>
    2bd6:	40e00733          	neg	a4,a4
    2bda:	0027e793          	ori	a5,a5,2
    2bde:	ce3a                	sw	a4,28(sp)
    2be0:	c83e                	sw	a5,16(sp)
    2be2:	b7f1                	j	2bae <_vfiprintf_r+0x170>
    2be4:	036787b3          	mul	a5,a5,s6
    2be8:	4685                	li	a3,1
    2bea:	842e                	mv	s0,a1
    2bec:	97ba                	add	a5,a5,a4
    2bee:	bf3d                	j	2b2c <_vfiprintf_r+0xee>
    2bf0:	57fd                	li	a5,-1
    2bf2:	b7c5                	j	2bd2 <_vfiprintf_r+0x194>
    2bf4:	0405                	addi	s0,s0,1
    2bf6:	ca02                	sw	zero,20(sp)
    2bf8:	4681                	li	a3,0
    2bfa:	4781                	li	a5,0
    2bfc:	4625                	li	a2,9
    2bfe:	2018                	lbu	a4,0(s0)
    2c00:	00140593          	addi	a1,s0,1
    2c04:	fd070713          	addi	a4,a4,-48
    2c08:	06e67463          	bgeu	a2,a4,2c70 <_vfiprintf_r+0x232>
    2c0c:	f2f9                	bnez	a3,2bd2 <_vfiprintf_r+0x194>
    2c0e:	200c                	lbu	a1,0(s0)
    2c10:	460d                	li	a2,3
    2c12:	00001517          	auipc	a0,0x1
    2c16:	99250513          	addi	a0,a0,-1646 # 35a4 <__sf_fake_stdout+0x28>
    2c1a:	2509                	jal	321c <memchr>
    2c1c:	cd11                	beqz	a0,2c38 <_vfiprintf_r+0x1fa>
    2c1e:	00001797          	auipc	a5,0x1
    2c22:	98678793          	addi	a5,a5,-1658 # 35a4 <__sf_fake_stdout+0x28>
    2c26:	8d1d                	sub	a0,a0,a5
    2c28:	04000793          	li	a5,64
    2c2c:	00a797b3          	sll	a5,a5,a0
    2c30:	4542                	lw	a0,16(sp)
    2c32:	0405                	addi	s0,s0,1
    2c34:	8d5d                	or	a0,a0,a5
    2c36:	c82a                	sw	a0,16(sp)
    2c38:	200c                	lbu	a1,0(s0)
    2c3a:	4619                	li	a2,6
    2c3c:	00001517          	auipc	a0,0x1
    2c40:	96c50513          	addi	a0,a0,-1684 # 35a8 <__sf_fake_stdout+0x2c>
    2c44:	00140913          	addi	s2,s0,1
    2c48:	02b10423          	sb	a1,40(sp)
    2c4c:	2bc1                	jal	321c <memchr>
    2c4e:	c135                	beqz	a0,2cb2 <_vfiprintf_r+0x274>
    2c50:	ffffd797          	auipc	a5,0xffffd
    2c54:	3b078793          	addi	a5,a5,944 # 0 <_sinit>
    2c58:	e795                	bnez	a5,2c84 <_vfiprintf_r+0x246>
    2c5a:	4742                	lw	a4,16(sp)
    2c5c:	47b2                	lw	a5,12(sp)
    2c5e:	10077713          	andi	a4,a4,256
    2c62:	cf09                	beqz	a4,2c7c <_vfiprintf_r+0x23e>
    2c64:	0791                	addi	a5,a5,4
    2c66:	c63e                	sw	a5,12(sp)
    2c68:	5792                	lw	a5,36(sp)
    2c6a:	97d2                	add	a5,a5,s4
    2c6c:	d23e                	sw	a5,36(sp)
    2c6e:	bd25                	j	2aa6 <_vfiprintf_r+0x68>
    2c70:	036787b3          	mul	a5,a5,s6
    2c74:	4685                	li	a3,1
    2c76:	842e                	mv	s0,a1
    2c78:	97ba                	add	a5,a5,a4
    2c7a:	b751                	j	2bfe <_vfiprintf_r+0x1c0>
    2c7c:	079d                	addi	a5,a5,7
    2c7e:	9be1                	andi	a5,a5,-8
    2c80:	07a1                	addi	a5,a5,8
    2c82:	b7d5                	j	2c66 <_vfiprintf_r+0x228>
    2c84:	0078                	addi	a4,sp,12
    2c86:	00000697          	auipc	a3,0x0
    2c8a:	d7668693          	addi	a3,a3,-650 # 29fc <__sfputs_r>
    2c8e:	8626                	mv	a2,s1
    2c90:	080c                	addi	a1,sp,16
    2c92:	854e                	mv	a0,s3
    2c94:	00000097          	auipc	ra,0x0
    2c98:	000000e7          	jalr	zero # 0 <_sinit>
    2c9c:	57fd                	li	a5,-1
    2c9e:	8a2a                	mv	s4,a0
    2ca0:	fcf514e3          	bne	a0,a5,2c68 <_vfiprintf_r+0x22a>
    2ca4:	24de                	lhu	a5,12(s1)
    2ca6:	0407f793          	andi	a5,a5,64
    2caa:	ec0792e3          	bnez	a5,2b6e <_vfiprintf_r+0x130>
    2cae:	5512                	lw	a0,36(sp)
    2cb0:	b5c1                	j	2b70 <_vfiprintf_r+0x132>
    2cb2:	0078                	addi	a4,sp,12
    2cb4:	00000697          	auipc	a3,0x0
    2cb8:	d4868693          	addi	a3,a3,-696 # 29fc <__sfputs_r>
    2cbc:	8626                	mv	a2,s1
    2cbe:	080c                	addi	a1,sp,16
    2cc0:	854e                	mv	a0,s3
    2cc2:	2a01                	jal	2dd2 <_printf_i>
    2cc4:	bfe1                	j	2c9c <_vfiprintf_r+0x25e>

00002cc6 <_printf_common>:
    2cc6:	7179                	addi	sp,sp,-48
    2cc8:	ca56                	sw	s5,20(sp)
    2cca:	499c                	lw	a5,16(a1)
    2ccc:	8aba                	mv	s5,a4
    2cce:	4598                	lw	a4,8(a1)
    2cd0:	d422                	sw	s0,40(sp)
    2cd2:	d226                	sw	s1,36(sp)
    2cd4:	ce4e                	sw	s3,28(sp)
    2cd6:	cc52                	sw	s4,24(sp)
    2cd8:	d606                	sw	ra,44(sp)
    2cda:	d04a                	sw	s2,32(sp)
    2cdc:	c85a                	sw	s6,16(sp)
    2cde:	c65e                	sw	s7,12(sp)
    2ce0:	89aa                	mv	s3,a0
    2ce2:	842e                	mv	s0,a1
    2ce4:	84b2                	mv	s1,a2
    2ce6:	8a36                	mv	s4,a3
    2ce8:	00e7d363          	bge	a5,a4,2cee <_printf_common+0x28>
    2cec:	87ba                	mv	a5,a4
    2cee:	c09c                	sw	a5,0(s1)
    2cf0:	04344703          	lbu	a4,67(s0)
    2cf4:	c319                	beqz	a4,2cfa <_printf_common+0x34>
    2cf6:	0785                	addi	a5,a5,1
    2cf8:	c09c                	sw	a5,0(s1)
    2cfa:	401c                	lw	a5,0(s0)
    2cfc:	0207f793          	andi	a5,a5,32
    2d00:	c781                	beqz	a5,2d08 <_printf_common+0x42>
    2d02:	409c                	lw	a5,0(s1)
    2d04:	0789                	addi	a5,a5,2
    2d06:	c09c                	sw	a5,0(s1)
    2d08:	00042903          	lw	s2,0(s0)
    2d0c:	00697913          	andi	s2,s2,6
    2d10:	00091a63          	bnez	s2,2d24 <_printf_common+0x5e>
    2d14:	01940b13          	addi	s6,s0,25
    2d18:	5bfd                	li	s7,-1
    2d1a:	445c                	lw	a5,12(s0)
    2d1c:	4098                	lw	a4,0(s1)
    2d1e:	8f99                	sub	a5,a5,a4
    2d20:	04f94c63          	blt	s2,a5,2d78 <_printf_common+0xb2>
    2d24:	401c                	lw	a5,0(s0)
    2d26:	04344683          	lbu	a3,67(s0)
    2d2a:	0207f793          	andi	a5,a5,32
    2d2e:	00d036b3          	snez	a3,a3
    2d32:	eba5                	bnez	a5,2da2 <_printf_common+0xdc>
    2d34:	04340613          	addi	a2,s0,67
    2d38:	85d2                	mv	a1,s4
    2d3a:	854e                	mv	a0,s3
    2d3c:	9a82                	jalr	s5
    2d3e:	57fd                	li	a5,-1
    2d40:	04f50363          	beq	a0,a5,2d86 <_printf_common+0xc0>
    2d44:	401c                	lw	a5,0(s0)
    2d46:	4611                	li	a2,4
    2d48:	4098                	lw	a4,0(s1)
    2d4a:	8b99                	andi	a5,a5,6
    2d4c:	4454                	lw	a3,12(s0)
    2d4e:	4481                	li	s1,0
    2d50:	00c79763          	bne	a5,a2,2d5e <_printf_common+0x98>
    2d54:	40e684b3          	sub	s1,a3,a4
    2d58:	0004d363          	bgez	s1,2d5e <_printf_common+0x98>
    2d5c:	4481                	li	s1,0
    2d5e:	441c                	lw	a5,8(s0)
    2d60:	4818                	lw	a4,16(s0)
    2d62:	00f75463          	bge	a4,a5,2d6a <_printf_common+0xa4>
    2d66:	8f99                	sub	a5,a5,a4
    2d68:	94be                	add	s1,s1,a5
    2d6a:	4901                	li	s2,0
    2d6c:	0469                	addi	s0,s0,26
    2d6e:	5b7d                	li	s6,-1
    2d70:	05249863          	bne	s1,s2,2dc0 <_printf_common+0xfa>
    2d74:	4501                	li	a0,0
    2d76:	a809                	j	2d88 <_printf_common+0xc2>
    2d78:	4685                	li	a3,1
    2d7a:	865a                	mv	a2,s6
    2d7c:	85d2                	mv	a1,s4
    2d7e:	854e                	mv	a0,s3
    2d80:	9a82                	jalr	s5
    2d82:	01751e63          	bne	a0,s7,2d9e <_printf_common+0xd8>
    2d86:	557d                	li	a0,-1
    2d88:	50b2                	lw	ra,44(sp)
    2d8a:	5422                	lw	s0,40(sp)
    2d8c:	5492                	lw	s1,36(sp)
    2d8e:	5902                	lw	s2,32(sp)
    2d90:	49f2                	lw	s3,28(sp)
    2d92:	4a62                	lw	s4,24(sp)
    2d94:	4ad2                	lw	s5,20(sp)
    2d96:	4b42                	lw	s6,16(sp)
    2d98:	4bb2                	lw	s7,12(sp)
    2d9a:	6145                	addi	sp,sp,48
    2d9c:	8082                	ret
    2d9e:	0905                	addi	s2,s2,1
    2da0:	bfad                	j	2d1a <_printf_common+0x54>
    2da2:	00d40733          	add	a4,s0,a3
    2da6:	03000613          	li	a2,48
    2daa:	04c701a3          	sb	a2,67(a4)
    2dae:	04544703          	lbu	a4,69(s0)
    2db2:	00168793          	addi	a5,a3,1
    2db6:	97a2                	add	a5,a5,s0
    2db8:	0689                	addi	a3,a3,2
    2dba:	04e781a3          	sb	a4,67(a5)
    2dbe:	bf9d                	j	2d34 <_printf_common+0x6e>
    2dc0:	4685                	li	a3,1
    2dc2:	8622                	mv	a2,s0
    2dc4:	85d2                	mv	a1,s4
    2dc6:	854e                	mv	a0,s3
    2dc8:	9a82                	jalr	s5
    2dca:	fb650ee3          	beq	a0,s6,2d86 <_printf_common+0xc0>
    2dce:	0905                	addi	s2,s2,1
    2dd0:	b745                	j	2d70 <_printf_common+0xaa>

00002dd2 <_printf_i>:
    2dd2:	7179                	addi	sp,sp,-48
    2dd4:	d422                	sw	s0,40(sp)
    2dd6:	d226                	sw	s1,36(sp)
    2dd8:	d04a                	sw	s2,32(sp)
    2dda:	ce4e                	sw	s3,28(sp)
    2ddc:	d606                	sw	ra,44(sp)
    2dde:	cc52                	sw	s4,24(sp)
    2de0:	ca56                	sw	s5,20(sp)
    2de2:	c85a                	sw	s6,16(sp)
    2de4:	89b6                	mv	s3,a3
    2de6:	2d94                	lbu	a3,24(a1)
    2de8:	06900793          	li	a5,105
    2dec:	8932                	mv	s2,a2
    2dee:	84aa                	mv	s1,a0
    2df0:	842e                	mv	s0,a1
    2df2:	04358613          	addi	a2,a1,67
    2df6:	02f68d63          	beq	a3,a5,2e30 <_printf_i+0x5e>
    2dfa:	06d7e263          	bltu	a5,a3,2e5e <_printf_i+0x8c>
    2dfe:	05800793          	li	a5,88
    2e02:	18f68663          	beq	a3,a5,2f8e <_printf_i+0x1bc>
    2e06:	00d7ed63          	bltu	a5,a3,2e20 <_printf_i+0x4e>
    2e0a:	20068e63          	beqz	a3,3026 <_printf_i+0x254>
    2e0e:	04300793          	li	a5,67
    2e12:	0af68e63          	beq	a3,a5,2ece <_printf_i+0xfc>
    2e16:	04240a93          	addi	s5,s0,66
    2e1a:	04d40123          	sb	a3,66(s0)
    2e1e:	a0c9                	j	2ee0 <_printf_i+0x10e>
    2e20:	06300793          	li	a5,99
    2e24:	0af68563          	beq	a3,a5,2ece <_printf_i+0xfc>
    2e28:	06400793          	li	a5,100
    2e2c:	fef695e3          	bne	a3,a5,2e16 <_printf_i+0x44>
    2e30:	401c                	lw	a5,0(s0)
    2e32:	4308                	lw	a0,0(a4)
    2e34:	0807f693          	andi	a3,a5,128
    2e38:	00450593          	addi	a1,a0,4
    2e3c:	c6c5                	beqz	a3,2ee4 <_printf_i+0x112>
    2e3e:	411c                	lw	a5,0(a0)
    2e40:	c30c                	sw	a1,0(a4)
    2e42:	0007d863          	bgez	a5,2e52 <_printf_i+0x80>
    2e46:	02d00713          	li	a4,45
    2e4a:	40f007b3          	neg	a5,a5
    2e4e:	04e401a3          	sb	a4,67(s0)
    2e52:	00000697          	auipc	a3,0x0
    2e56:	75e68693          	addi	a3,a3,1886 # 35b0 <__sf_fake_stdout+0x34>
    2e5a:	4729                	li	a4,10
    2e5c:	a865                	j	2f14 <_printf_i+0x142>
    2e5e:	07000793          	li	a5,112
    2e62:	16f68263          	beq	a3,a5,2fc6 <_printf_i+0x1f4>
    2e66:	02d7e563          	bltu	a5,a3,2e90 <_printf_i+0xbe>
    2e6a:	06e00793          	li	a5,110
    2e6e:	18f68963          	beq	a3,a5,3000 <_printf_i+0x22e>
    2e72:	06f00793          	li	a5,111
    2e76:	faf690e3          	bne	a3,a5,2e16 <_printf_i+0x44>
    2e7a:	400c                	lw	a1,0(s0)
    2e7c:	431c                	lw	a5,0(a4)
    2e7e:	0805f813          	andi	a6,a1,128
    2e82:	00478513          	addi	a0,a5,4
    2e86:	06080763          	beqz	a6,2ef4 <_printf_i+0x122>
    2e8a:	c308                	sw	a0,0(a4)
    2e8c:	439c                	lw	a5,0(a5)
    2e8e:	a885                	j	2efe <_printf_i+0x12c>
    2e90:	07500793          	li	a5,117
    2e94:	fef683e3          	beq	a3,a5,2e7a <_printf_i+0xa8>
    2e98:	07800793          	li	a5,120
    2e9c:	12f68963          	beq	a3,a5,2fce <_printf_i+0x1fc>
    2ea0:	07300793          	li	a5,115
    2ea4:	f6f699e3          	bne	a3,a5,2e16 <_printf_i+0x44>
    2ea8:	431c                	lw	a5,0(a4)
    2eaa:	41d0                	lw	a2,4(a1)
    2eac:	4581                	li	a1,0
    2eae:	00478693          	addi	a3,a5,4
    2eb2:	c314                	sw	a3,0(a4)
    2eb4:	0007aa83          	lw	s5,0(a5)
    2eb8:	8556                	mv	a0,s5
    2eba:	268d                	jal	321c <memchr>
    2ebc:	c501                	beqz	a0,2ec4 <_printf_i+0xf2>
    2ebe:	41550533          	sub	a0,a0,s5
    2ec2:	c048                	sw	a0,4(s0)
    2ec4:	405c                	lw	a5,4(s0)
    2ec6:	c81c                	sw	a5,16(s0)
    2ec8:	040401a3          	sb	zero,67(s0)
    2ecc:	a861                	j	2f64 <_printf_i+0x192>
    2ece:	431c                	lw	a5,0(a4)
    2ed0:	04240a93          	addi	s5,s0,66
    2ed4:	00478693          	addi	a3,a5,4
    2ed8:	439c                	lw	a5,0(a5)
    2eda:	c314                	sw	a3,0(a4)
    2edc:	04f40123          	sb	a5,66(s0)
    2ee0:	4785                	li	a5,1
    2ee2:	b7d5                	j	2ec6 <_printf_i+0xf4>
    2ee4:	0407f693          	andi	a3,a5,64
    2ee8:	411c                	lw	a5,0(a0)
    2eea:	c30c                	sw	a1,0(a4)
    2eec:	dab9                	beqz	a3,2e42 <_printf_i+0x70>
    2eee:	07c2                	slli	a5,a5,0x10
    2ef0:	87c1                	srai	a5,a5,0x10
    2ef2:	bf81                	j	2e42 <_printf_i+0x70>
    2ef4:	0405f593          	andi	a1,a1,64
    2ef8:	c308                	sw	a0,0(a4)
    2efa:	d9c9                	beqz	a1,2e8c <_printf_i+0xba>
    2efc:	239e                	lhu	a5,0(a5)
    2efe:	06f00713          	li	a4,111
    2f02:	0ee68763          	beq	a3,a4,2ff0 <_printf_i+0x21e>
    2f06:	00000697          	auipc	a3,0x0
    2f0a:	6aa68693          	addi	a3,a3,1706 # 35b0 <__sf_fake_stdout+0x34>
    2f0e:	4729                	li	a4,10
    2f10:	040401a3          	sb	zero,67(s0)
    2f14:	404c                	lw	a1,4(s0)
    2f16:	c40c                	sw	a1,8(s0)
    2f18:	0005c563          	bltz	a1,2f22 <_printf_i+0x150>
    2f1c:	4008                	lw	a0,0(s0)
    2f1e:	996d                	andi	a0,a0,-5
    2f20:	c008                	sw	a0,0(s0)
    2f22:	e399                	bnez	a5,2f28 <_printf_i+0x156>
    2f24:	8ab2                	mv	s5,a2
    2f26:	cd89                	beqz	a1,2f40 <_printf_i+0x16e>
    2f28:	8ab2                	mv	s5,a2
    2f2a:	02e7f5b3          	remu	a1,a5,a4
    2f2e:	1afd                	addi	s5,s5,-1
    2f30:	95b6                	add	a1,a1,a3
    2f32:	218c                	lbu	a1,0(a1)
    2f34:	00ba8023          	sb	a1,0(s5)
    2f38:	02e7d5b3          	divu	a1,a5,a4
    2f3c:	0ce7f063          	bgeu	a5,a4,2ffc <_printf_i+0x22a>
    2f40:	47a1                	li	a5,8
    2f42:	00f71e63          	bne	a4,a5,2f5e <_printf_i+0x18c>
    2f46:	401c                	lw	a5,0(s0)
    2f48:	8b85                	andi	a5,a5,1
    2f4a:	cb91                	beqz	a5,2f5e <_printf_i+0x18c>
    2f4c:	4058                	lw	a4,4(s0)
    2f4e:	481c                	lw	a5,16(s0)
    2f50:	00e7c763          	blt	a5,a4,2f5e <_printf_i+0x18c>
    2f54:	03000793          	li	a5,48
    2f58:	fefa8fa3          	sb	a5,-1(s5)
    2f5c:	1afd                	addi	s5,s5,-1
    2f5e:	41560633          	sub	a2,a2,s5
    2f62:	c810                	sw	a2,16(s0)
    2f64:	874e                	mv	a4,s3
    2f66:	86ca                	mv	a3,s2
    2f68:	0070                	addi	a2,sp,12
    2f6a:	85a2                	mv	a1,s0
    2f6c:	8526                	mv	a0,s1
    2f6e:	d59ff0ef          	jal	ra,2cc6 <_printf_common>
    2f72:	5a7d                	li	s4,-1
    2f74:	0b451d63          	bne	a0,s4,302e <_printf_i+0x25c>
    2f78:	557d                	li	a0,-1
    2f7a:	50b2                	lw	ra,44(sp)
    2f7c:	5422                	lw	s0,40(sp)
    2f7e:	5492                	lw	s1,36(sp)
    2f80:	5902                	lw	s2,32(sp)
    2f82:	49f2                	lw	s3,28(sp)
    2f84:	4a62                	lw	s4,24(sp)
    2f86:	4ad2                	lw	s5,20(sp)
    2f88:	4b42                	lw	s6,16(sp)
    2f8a:	6145                	addi	sp,sp,48
    2f8c:	8082                	ret
    2f8e:	04d582a3          	sb	a3,69(a1)
    2f92:	00000697          	auipc	a3,0x0
    2f96:	61e68693          	addi	a3,a3,1566 # 35b0 <__sf_fake_stdout+0x34>
    2f9a:	400c                	lw	a1,0(s0)
    2f9c:	4308                	lw	a0,0(a4)
    2f9e:	0805f813          	andi	a6,a1,128
    2fa2:	411c                	lw	a5,0(a0)
    2fa4:	0511                	addi	a0,a0,4
    2fa6:	02080d63          	beqz	a6,2fe0 <_printf_i+0x20e>
    2faa:	c308                	sw	a0,0(a4)
    2fac:	0015f713          	andi	a4,a1,1
    2fb0:	c701                	beqz	a4,2fb8 <_printf_i+0x1e6>
    2fb2:	0205e593          	ori	a1,a1,32
    2fb6:	c00c                	sw	a1,0(s0)
    2fb8:	4741                	li	a4,16
    2fba:	fbb9                	bnez	a5,2f10 <_printf_i+0x13e>
    2fbc:	400c                	lw	a1,0(s0)
    2fbe:	fdf5f593          	andi	a1,a1,-33
    2fc2:	c00c                	sw	a1,0(s0)
    2fc4:	b7b1                	j	2f10 <_printf_i+0x13e>
    2fc6:	419c                	lw	a5,0(a1)
    2fc8:	0207e793          	ori	a5,a5,32
    2fcc:	c19c                	sw	a5,0(a1)
    2fce:	07800793          	li	a5,120
    2fd2:	04f402a3          	sb	a5,69(s0)
    2fd6:	00000697          	auipc	a3,0x0
    2fda:	5ee68693          	addi	a3,a3,1518 # 35c4 <__sf_fake_stdout+0x48>
    2fde:	bf75                	j	2f9a <_printf_i+0x1c8>
    2fe0:	0405f813          	andi	a6,a1,64
    2fe4:	c308                	sw	a0,0(a4)
    2fe6:	fc0803e3          	beqz	a6,2fac <_printf_i+0x1da>
    2fea:	07c2                	slli	a5,a5,0x10
    2fec:	83c1                	srli	a5,a5,0x10
    2fee:	bf7d                	j	2fac <_printf_i+0x1da>
    2ff0:	00000697          	auipc	a3,0x0
    2ff4:	5c068693          	addi	a3,a3,1472 # 35b0 <__sf_fake_stdout+0x34>
    2ff8:	4721                	li	a4,8
    2ffa:	bf19                	j	2f10 <_printf_i+0x13e>
    2ffc:	87ae                	mv	a5,a1
    2ffe:	b735                	j	2f2a <_printf_i+0x158>
    3000:	4194                	lw	a3,0(a1)
    3002:	431c                	lw	a5,0(a4)
    3004:	49cc                	lw	a1,20(a1)
    3006:	0806f813          	andi	a6,a3,128
    300a:	00478513          	addi	a0,a5,4
    300e:	00080663          	beqz	a6,301a <_printf_i+0x248>
    3012:	c308                	sw	a0,0(a4)
    3014:	439c                	lw	a5,0(a5)
    3016:	c38c                	sw	a1,0(a5)
    3018:	a039                	j	3026 <_printf_i+0x254>
    301a:	c308                	sw	a0,0(a4)
    301c:	0406f693          	andi	a3,a3,64
    3020:	439c                	lw	a5,0(a5)
    3022:	daf5                	beqz	a3,3016 <_printf_i+0x244>
    3024:	a38e                	sh	a1,0(a5)
    3026:	00042823          	sw	zero,16(s0)
    302a:	8ab2                	mv	s5,a2
    302c:	bf25                	j	2f64 <_printf_i+0x192>
    302e:	4814                	lw	a3,16(s0)
    3030:	8656                	mv	a2,s5
    3032:	85ca                	mv	a1,s2
    3034:	8526                	mv	a0,s1
    3036:	9982                	jalr	s3
    3038:	f54500e3          	beq	a0,s4,2f78 <_printf_i+0x1a6>
    303c:	401c                	lw	a5,0(s0)
    303e:	8b89                	andi	a5,a5,2
    3040:	e78d                	bnez	a5,306a <_printf_i+0x298>
    3042:	47b2                	lw	a5,12(sp)
    3044:	4448                	lw	a0,12(s0)
    3046:	f2f55ae3          	bge	a0,a5,2f7a <_printf_i+0x1a8>
    304a:	853e                	mv	a0,a5
    304c:	b73d                	j	2f7a <_printf_i+0x1a8>
    304e:	4685                	li	a3,1
    3050:	8656                	mv	a2,s5
    3052:	85ca                	mv	a1,s2
    3054:	8526                	mv	a0,s1
    3056:	9982                	jalr	s3
    3058:	f36500e3          	beq	a0,s6,2f78 <_printf_i+0x1a6>
    305c:	0a05                	addi	s4,s4,1
    305e:	445c                	lw	a5,12(s0)
    3060:	4732                	lw	a4,12(sp)
    3062:	8f99                	sub	a5,a5,a4
    3064:	fefa45e3          	blt	s4,a5,304e <_printf_i+0x27c>
    3068:	bfe9                	j	3042 <_printf_i+0x270>
    306a:	4a01                	li	s4,0
    306c:	01940a93          	addi	s5,s0,25
    3070:	5b7d                	li	s6,-1
    3072:	b7f5                	j	305e <_printf_i+0x28c>

00003074 <_sbrk_r>:
    3074:	1141                	addi	sp,sp,-16
    3076:	c422                	sw	s0,8(sp)
    3078:	842a                	mv	s0,a0
    307a:	852e                	mv	a0,a1
    307c:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    3080:	c606                	sw	ra,12(sp)
    3082:	b31fe0ef          	jal	ra,1bb2 <_sbrk>
    3086:	57fd                	li	a5,-1
    3088:	00f51763          	bne	a0,a5,3096 <_sbrk_r+0x22>
    308c:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    3090:	439c                	lw	a5,0(a5)
    3092:	c391                	beqz	a5,3096 <_sbrk_r+0x22>
    3094:	c01c                	sw	a5,0(s0)
    3096:	40b2                	lw	ra,12(sp)
    3098:	4422                	lw	s0,8(sp)
    309a:	0141                	addi	sp,sp,16
    309c:	8082                	ret

0000309e <__sread>:
    309e:	1141                	addi	sp,sp,-16
    30a0:	c422                	sw	s0,8(sp)
    30a2:	842e                	mv	s0,a1
    30a4:	00e59583          	lh	a1,14(a1)
    30a8:	c606                	sw	ra,12(sp)
    30aa:	2279                	jal	3238 <_read_r>
    30ac:	00054963          	bltz	a0,30be <__sread+0x20>
    30b0:	487c                	lw	a5,84(s0)
    30b2:	97aa                	add	a5,a5,a0
    30b4:	c87c                	sw	a5,84(s0)
    30b6:	40b2                	lw	ra,12(sp)
    30b8:	4422                	lw	s0,8(sp)
    30ba:	0141                	addi	sp,sp,16
    30bc:	8082                	ret
    30be:	245e                	lhu	a5,12(s0)
    30c0:	777d                	lui	a4,0xfffff
    30c2:	177d                	addi	a4,a4,-1
    30c4:	8ff9                	and	a5,a5,a4
    30c6:	a45e                	sh	a5,12(s0)
    30c8:	b7fd                	j	30b6 <__sread+0x18>

000030ca <__swrite>:
    30ca:	25de                	lhu	a5,12(a1)
    30cc:	1101                	addi	sp,sp,-32
    30ce:	cc22                	sw	s0,24(sp)
    30d0:	ca26                	sw	s1,20(sp)
    30d2:	c84a                	sw	s2,16(sp)
    30d4:	c64e                	sw	s3,12(sp)
    30d6:	ce06                	sw	ra,28(sp)
    30d8:	1007f793          	andi	a5,a5,256
    30dc:	84aa                	mv	s1,a0
    30de:	842e                	mv	s0,a1
    30e0:	8932                	mv	s2,a2
    30e2:	89b6                	mv	s3,a3
    30e4:	c791                	beqz	a5,30f0 <__swrite+0x26>
    30e6:	00e59583          	lh	a1,14(a1)
    30ea:	4689                	li	a3,2
    30ec:	4601                	li	a2,0
    30ee:	2209                	jal	31f0 <_lseek_r>
    30f0:	245e                	lhu	a5,12(s0)
    30f2:	777d                	lui	a4,0xfffff
    30f4:	177d                	addi	a4,a4,-1
    30f6:	8ff9                	and	a5,a5,a4
    30f8:	a45e                	sh	a5,12(s0)
    30fa:	00e41583          	lh	a1,14(s0)
    30fe:	4462                	lw	s0,24(sp)
    3100:	40f2                	lw	ra,28(sp)
    3102:	86ce                	mv	a3,s3
    3104:	864a                	mv	a2,s2
    3106:	49b2                	lw	s3,12(sp)
    3108:	4942                	lw	s2,16(sp)
    310a:	8526                	mv	a0,s1
    310c:	44d2                	lw	s1,20(sp)
    310e:	6105                	addi	sp,sp,32
    3110:	a825                	j	3148 <_write_r>

00003112 <__sseek>:
    3112:	1141                	addi	sp,sp,-16
    3114:	c422                	sw	s0,8(sp)
    3116:	842e                	mv	s0,a1
    3118:	00e59583          	lh	a1,14(a1)
    311c:	c606                	sw	ra,12(sp)
    311e:	28c9                	jal	31f0 <_lseek_r>
    3120:	57fd                	li	a5,-1
    3122:	245a                	lhu	a4,12(s0)
    3124:	00f51a63          	bne	a0,a5,3138 <__sseek+0x26>
    3128:	77fd                	lui	a5,0xfffff
    312a:	17fd                	addi	a5,a5,-1
    312c:	8ff9                	and	a5,a5,a4
    312e:	a45e                	sh	a5,12(s0)
    3130:	40b2                	lw	ra,12(sp)
    3132:	4422                	lw	s0,8(sp)
    3134:	0141                	addi	sp,sp,16
    3136:	8082                	ret
    3138:	6785                	lui	a5,0x1
    313a:	8fd9                	or	a5,a5,a4
    313c:	a45e                	sh	a5,12(s0)
    313e:	c868                	sw	a0,84(s0)
    3140:	bfc5                	j	3130 <__sseek+0x1e>

00003142 <__sclose>:
    3142:	00e59583          	lh	a1,14(a1)
    3146:	a805                	j	3176 <_close_r>

00003148 <_write_r>:
    3148:	1141                	addi	sp,sp,-16
    314a:	c422                	sw	s0,8(sp)
    314c:	842a                	mv	s0,a0
    314e:	852e                	mv	a0,a1
    3150:	85b2                	mv	a1,a2
    3152:	8636                	mv	a2,a3
    3154:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    3158:	c606                	sw	ra,12(sp)
    315a:	a1bfe0ef          	jal	ra,1b74 <_write>
    315e:	57fd                	li	a5,-1
    3160:	00f51763          	bne	a0,a5,316e <_write_r+0x26>
    3164:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    3168:	439c                	lw	a5,0(a5)
    316a:	c391                	beqz	a5,316e <_write_r+0x26>
    316c:	c01c                	sw	a5,0(s0)
    316e:	40b2                	lw	ra,12(sp)
    3170:	4422                	lw	s0,8(sp)
    3172:	0141                	addi	sp,sp,16
    3174:	8082                	ret

00003176 <_close_r>:
    3176:	1141                	addi	sp,sp,-16
    3178:	c422                	sw	s0,8(sp)
    317a:	842a                	mv	s0,a0
    317c:	852e                	mv	a0,a1
    317e:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    3182:	c606                	sw	ra,12(sp)
    3184:	20c5                	jal	3264 <_close>
    3186:	57fd                	li	a5,-1
    3188:	00f51763          	bne	a0,a5,3196 <_close_r+0x20>
    318c:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    3190:	439c                	lw	a5,0(a5)
    3192:	c391                	beqz	a5,3196 <_close_r+0x20>
    3194:	c01c                	sw	a5,0(s0)
    3196:	40b2                	lw	ra,12(sp)
    3198:	4422                	lw	s0,8(sp)
    319a:	0141                	addi	sp,sp,16
    319c:	8082                	ret

0000319e <_fstat_r>:
    319e:	1141                	addi	sp,sp,-16
    31a0:	c422                	sw	s0,8(sp)
    31a2:	842a                	mv	s0,a0
    31a4:	852e                	mv	a0,a1
    31a6:	85b2                	mv	a1,a2
    31a8:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    31ac:	c606                	sw	ra,12(sp)
    31ae:	20c9                	jal	3270 <_fstat>
    31b0:	57fd                	li	a5,-1
    31b2:	00f51763          	bne	a0,a5,31c0 <_fstat_r+0x22>
    31b6:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    31ba:	439c                	lw	a5,0(a5)
    31bc:	c391                	beqz	a5,31c0 <_fstat_r+0x22>
    31be:	c01c                	sw	a5,0(s0)
    31c0:	40b2                	lw	ra,12(sp)
    31c2:	4422                	lw	s0,8(sp)
    31c4:	0141                	addi	sp,sp,16
    31c6:	8082                	ret

000031c8 <_isatty_r>:
    31c8:	1141                	addi	sp,sp,-16
    31ca:	c422                	sw	s0,8(sp)
    31cc:	842a                	mv	s0,a0
    31ce:	852e                	mv	a0,a1
    31d0:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    31d4:	c606                	sw	ra,12(sp)
    31d6:	205d                	jal	327c <_isatty>
    31d8:	57fd                	li	a5,-1
    31da:	00f51763          	bne	a0,a5,31e8 <_isatty_r+0x20>
    31de:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    31e2:	439c                	lw	a5,0(a5)
    31e4:	c391                	beqz	a5,31e8 <_isatty_r+0x20>
    31e6:	c01c                	sw	a5,0(s0)
    31e8:	40b2                	lw	ra,12(sp)
    31ea:	4422                	lw	s0,8(sp)
    31ec:	0141                	addi	sp,sp,16
    31ee:	8082                	ret

000031f0 <_lseek_r>:
    31f0:	1141                	addi	sp,sp,-16
    31f2:	c422                	sw	s0,8(sp)
    31f4:	842a                	mv	s0,a0
    31f6:	852e                	mv	a0,a1
    31f8:	85b2                	mv	a1,a2
    31fa:	8636                	mv	a2,a3
    31fc:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    3200:	c606                	sw	ra,12(sp)
    3202:	2059                	jal	3288 <_lseek>
    3204:	57fd                	li	a5,-1
    3206:	00f51763          	bne	a0,a5,3214 <_lseek_r+0x24>
    320a:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    320e:	439c                	lw	a5,0(a5)
    3210:	c391                	beqz	a5,3214 <_lseek_r+0x24>
    3212:	c01c                	sw	a5,0(s0)
    3214:	40b2                	lw	ra,12(sp)
    3216:	4422                	lw	s0,8(sp)
    3218:	0141                	addi	sp,sp,16
    321a:	8082                	ret

0000321c <memchr>:
    321c:	0ff5f593          	andi	a1,a1,255
    3220:	962a                	add	a2,a2,a0
    3222:	00c51463          	bne	a0,a2,322a <memchr+0xe>
    3226:	4501                	li	a0,0
    3228:	8082                	ret
    322a:	211c                	lbu	a5,0(a0)
    322c:	feb78ee3          	beq	a5,a1,3228 <memchr+0xc>
    3230:	0505                	addi	a0,a0,1
    3232:	bfc5                	j	3222 <memchr+0x6>

00003234 <__malloc_lock>:
    3234:	8082                	ret

00003236 <__malloc_unlock>:
    3236:	8082                	ret

00003238 <_read_r>:
    3238:	1141                	addi	sp,sp,-16
    323a:	c422                	sw	s0,8(sp)
    323c:	842a                	mv	s0,a0
    323e:	852e                	mv	a0,a1
    3240:	85b2                	mv	a1,a2
    3242:	8636                	mv	a2,a3
    3244:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    3248:	c606                	sw	ra,12(sp)
    324a:	20a9                	jal	3294 <_read>
    324c:	57fd                	li	a5,-1
    324e:	00f51763          	bne	a0,a5,325c <_read_r+0x24>
    3252:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    3256:	439c                	lw	a5,0(a5)
    3258:	c391                	beqz	a5,325c <_read_r+0x24>
    325a:	c01c                	sw	a5,0(s0)
    325c:	40b2                	lw	ra,12(sp)
    325e:	4422                	lw	s0,8(sp)
    3260:	0141                	addi	sp,sp,16
    3262:	8082                	ret

00003264 <_close>:
    3264:	05800793          	li	a5,88
    3268:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    326c:	557d                	li	a0,-1
    326e:	8082                	ret

00003270 <_fstat>:
    3270:	05800793          	li	a5,88
    3274:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    3278:	557d                	li	a0,-1
    327a:	8082                	ret

0000327c <_isatty>:
    327c:	05800793          	li	a5,88
    3280:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    3284:	4501                	li	a0,0
    3286:	8082                	ret

00003288 <_lseek>:
    3288:	05800793          	li	a5,88
    328c:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    3290:	557d                	li	a0,-1
    3292:	8082                	ret

00003294 <_read>:
    3294:	05800793          	li	a5,88
    3298:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    329c:	557d                	li	a0,-1
    329e:	8082                	ret
    32a0:	b3cdb5cf          	fnmadd.d	fa1,fs11,ft8,fs6,rup
    32a4:	bccaf5b3          	0xbccaf5b3
    32a8:	eacdafbb          	0xeacdafbb
    32ac:	aca3c9b3          	0xaca3c9b3
    32b0:	ddc5e5b3          	0xddc5e5b3
    32b4:	c6d6d8bf b3cdb5cf 	0xb3cdb5cfc6d6d8bf
    32bc:	d1d2                	sw	s4,224(sp)
    32be:	f4c6                	fsw	fa7,104(sp)
    32c0:	afb6                	sh	a3,26(a5)
    32c2:	0000                	unimp
    32c4:	0e1e                	slli	t3,t3,0x7
    32c6:	0000                	unimp
    32c8:	0e48                	addi	a0,sp,788
    32ca:	0000                	unimp
    32cc:	0e4c                	addi	a1,sp,788
    32ce:	0000                	unimp
    32d0:	0e48                	addi	a0,sp,788
    32d2:	0000                	unimp
    32d4:	0e48                	addi	a0,sp,788
    32d6:	0000                	unimp
    32d8:	0e68                	addi	a0,sp,796
    32da:	0000                	unimp
    32dc:	0e78                	addi	a4,sp,796
    32de:	0000                	unimp
    32e0:	0e9c                	addi	a5,sp,848
    32e2:	0000                	unimp
    32e4:	bccaaabf aecbd3bc 	0xaecbd3bcbccaaabf
    32ec:	aecbaca3          	sw	a2,-1287(s7)
    32f0:	c3b1                	beqz	a5,3334 <_read+0xa0>
    32f2:	c631                	beqz	a2,333e <_read+0xaa>
    32f4:	b6f4                	sb	a3,15(a3)
    32f6:	aabf00af          	0xaabf00af
    32fa:	bcca                	sh	a0,60(s1)
    32fc:	d3bc                	sw	a5,96(a5)
    32fe:	c8c8                	sw	a0,20(s1)
    3300:	f5b3aca3          	sw	s11,-167(t2)
    3304:	bcca                	sh	a0,60(s1)
    3306:	c2ce                	sw	s3,68(sp)
    3308:	c8b6                	sw	a3,80(sp)
    330a:	203a                	lhu	a4,2(s0)
    330c:	2e25                	jal	3644 <_data_lma+0x6c>
    330e:	6632                	flw	fa2,12(sp)
    3310:	e3a1                	bnez	a5,3350 <_read+0xbc>
    3312:	00000a43          	fmadd.s	fs4,ft0,ft0,ft0,rne
    3316:	0000                	unimp
    3318:	bccaaabf e8b0c1bd 	0xe8b0c1bdbccaaabf
    3320:	0000                	unimp
    3322:	0000                	unimp
    3324:	c1bd                	beqz	a1,338a <_read+0xf6>
    3326:	e8b0                	fsw	fa2,80(s1)
    3328:	d1d2                	sw	s4,224(sp)
    332a:	a3cd                	j	390c <_data_lma+0x334>
    332c:	b9d6                	sh	a3,52(a1)
    332e:	0000                	unimp
    3330:	ddc5e5b3          	0xddc5e5b3
    3334:	f7c1                	bnez	a5,32bc <_read+0x28>
    3336:	aabfccb3          	0xaabfccb3
    333a:	bcca                	sh	a0,60(s1)
    333c:	c8b5aca3          	sw	a1,-871(a1)
    3340:	fdb4                	fsw	fa3,120(a1)
    3342:	b4b0                	sb	a2,11(s1)
    3344:	fcbc                	fsw	fa5,120(s1)
    3346:	c831                	beqz	s0,339a <_read+0x106>
    3348:	bccfc8b7          	lui	a7,0xbccfc
    334c:	00aecbd3          	fadd.s	fs7,ft9,fa0,rmm
    3350:	d3bc                	sw	a5,96(a5)
    3352:	eacdaecb          	fnmsub.d	ft9,fs11,fa2,ft9,rdn
    3356:	aca3c9b3          	0xaca3c9b3
    335a:	b1cac3d3          	0xb1cac3d3
    335e:	203a                	lhu	a4,2(s0)
    3360:	6c25                	lui	s8,0x9
    3362:	2075                	jal	340e <_read+0x17a>
    3364:	736d                	lui	t1,0xffffb
    3366:	c8b5aca3          	sw	a1,-871(a1)
    336a:	fdb4                	fsw	fa3,120(a1)
    336c:	b4b0                	sb	a2,11(s1)
    336e:	fcbc                	fsw	fa5,120(s1)
    3370:	c832                	sw	a2,16(sp)
    3372:	bccfc8b7          	lui	a7,0xbccfc
    3376:	0ac8c8d3          	fsub.d	fa7,fa7,fa2,rmm
    337a:	0000                	unimp
    337c:	d3bc                	sw	a5,96(a5)
    337e:	c8c8                	sw	a0,20(s1)
    3380:	eacd                	bnez	a3,3432 <_read+0x19e>
    3382:	aca3c9b3          	0xaca3c9b3
    3386:	c2ce                	sw	s3,68(sp)
    3388:	c8b6                	sw	a3,80(sp)
    338a:	cfc9                	beqz	a5,3424 <_read+0x190>
    338c:	fdc9                	bnez	a1,3326 <_read+0x92>
    338e:	203a                	lhu	a4,2(s0)
    3390:	a132                	sh	a2,2(a0)
    3392:	aca343e3          	blt	t1,a0,2e58 <_printf_i+0x86>
    3396:	c8b5                	beqz	s1,340a <_read+0x176>
    3398:	fdb4                	fsw	fa3,120(a1)
    339a:	b4b0                	sb	a2,11(s1)
    339c:	fcbc                	fsw	fa5,120(s1)
    339e:	c8b7c833          	0xc8b7c833
    33a2:	b0c1bdcf          	fnmadd.s	fs11,ft3,fa2,fs6,rup
    33a6:	00e8                	addi	a0,sp,76
    33a8:	bccaaabf c3d6b2be 	0xc3d6b2bebccaaabf
    33b0:	3031                	jal	2bbc <_vfiprintf_r+0x17e>
    33b2:	0000ebc3          	fmadd.s	fs7,ft1,ft0,ft0,unknown
    33b6:	0000                	unimp
    33b8:	b2be                	sh	a5,34(a3)
    33ba:	c3d6                	sw	s5,196(sp)
    33bc:	eacd                	bnez	a3,346e <__clz_tab+0x32>
    33be:	aca3c9b3          	0xaca3c9b3
    33c2:	bccaaabf cbc2fdb9 	0xcbc2fdb9bccaaabf
    33ca:	aecbaca3          	sw	a2,-1287(s7)
    33ce:	c3b1                	beqz	a5,3412 <_read+0x17e>
    33d0:	c632                	sw	a2,12(sp)
    33d2:	b6f4                	sb	a3,15(a3)
    33d4:	000000af          	0xaf
    33d8:	fdb9                	bnez	a1,3336 <_read+0xa2>
    33da:	cbc2                	sw	a6,212(sp)
    33dc:	eacd                	bnez	a3,348e <__clz_tab+0x52>
    33de:	aca3c9b3          	0xaca3c9b3
    33e2:	ddc5e5b3          	0xddc5e5b3
    33e6:	f7c1                	bnez	a5,336e <_read+0xda>
    33e8:	e1bdccb3          	0xe1bdccb3
    33ec:	f8ca                	fsw	fs2,112(sp)
    33ee:	0000                	unimp
    33f0:	0000                	unimp
    33f2:	c2c8                	sw	a0,4(a3)
    33f4:	0000                	unimp
    33f6:	c47a                	sw	t5,8(sp)
    33f8:	0000                	unimp
    33fa:	3d80                	lbu	s0,25(a1)
    33fc:	e9e8                	fsw	fa0,84(a1)
    33fe:	ffff                	0xffff
    3400:	e93a                	fsw	fa4,144(sp)
    3402:	ffff                	0xffff
    3404:	e93a                	fsw	fa4,144(sp)
    3406:	ffff                	0xffff
    3408:	e938                	fsw	fa4,80(a0)
    340a:	ffff                	0xffff
    340c:	e93e                	fsw	fa5,144(sp)
    340e:	ffff                	0xffff
    3410:	e93e                	fsw	fa5,144(sp)
    3412:	ffff                	0xffff
    3414:	e90e                	fsw	ft3,144(sp)
    3416:	ffff                	0xffff
    3418:	e938                	fsw	fa4,80(a0)
    341a:	ffff                	0xffff
    341c:	e93e                	fsw	fa5,144(sp)
    341e:	ffff                	0xffff
    3420:	e90e                	fsw	ft3,144(sp)
    3422:	ffff                	0xffff
    3424:	e93e                	fsw	fa5,144(sp)
    3426:	ffff                	0xffff
    3428:	e938                	fsw	fa4,80(a0)
    342a:	ffff                	0xffff
    342c:	e9d6                	fsw	fs5,208(sp)
    342e:	ffff                	0xffff
    3430:	e9d6                	fsw	fs5,208(sp)
    3432:	ffff                	0xffff
    3434:	e9d6                	fsw	fs5,208(sp)
    3436:	ffff                	0xffff
    3438:	e90e                	fsw	ft3,144(sp)
    343a:	ffff                	0xffff

0000343c <__clz_tab>:
    343c:	0100 0202 0303 0303 0404 0404 0404 0404     ................
    344c:	0505 0505 0505 0505 0505 0505 0505 0505     ................
    345c:	0606 0606 0606 0606 0606 0606 0606 0606     ................
    346c:	0606 0606 0606 0606 0606 0606 0606 0606     ................
    347c:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    348c:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    349c:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    34ac:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    34bc:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34cc:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34dc:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34ec:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34fc:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    350c:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    351c:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    352c:	0808 0808 0808 0808 0808 0808 0808 0808     ................

0000353c <__sf_fake_stderr>:
	...

0000355c <__sf_fake_stdin>:
	...

0000357c <__sf_fake_stdout>:
	...
    359c:	2d23 2b30 0020 0000 6c68 004c 6665 4567     #-0+ ...hlL.efgE
    35ac:	4746 0000 3130 3332 3534 3736 3938 4241     FG..0123456789AB
    35bc:	4443 4645 0000 0000 3130 3332 3534 3736     CDEF....01234567
    35cc:	3938 6261 6463 6665 0000 0000               89abcdef....
