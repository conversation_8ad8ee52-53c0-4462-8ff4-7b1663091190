
ds18b20.elf:     file format elf32-littleriscv
ds18b20.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x000035d4 memsz 0x000035d4 flags r-x
    LOAD off    0x00005000 vaddr 0x20000000 paddr 0x000035d4 align 2**12
         filesz 0x000000c0 memsz 0x0000012c flags rw-
    LOAD off    0x00005800 vaddr 0x20007800 paddr 0x20007800 align 2**12
         filesz 0x00000000 memsz 0x00000800 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         00000004  00000000  00000000  00001000  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .vector       000001bc  00000004  00000004  00001004  2**1
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .text         00003414  000001c0  000001c0  000011c0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  3 .fini         00000000  000035d4  000035d4  000050c0  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  4 .dalign       00000000  20000000  20000000  000050c0  2**0
                  CONTENTS
  5 .dlalign      00000000  000035d4  000035d4  000050c0  2**0
                  CONTENTS
  6 .data         000000c0  20000000  000035d4  00005000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  7 .bss          0000006c  200000c0  00003694  000050c0  2**2
                  ALLOC
  8 .stack        00000800  20007800  20007800  00005800  2**0
                  ALLOC
  9 .debug_info   00016320  00000000  00000000  000050c0  2**0
                  CONTENTS, READONLY, DEBUGGING
 10 .debug_abbrev 00003abe  00000000  00000000  0001b3e0  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_aranges 00000b30  00000000  00000000  0001eea0  2**3
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_ranges 00000b70  00000000  00000000  0001f9d0  2**3
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_line   0000e0cb  00000000  00000000  00020540  2**0
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_str    00003564  00000000  00000000  0002e60b  2**0
                  CONTENTS, READONLY, DEBUGGING
 15 .comment      00000033  00000000  00000000  00031b6f  2**0
                  CONTENTS, READONLY
 16 .debug_frame  000024cc  00000000  00000000  00031ba4  2**2
                  CONTENTS, READONLY, DEBUGGING
 17 .debug_loc    000058e2  00000000  00000000  00034070  2**0
                  CONTENTS, READONLY, DEBUGGING
 18 .stab         00000084  00000000  00000000  00039954  2**2
                  CONTENTS, READONLY, DEBUGGING
 19 .stabstr      00000117  00000000  00000000  000399d8  2**0
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
00000004 l    d  .vector	00000000 .vector
000001c0 l    d  .text	00000000 .text
000035d4 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
000035d4 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
200000c0 l    d  .bss	00000000 .bss
20007800 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .stab	00000000 .stab
00000000 l    d  .stabstr	00000000 .stabstr
00000000 l    df *ABS*	00000000 ./Startup/startup_ch32v30x_D8C.o
00000004 l       .vector	00000000 _vector_base
00000000 l    df *ABS*	00000000 ch32v30x_it.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 system_ch32v30x.c
00000000 l    df *ABS*	00000000 ch32v30x_gpio.c
00000000 l    df *ABS*	00000000 ch32v30x_misc.c
00000000 l    df *ABS*	00000000 ch32v30x_rcc.c
20000034 l     O .data	00000010 APBAHBPrescTable
200000ac l     O .data	00000004 ADCPrescTable
00000000 l    df *ABS*	00000000 ch32v30x_spi.c
00000000 l    df *ABS*	00000000 ch32v30x_tim.c
00000000 l    df *ABS*	00000000 ch32v30x_usart.c
00000000 l    df *ABS*	00000000 SysTickDelay.c
200000d0 l     O .bss	00000002 fac_ms
200000d2 l     O .bss	00000001 fac_us
00000000 l    df *ABS*	00000000 brewing_control.c
00000000 l    df *ABS*	00000000 ds18b20.c
00000000 l    df *ABS*	00000000 heater.c
00000000 l    df *ABS*	00000000 key.c
00000000 l    df *ABS*	00000000 lcd.c
00000000 l    df *ABS*	00000000 timer.c
00000000 l    df *ABS*	00000000 water_level.c
00000000 l    df *ABS*	00000000 water_pump.c
00000000 l    df *ABS*	00000000 debug.c
200000d4 l     O .bss	00000002 p_ms
200000d6 l     O .bss	00000001 p_us
200000b0 l     O .data	00000004 curbrk.5274
00000000 l    df *ABS*	00000000 lesf2.c
00000000 l    df *ABS*	00000000 mulsf3.c
00000000 l    df *ABS*	00000000 floatsisf.c
00000000 l    df *ABS*	00000000 extendsfdf2.c
00000000 l    df *ABS*	00000000 libgcc2.c
00000000 l    df *ABS*	00000000 printf.c
00000000 l    df *ABS*	00000000 puts.c
00000000 l    df *ABS*	00000000 wbuf.c
00000000 l    df *ABS*	00000000 wsetup.c
00000000 l    df *ABS*	00000000 fflush.c
00000000 l    df *ABS*	00000000 findfp.c
0000253c l     F .text	00000066 std
00000000 l    df *ABS*	00000000 fwalk.c
00000000 l    df *ABS*	00000000 makebuf.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 nano-mallocr.c
00000000 l    df *ABS*	00000000 nano-vfprintf.c
000029ce l     F .text	00000028 __sfputc_r
00000000 l    df *ABS*	00000000 nano-vfprintf_i.c
00000000 l    df *ABS*	00000000 sbrkr.c
00000000 l    df *ABS*	00000000 stdio.c
00000000 l    df *ABS*	00000000 writer.c
00000000 l    df *ABS*	00000000 closer.c
00000000 l    df *ABS*	00000000 fstatr.c
00000000 l    df *ABS*	00000000 isattyr.c
00000000 l    df *ABS*	00000000 lseekr.c
00000000 l    df *ABS*	00000000 memchr.c
00000000 l    df *ABS*	00000000 mlock.c
00000000 l    df *ABS*	00000000 readr.c
00000000 l    df *ABS*	00000000 close.c
00000000 l    df *ABS*	00000000 fstat.c
00000000 l    df *ABS*	00000000 isatty.c
00000000 l    df *ABS*	00000000 lseek.c
00000000 l    df *ABS*	00000000 read.c
00000000 l    df *ABS*	00000000 libgcc2.c
00000000 l    df *ABS*	00000000 impure.c
20000044 l     O .data	00000060 impure_data
00000000 l    df *ABS*	00000000 reent.c
00000668  w      .text	00000000 EXTI2_IRQHandler
00000668  w      .text	00000000 TIM8_TRG_COM_IRQHandler
00000668  w      .text	00000000 TIM8_CC_IRQHandler
000031c2 g     F .text	00000028 _isatty_r
0000210e g     F .text	000000d4 _puts_r
000031ea g     F .text	0000002c _lseek_r
00000d44 g     F .text	000000b4 BrewingControl_KeyHandler
00000668  w      .text	00000000 UART8_IRQHandler
00001034 g     F .text	00000024 DS18B20_ReadByte
000020ce g     F .text	00000040 printf
200008a8 g       .data	00000000 __global_pointer$
000001c8 g     F .text	00000028 .hidden __riscv_save_8
00000668  w      .text	00000000 TIM1_CC_IRQHandler
0000310c g     F .text	00000030 __sseek
000025f2 g     F .text	0000006c __sinit
00000a24 g     F .text	00000004 SPI_I2S_SendData
000021ee g     F .text	000000bc __swbuf_r
000002ca g     F .text	00000010 HardFault_Handler
200000c4 g     O .bss	00000001 p
000025ac g     F .text	00000046 __sfmoreglue
00003230 g     F .text	00000002 __malloc_unlock
0000116e g     F .text	00000092 key_init
000002dc g     F .text	0000003a key_proc
20000000 g     O .data	00000024 scheduler_task_t
00000214 g     F .text	0000000c .hidden __riscv_restore_3
00000668  w      .text	00000000 TIM6_IRQHandler
00000ba6 g     F .text	0000000e TIM_OC1PreloadConfig
00000668  w      .text	00000000 SysTick_Handler
000007cc g     F .text	0000004e NVIC_Init
00000668  w      .text	00000000 PVD_IRQHandler
00000668  w      .text	00000000 SDIO_IRQHandler
00001900 g     F .text	0000005e stir_360
00000668  w      .text	00000000 TIM9_BRK_IRQHandler
00000200 g     F .text	00000020 .hidden __riscv_restore_10
00000a28 g     F .text	00000004 SPI_I2S_ReceiveData
00000668  w      .text	00000000 DMA2_Channel8_IRQHandler
000002c8 g     F .text	00000002 NMI_Handler
00000668  w      .text	00000000 CAN2_RX1_IRQHandler
00000668  w      .text	00000000 EXTI3_IRQHandler
000001c8 g     F .text	00000028 .hidden __riscv_save_11
000014e0 g     F .text	00000062 spi_readwrite
00000668  w      .text	00000000 USBHS_IRQHandler
00000c88 g     F .text	0000000a USART_GetFlagStatus
00000668  w      .text	00000000 DMA2_Channel9_IRQHandler
00003198 g     F .text	0000002a _fstat_r
00000668  w      .text	00000000 TIM10_CC_IRQHandler
20000128 g     O .bss	00000004 errno
200000c0 g       .bss	00000000 _sbss
00000800 g       *ABS*	00000000 __stack_size
000015aa g     F .text	00000030 LCD_WR_REG
00001b14 g     F .text	0000005a USART_Printf_Init
00000ec6 g     F .text	00000026 DS18B20_IO_OUT
00000668  w      .text	00000000 USBFS_IRQHandler
00000214 g     F .text	0000000c .hidden __riscv_restore_2
0000195e g     F .text	00000020 stir
000019da g     F .text	00000082 WaterPump_Init
00000f10 g     F .text	00000030 DS18B20_Reset
000025a2 g     F .text	0000000a _cleanup_r
00000d22 g     F .text	00000022 BrewingControl_Start
00000668  w      .text	00000000 EXTI0_IRQHandler
00000668  w      .text	00000000 I2C2_EV_IRQHandler
00000668  w      .text	00000000 TIM10_TRG_COM_IRQHandler
00000b62 g     F .text	00000018 TIM_Cmd
000021e2 g     F .text	0000000c puts
200000a8 g     O .data	00000004 SystemCoreClock
0000326a g     F .text	0000000c _fstat
00000004 g       .init	00000000 _einit
00000bd0 g     F .text	0000000c TIM_ClearITPendingBit
00001bd2 g     F .text	0000008c .hidden __lesf2
00000990 g     F .text	0000001e RCC_APB2PeriphClockCmd
000001c0 g     F .text	00000030 .hidden __riscv_save_12
00000668  w      .text	00000000 CAN2_SCE_IRQHandler
00000668  w      .text	00000000 ADC1_2_IRQHandler
00001876 g     F .text	0000008a TIM2_PWM_Init
000006f4 g     F .text	000000c0 GPIO_Init
00000668  w      .text	00000000 Break_Point_Handler
00000200 g     F .text	00000020 .hidden __riscv_restore_11
200000cc g     O .bss	00000004 NVIC_Priority_Group
00000668  w      .text	00000000 SPI1_IRQHandler
00000c6a g     F .text	00000016 USART_Cmd
0000306e g     F .text	0000002a _sbrk_r
000001f0 g     F .text	0000000c .hidden __riscv_save_1
00000668  w      .text	00000000 TAMPER_IRQHandler
00000214 g     F .text	0000000c .hidden __riscv_restore_0
00003232 g     F .text	0000002c _read_r
000001d6 g     F .text	0000001a .hidden __riscv_save_7
00000668  w      .text	00000000 CAN2_RX0_IRQHandler
00001fb4 g     F .text	000000ac .hidden __extendsfdf2
00000668  w      .text	00000000 TIM8_UP_IRQHandler
000009ae g     F .text	0000001e RCC_APB1PeriphClockCmd
00000668  w      .text	00000000 Ecall_M_Mode_Handler
20007800 g       .stack	00000000 _heap_end
00003276 g     F .text	0000000c _isatty
200000b8 g     O .data	00000004 _global_impure_ptr
0000020a g     F .text	00000016 .hidden __riscv_restore_5
00000eec g     F .text	00000024 DS18B20_IO_IN
00000a2c g     F .text	0000000a SPI_I2S_GetFlagStatus
00000668  w      .text	00000000 DMA2_Channel2_IRQHandler
00000668  w      .text	00000000 DMA1_Channel4_IRQHandler
00001bac g     F .text	00000026 _sbrk
200000e0 g     O .bss	00000010 ds18b20
00000668  w      .text	00000000 TIM9_UP_IRQHandler
0000020a g     F .text	00000016 .hidden __riscv_restore_6
00000668  w      .text	00000000 USART3_IRQHandler
200000c3 g     O .bss	00000001 key_val
00000668  w      .text	00000000 RTC_IRQHandler
2000012c g       .bss	00000000 _ebss
00000668  w      .text	00000000 DMA1_Channel7_IRQHandler
00000ae0 g     F .text	00000082 TIM_OC1Init
00000668  w      .text	00000000 CAN1_RX1_IRQHandler
00001ab4 g     F .text	0000002a Delay_Init
00000668  w      .text	00000000 DVP_IRQHandler
00000668  w      .text	00000000 UART5_IRQHandler
00001200 g     F .text	00000200 key_read
200000c1 g     O .bss	00000001 key_old
00001572 g     F .text	00000038 LCD_WR_DATA
00001400 g     F .text	000000dc SPI_LCD_Init
000007be g     F .text	00000004 GPIO_SetBits
00000668  w      .text	00000000 TIM4_IRQHandler
00000b8c g     F .text	0000001a TIM_ARRPreloadConfig
000001c8 g     F .text	00000028 .hidden __riscv_save_9
00001a5c g     F .text	00000058 WaterPump_Control
00000d02 g     F .text	00000020 BrewingControl_Init
00000668  w      .text	00000000 DMA2_Channel1_IRQHandler
00001122 g     F .text	00000016 Heater_Stop
00003538 g     O .text	00000020 __sf_fake_stderr
000001d6 g     F .text	0000001a .hidden __riscv_save_4
00000668  w      .text	00000000 I2C1_EV_IRQHandler
00000bb8 g     F .text	00000018 TIM_GetITStatus
0000081a g     F .text	00000176 RCC_GetClocksFreq
00000668  w      .text	00000000 DMA1_Channel6_IRQHandler
00002060 g     F .text	0000006e .hidden __clzsi2
000029f6 g     F .text	00000042 __sfputs_r
00000668  w      .text	00000000 UART4_IRQHandler
00000668  w      .text	00000000 DMA2_Channel4_IRQHandler
00003216 g     F .text	00000018 memchr
00000bdc g     F .text	0000008e USART_Init
00000f94 g     F .text	00000044 DS18B20_WriteBit
00002852 g     F .text	000000a8 _free_r
0000036c g     F .text	0000002c TIM3_IRQHandler
00000668  w      .text	00000000 RCC_IRQHandler
0000197e g     F .text	00000032 WaterLevel_Init
000001f0 g     F .text	0000000c .hidden __riscv_save_3
00001058 g     F .text	00000020 DS18B20_StartConvert
00000668  w      .text	00000000 TIM1_TRG_COM_IRQHandler
00000668  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
00000668  w      .text	00000000 DMA2_Channel7_IRQHandler
20000024 g     O .data	00000010 AHBPrescTable
00003282 g     F .text	0000000c _lseek
00000398 g     F .text	0000003c scheduler_run
00001eee g     F .text	000000c6 .hidden __floatsisf
00000668  w      .text	00000000 EXTI15_10_IRQHandler
0000110c g     F .text	00000016 Heater_Start
00001078 g     F .text	0000004c DS18B20_ReadTempRaw
00000b7a g     F .text	00000012 TIM_ITConfig
00001018 g     F .text	0000001c DS18B20_WriteByte
000007c2 g     F .text	00000004 GPIO_ResetBits
00000668  w      .text	00000000 TIM7_IRQHandler
00003170 g     F .text	00000028 _close_r
00000668  w      .text	00000000 CAN2_TX_IRQHandler
20000000 g       .dalign	00000000 _data_vma
00000668  w      .text	00000000 TIM5_IRQHandler
200000c5 g     O .bss	00000001 task_num
00000f40 g     F .text	00000054 DS18B20_Check
000022aa g     F .text	000000fc __swsetup_r
00000668  w      .text	00000000 EXTI9_5_IRQHandler
0000265e g     F .text	000000a0 __sfp
00000cc4 g     F .text	0000003e Delay_ms
000001c8 g     F .text	00000028 .hidden __riscv_save_10
00003098 g     F .text	0000002c __sread
000019b0 g     F .text	00000010 delay_us
00000668  w      .text	00000000 ETH_WKUP_IRQHandler
0000322e g     F .text	00000002 __malloc_lock
0000020a g     F .text	00000016 .hidden __riscv_restore_4
00000200 g     F .text	00000020 .hidden __riscv_restore_8
00000fd8 g     F .text	00000040 DS18B20_ReadBit
000024d6 g     F .text	00000066 _fflush_r
000001d6 g     F .text	0000001a .hidden __riscv_save_6
00000668  w      .text	00000000 SPI2_IRQHandler
00001542 g     F .text	00000030 LCD_WR_DATA8
00003558 g     O .text	00000020 __sf_fake_stdin
0000162e g     F .text	00000206 LCD_Init
00000220 g     F .text	000000a8 memset
00000200 g     F .text	00000020 .hidden __riscv_restore_9
0000020a g     F .text	00000016 .hidden __riscv_restore_7
000003d4 g     F .text	0000006c main
000019c0 g     F .text	0000001a delay_ms
00000668  w      .text	00000000 TIM10_BRK_IRQHandler
00001bd2 g     F .text	0000008c .hidden __ltsf2
00000668  w      .text	00000000 TIM9_CC_IRQHandler
0000313c g     F .text	00000006 __sclose
00000668  w      .text	00000000 DMA2_Channel5_IRQHandler
000028fa g     F .text	000000d4 _malloc_r
20000108 g     O .bss	00000020 g_pump_ctrl
00000668  w      .text	00000000 DMA1_Channel5_IRQHandler
00000668  w      .text	00000000 EXTI4_IRQHandler
00001ade g     F .text	00000036 Delay_Ms
00000668  w      .text	00000000 USB_LP_CAN1_RX0_IRQHandler
00001c5e g     F .text	00000290 .hidden __mulsf3
00000a0a g     F .text	0000001a SPI_Cmd
00000440 g     F .text	000000fa SystemInit
00000668  w      .text	00000000 RNG_IRQHandler
00000df8 g     F .text	000000ce BrewingControl_Task
00001138 g     F .text	00000036 Heater_Init
00001834 g     F .text	00000042 LCD_Fill
000020ce g     F .text	00000040 iprintf
00000668  w      .text	00000000 USB_HP_CAN1_TX_IRQHandler
00003438 g     O .text	00000100 .hidden __clz_tab
00000000 g       .init	00000000 _sinit
000010c4 g     F .text	00000048 DS18B20_ReadRealtimeTemp
00003142 g     F .text	0000002e _write_r
00000668  w      .text	00000000 DMA1_Channel3_IRQHandler
000015da g     F .text	00000054 LCD_Address_Set
00000316 g     F .text	00000056 Tim3_Init
200000f0 g     O .bss	00000018 g_brewing_ctrl
00000668  w      .text	00000000 ETH_IRQHandler
00002cc0 g     F .text	0000010c _printf_common
200000b4 g     O .data	00000004 _impure_ptr
00000668  w      .text	00000000 TIM1_UP_IRQHandler
000023a6 g     F .text	00000130 __sflush_r
000002da g     F .text	00000002 lcd_proc
00000668  w      .text	00000000 WWDG_IRQHandler
00000668  w      .text	00000000 USBHSWakeup_IRQHandler
00000668  w      .text	00000000 DMA2_Channel11_IRQHandler
00000668  w      .text	00000000 Ecall_U_Mode_Handler
00000668  w      .text	00000000 DMA2_Channel6_IRQHandler
00000668  w      .text	00000000 TIM2_IRQHandler
20008000 g       .stack	00000000 _eusrstack
000001f0 g     F .text	0000000c .hidden __riscv_save_2
00000668  w      .text	00000000 SW_Handler
00000668  w      .text	00000000 TIM1_BRK_IRQHandler
00002768 g     F .text	00000058 __swhatbuf_r
00000c80 g     F .text	00000008 USART_SendData
00000668  w      .text	00000000 DMA2_Channel10_IRQHandler
00000668  w      .text	00000000 EXTI1_IRQHandler
200000c0 g     O .bss	00000001 key_down
000001d6 g     F .text	0000001a .hidden __riscv_save_5
00001b6e g     F .text	0000003e _write
200000c2 g     O .bss	00000001 key_up
200000c0 g       .data	00000000 _edata
2000012c g       .bss	00000000 _end
00000a36 g     F .text	000000aa TIM_TimeBaseInit
00000668  w      .text	00000000 RTCAlarm_IRQHandler
000035d4 g       .dlalign	00000000 _data_lma
00000668  w      .text	00000000 TIM10_UP_IRQHandler
00000668  w      .text	00000000 TIM9_TRG_COM_IRQHandler
00000668  w      .text	00000000 UART7_IRQHandler
00000668  w      .text	00000000 USART2_IRQHandler
00000668  w      .text	00000000 UART6_IRQHandler
000030c4 g     F .text	00000048 __swrite
00002a38 g     F .text	00000288 _vfiprintf_r
000026fe g     F .text	0000006a _fwalk_reent
0000053a g     F .text	0000012e SystemCoreClockUpdate
00000668  w      .text	00000000 I2C2_ER_IRQHandler
00000668  w      .text	00000000 DMA1_Channel2_IRQHandler
00003578 g     O .text	00000020 __sf_fake_stdout
000001fc g     F .text	00000024 .hidden __riscv_restore_12
00000668  w      .text	00000000 TIM8_BRK_IRQHandler
0000328e g     F .text	0000000c _read
0000066a  w      .text	00000000 handle_reset
00000668  w      .text	00000000 CAN1_SCE_IRQHandler
00000668  w      .text	00000000 FLASH_IRQHandler
000001f0 g     F .text	0000000c .hidden __riscv_save_0
00000668  w      .text	00000000 USART1_IRQHandler
000027c0 g     F .text	00000092 __smakebuf_r
00002dcc g     F .text	000002a2 _printf_i
000014dc g     F .text	00000004 SPI3_IRQHandler
200000dc g     O .bss	00000004 __malloc_sbrk_start
00000668  w      .text	00000000 I2C1_ER_IRQHandler
00000c92 g     F .text	00000032 Delay_us
000009cc g     F .text	0000003e SPI_Init
000007c6 g     F .text	00000006 NVIC_PriorityGroupConfig
200000d8 g     O .bss	00000004 __malloc_free_list
00000214 g     F .text	0000000c .hidden __riscv_restore_1
00002a38 g     F .text	00000288 _vfprintf_r
00000bb4 g     F .text	00000004 TIM_SetCompare1
000007b4 g     F .text	0000000a GPIO_ReadInputDataBit
200000c8 g     O .bss	00000004 uwtick
00000668  w      .text	00000000 USBWakeUp_IRQHandler
0000325e g     F .text	0000000c _close
00000668  w      .text	00000000 DMA2_Channel3_IRQHandler



Disassembly of section .init:

00000000 <_sinit>:
   0:	66a0006f          	j	66a <handle_reset>

Disassembly of section .vector:

00000004 <_vector_base>:
	...
   c:	02c8                	addi	a0,sp,324
   e:	0000                	unimp
  10:	02ca                	slli	t0,t0,0x12
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0668                	addi	a0,sp,780
	...
  22:	0000                	unimp
  24:	0668                	addi	a0,sp,780
  26:	0000                	unimp
  28:	0668                	addi	a0,sp,780
	...
  32:	0000                	unimp
  34:	0668                	addi	a0,sp,780
  36:	0000                	unimp
  38:	0000                	unimp
  3a:	0000                	unimp
  3c:	0668                	addi	a0,sp,780
  3e:	0000                	unimp
  40:	0000                	unimp
  42:	0000                	unimp
  44:	0668                	addi	a0,sp,780
  46:	0000                	unimp
  48:	0668                	addi	a0,sp,780
  4a:	0000                	unimp
  4c:	0668                	addi	a0,sp,780
  4e:	0000                	unimp
  50:	0668                	addi	a0,sp,780
  52:	0000                	unimp
  54:	0668                	addi	a0,sp,780
  56:	0000                	unimp
  58:	0668                	addi	a0,sp,780
  5a:	0000                	unimp
  5c:	0668                	addi	a0,sp,780
  5e:	0000                	unimp
  60:	0668                	addi	a0,sp,780
  62:	0000                	unimp
  64:	0668                	addi	a0,sp,780
  66:	0000                	unimp
  68:	0668                	addi	a0,sp,780
  6a:	0000                	unimp
  6c:	0668                	addi	a0,sp,780
  6e:	0000                	unimp
  70:	0668                	addi	a0,sp,780
  72:	0000                	unimp
  74:	0668                	addi	a0,sp,780
  76:	0000                	unimp
  78:	0668                	addi	a0,sp,780
  7a:	0000                	unimp
  7c:	0668                	addi	a0,sp,780
  7e:	0000                	unimp
  80:	0668                	addi	a0,sp,780
  82:	0000                	unimp
  84:	0668                	addi	a0,sp,780
  86:	0000                	unimp
  88:	0668                	addi	a0,sp,780
  8a:	0000                	unimp
  8c:	0668                	addi	a0,sp,780
  8e:	0000                	unimp
  90:	0668                	addi	a0,sp,780
  92:	0000                	unimp
  94:	0668                	addi	a0,sp,780
  96:	0000                	unimp
  98:	0668                	addi	a0,sp,780
  9a:	0000                	unimp
  9c:	0668                	addi	a0,sp,780
  9e:	0000                	unimp
  a0:	0668                	addi	a0,sp,780
  a2:	0000                	unimp
  a4:	0668                	addi	a0,sp,780
  a6:	0000                	unimp
  a8:	0668                	addi	a0,sp,780
  aa:	0000                	unimp
  ac:	0668                	addi	a0,sp,780
  ae:	0000                	unimp
  b0:	0668                	addi	a0,sp,780
  b2:	0000                	unimp
  b4:	0668                	addi	a0,sp,780
  b6:	0000                	unimp
  b8:	036c                	addi	a1,sp,396
  ba:	0000                	unimp
  bc:	0668                	addi	a0,sp,780
  be:	0000                	unimp
  c0:	0668                	addi	a0,sp,780
  c2:	0000                	unimp
  c4:	0668                	addi	a0,sp,780
  c6:	0000                	unimp
  c8:	0668                	addi	a0,sp,780
  ca:	0000                	unimp
  cc:	0668                	addi	a0,sp,780
  ce:	0000                	unimp
  d0:	0668                	addi	a0,sp,780
  d2:	0000                	unimp
  d4:	0668                	addi	a0,sp,780
  d6:	0000                	unimp
  d8:	0668                	addi	a0,sp,780
  da:	0000                	unimp
  dc:	0668                	addi	a0,sp,780
  de:	0000                	unimp
  e0:	0668                	addi	a0,sp,780
  e2:	0000                	unimp
  e4:	0668                	addi	a0,sp,780
  e6:	0000                	unimp
  e8:	0668                	addi	a0,sp,780
  ea:	0000                	unimp
  ec:	0668                	addi	a0,sp,780
  ee:	0000                	unimp
  f0:	0668                	addi	a0,sp,780
  f2:	0000                	unimp
  f4:	0668                	addi	a0,sp,780
  f6:	0000                	unimp
  f8:	0668                	addi	a0,sp,780
  fa:	0000                	unimp
  fc:	0668                	addi	a0,sp,780
  fe:	0000                	unimp
 100:	0668                	addi	a0,sp,780
 102:	0000                	unimp
 104:	0000                	unimp
 106:	0000                	unimp
 108:	0668                	addi	a0,sp,780
 10a:	0000                	unimp
 10c:	0668                	addi	a0,sp,780
 10e:	0000                	unimp
 110:	14dc                	addi	a5,sp,612
 112:	0000                	unimp
 114:	0668                	addi	a0,sp,780
 116:	0000                	unimp
 118:	0668                	addi	a0,sp,780
 11a:	0000                	unimp
 11c:	0668                	addi	a0,sp,780
 11e:	0000                	unimp
 120:	0668                	addi	a0,sp,780
 122:	0000                	unimp
 124:	0668                	addi	a0,sp,780
 126:	0000                	unimp
 128:	0668                	addi	a0,sp,780
 12a:	0000                	unimp
 12c:	0668                	addi	a0,sp,780
 12e:	0000                	unimp
 130:	0668                	addi	a0,sp,780
 132:	0000                	unimp
 134:	0668                	addi	a0,sp,780
 136:	0000                	unimp
 138:	0668                	addi	a0,sp,780
 13a:	0000                	unimp
 13c:	0668                	addi	a0,sp,780
 13e:	0000                	unimp
 140:	0668                	addi	a0,sp,780
 142:	0000                	unimp
 144:	0668                	addi	a0,sp,780
 146:	0000                	unimp
 148:	0668                	addi	a0,sp,780
 14a:	0000                	unimp
 14c:	0668                	addi	a0,sp,780
 14e:	0000                	unimp
 150:	0668                	addi	a0,sp,780
 152:	0000                	unimp
 154:	0668                	addi	a0,sp,780
 156:	0000                	unimp
 158:	0668                	addi	a0,sp,780
 15a:	0000                	unimp
 15c:	0668                	addi	a0,sp,780
 15e:	0000                	unimp
 160:	0668                	addi	a0,sp,780
 162:	0000                	unimp
 164:	0668                	addi	a0,sp,780
 166:	0000                	unimp
 168:	0668                	addi	a0,sp,780
 16a:	0000                	unimp
 16c:	0668                	addi	a0,sp,780
 16e:	0000                	unimp
 170:	0668                	addi	a0,sp,780
 172:	0000                	unimp
 174:	0668                	addi	a0,sp,780
 176:	0000                	unimp
 178:	0668                	addi	a0,sp,780
 17a:	0000                	unimp
 17c:	0668                	addi	a0,sp,780
 17e:	0000                	unimp
 180:	0668                	addi	a0,sp,780
 182:	0000                	unimp
 184:	0668                	addi	a0,sp,780
 186:	0000                	unimp
 188:	0668                	addi	a0,sp,780
 18a:	0000                	unimp
 18c:	0668                	addi	a0,sp,780
 18e:	0000                	unimp
 190:	0668                	addi	a0,sp,780
 192:	0000                	unimp
 194:	0668                	addi	a0,sp,780
 196:	0000                	unimp
 198:	0668                	addi	a0,sp,780
 19a:	0000                	unimp
 19c:	0668                	addi	a0,sp,780
 19e:	0000                	unimp
 1a0:	0668                	addi	a0,sp,780
	...

Disassembly of section .text:

000001c0 <__riscv_save_12>:
     1c0:	7139                	addi	sp,sp,-64
     1c2:	4301                	li	t1,0
     1c4:	c66e                	sw	s11,12(sp)
     1c6:	a019                	j	1cc <__riscv_save_10+0x4>

000001c8 <__riscv_save_10>:
     1c8:	7139                	addi	sp,sp,-64
     1ca:	5341                	li	t1,-16
     1cc:	c86a                	sw	s10,16(sp)
     1ce:	ca66                	sw	s9,20(sp)
     1d0:	cc62                	sw	s8,24(sp)
     1d2:	ce5e                	sw	s7,28(sp)
     1d4:	a019                	j	1da <__riscv_save_4+0x4>

000001d6 <__riscv_save_4>:
     1d6:	7139                	addi	sp,sp,-64
     1d8:	5301                	li	t1,-32
     1da:	d05a                	sw	s6,32(sp)
     1dc:	d256                	sw	s5,36(sp)
     1de:	d452                	sw	s4,40(sp)
     1e0:	d64e                	sw	s3,44(sp)
     1e2:	d84a                	sw	s2,48(sp)
     1e4:	da26                	sw	s1,52(sp)
     1e6:	dc22                	sw	s0,56(sp)
     1e8:	de06                	sw	ra,60(sp)
     1ea:	40610133          	sub	sp,sp,t1
     1ee:	8282                	jr	t0

000001f0 <__riscv_save_0>:
     1f0:	1141                	addi	sp,sp,-16
     1f2:	c04a                	sw	s2,0(sp)
     1f4:	c226                	sw	s1,4(sp)
     1f6:	c422                	sw	s0,8(sp)
     1f8:	c606                	sw	ra,12(sp)
     1fa:	8282                	jr	t0

000001fc <__riscv_restore_12>:
     1fc:	4db2                	lw	s11,12(sp)
     1fe:	0141                	addi	sp,sp,16

00000200 <__riscv_restore_10>:
     200:	4d02                	lw	s10,0(sp)
     202:	4c92                	lw	s9,4(sp)
     204:	4c22                	lw	s8,8(sp)
     206:	4bb2                	lw	s7,12(sp)
     208:	0141                	addi	sp,sp,16

0000020a <__riscv_restore_4>:
     20a:	4b02                	lw	s6,0(sp)
     20c:	4a92                	lw	s5,4(sp)
     20e:	4a22                	lw	s4,8(sp)
     210:	49b2                	lw	s3,12(sp)
     212:	0141                	addi	sp,sp,16

00000214 <__riscv_restore_0>:
     214:	4902                	lw	s2,0(sp)
     216:	4492                	lw	s1,4(sp)
     218:	4422                	lw	s0,8(sp)
     21a:	40b2                	lw	ra,12(sp)
     21c:	0141                	addi	sp,sp,16
     21e:	8082                	ret

00000220 <memset>:
     220:	433d                	li	t1,15
     222:	872a                	mv	a4,a0
     224:	02c37363          	bgeu	t1,a2,24a <memset+0x2a>
     228:	00f77793          	andi	a5,a4,15
     22c:	efbd                	bnez	a5,2aa <memset+0x8a>
     22e:	e5ad                	bnez	a1,298 <memset+0x78>
     230:	ff067693          	andi	a3,a2,-16
     234:	8a3d                	andi	a2,a2,15
     236:	96ba                	add	a3,a3,a4
     238:	c30c                	sw	a1,0(a4)
     23a:	c34c                	sw	a1,4(a4)
     23c:	c70c                	sw	a1,8(a4)
     23e:	c74c                	sw	a1,12(a4)
     240:	0741                	addi	a4,a4,16
     242:	fed76be3          	bltu	a4,a3,238 <memset+0x18>
     246:	e211                	bnez	a2,24a <memset+0x2a>
     248:	8082                	ret
     24a:	40c306b3          	sub	a3,t1,a2
     24e:	068a                	slli	a3,a3,0x2
     250:	00000297          	auipc	t0,0x0
     254:	9696                	add	a3,a3,t0
     256:	00a68067          	jr	10(a3)
     25a:	00b70723          	sb	a1,14(a4)
     25e:	00b706a3          	sb	a1,13(a4)
     262:	00b70623          	sb	a1,12(a4)
     266:	00b705a3          	sb	a1,11(a4)
     26a:	00b70523          	sb	a1,10(a4)
     26e:	00b704a3          	sb	a1,9(a4)
     272:	00b70423          	sb	a1,8(a4)
     276:	00b703a3          	sb	a1,7(a4)
     27a:	00b70323          	sb	a1,6(a4)
     27e:	00b702a3          	sb	a1,5(a4)
     282:	00b70223          	sb	a1,4(a4)
     286:	00b701a3          	sb	a1,3(a4)
     28a:	00b70123          	sb	a1,2(a4)
     28e:	00b700a3          	sb	a1,1(a4)
     292:	00b70023          	sb	a1,0(a4)
     296:	8082                	ret
     298:	0ff5f593          	andi	a1,a1,255
     29c:	00859693          	slli	a3,a1,0x8
     2a0:	8dd5                	or	a1,a1,a3
     2a2:	01059693          	slli	a3,a1,0x10
     2a6:	8dd5                	or	a1,a1,a3
     2a8:	b761                	j	230 <memset+0x10>
     2aa:	00279693          	slli	a3,a5,0x2
     2ae:	00000297          	auipc	t0,0x0
     2b2:	9696                	add	a3,a3,t0
     2b4:	8286                	mv	t0,ra
     2b6:	fa8680e7          	jalr	-88(a3)
     2ba:	8096                	mv	ra,t0
     2bc:	17c1                	addi	a5,a5,-16
     2be:	8f1d                	sub	a4,a4,a5
     2c0:	963e                	add	a2,a2,a5
     2c2:	f8c374e3          	bgeu	t1,a2,24a <memset+0x2a>
     2c6:	b7a5                	j	22e <memset+0xe>

000002c8 <NMI_Handler>:
     2c8:	a001                	j	2c8 <NMI_Handler>

000002ca <HardFault_Handler>:
     2ca:	beef07b7          	lui	a5,0xbeef0
     2ce:	e000e737          	lui	a4,0xe000e
     2d2:	08078793          	addi	a5,a5,128 # beef0080 <_eusrstack+0x9eee8080>
     2d6:	c73c                	sw	a5,72(a4)
     2d8:	a001                	j	2d8 <HardFault_Handler+0xe>

000002da <lcd_proc>:
     2da:	8082                	ret

000002dc <key_proc>:
     2dc:	f15ff2ef          	jal	t0,1f0 <__riscv_save_0>
     2e0:	721000ef          	jal	ra,1200 <key_read>
     2e4:	81918713          	addi	a4,gp,-2023 # 200000c1 <key_old>
     2e8:	80a18da3          	sb	a0,-2021(gp) # 200000c3 <key_val>
     2ec:	231c                	lbu	a5,0(a4)
     2ee:	a308                	sb	a0,0(a4)
     2f0:	00f54633          	xor	a2,a0,a5
     2f4:	fff7c793          	not	a5,a5
     2f8:	80c18e23          	sb	a2,-2020(gp) # 200000c4 <p>
     2fc:	8fe9                	and	a5,a5,a0
     2fe:	80f18c23          	sb	a5,-2024(gp) # 200000c0 <_edata>
     302:	fff54693          	not	a3,a0
     306:	8ef1                	and	a3,a3,a2
     308:	80d18d23          	sb	a3,-2022(gp) # 200000c2 <key_up>
     30c:	c781                	beqz	a5,314 <key_proc+0x38>
     30e:	853e                	mv	a0,a5
     310:	235000ef          	jal	ra,d44 <BrewingControl_KeyHandler>
     314:	b701                	j	214 <__riscv_restore_0>

00000316 <Tim3_Init>:
     316:	edbff2ef          	jal	t0,1f0 <__riscv_save_0>
     31a:	1101                	addi	sp,sp,-32
     31c:	84aa                	mv	s1,a0
     31e:	842e                	mv	s0,a1
     320:	4509                	li	a0,2
     322:	4585                	li	a1,1
     324:	2569                	jal	9ae <RCC_APB1PeriphClockCmd>
     326:	82e0                	sh	s0,20(sp)
     328:	40000437          	lui	s0,0x40000
     32c:	084c                	addi	a1,sp,20
     32e:	40040513          	addi	a0,s0,1024 # 40000400 <_eusrstack+0x1fff8400>
     332:	84e4                	sh	s1,24(sp)
     334:	00011d23          	sh	zero,26(sp)
     338:	00011b23          	sh	zero,22(sp)
     33c:	2ded                	jal	a36 <TIM_TimeBaseInit>
     33e:	4605                	li	a2,1
     340:	04100593          	li	a1,65
     344:	40040513          	addi	a0,s0,1024
     348:	033000ef          	jal	ra,b7a <TIM_ITConfig>
     34c:	12d00793          	li	a5,301
     350:	867c                	sh	a5,12(sp)
     352:	478d                	li	a5,3
     354:	875c                	sb	a5,14(sp)
     356:	0068                	addi	a0,sp,12
     358:	4785                	li	a5,1
     35a:	c83e                	sw	a5,16(sp)
     35c:	2985                	jal	7cc <NVIC_Init>
     35e:	4585                	li	a1,1
     360:	40040513          	addi	a0,s0,1024
     364:	7fe000ef          	jal	ra,b62 <TIM_Cmd>
     368:	6105                	addi	sp,sp,32
     36a:	b56d                	j	214 <__riscv_restore_0>

0000036c <TIM3_IRQHandler>:
     36c:	40000537          	lui	a0,0x40000
     370:	4585                	li	a1,1
     372:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     376:	043000ef          	jal	ra,bb8 <TIM_GetITStatus>
     37a:	c511                	beqz	a0,386 <TIM3_IRQHandler+0x1a>
     37c:	82018793          	addi	a5,gp,-2016 # 200000c8 <uwtick>
     380:	4398                	lw	a4,0(a5)
     382:	0705                	addi	a4,a4,1
     384:	c398                	sw	a4,0(a5)
     386:	40000537          	lui	a0,0x40000
     38a:	4585                	li	a1,1
     38c:	40050513          	addi	a0,a0,1024 # 40000400 <_eusrstack+0x1fff8400>
     390:	041000ef          	jal	ra,bd0 <TIM_ClearITPendingBit>
     394:	30200073          	mret

00000398 <scheduler_run>:
     398:	e3fff2ef          	jal	t0,1d6 <__riscv_save_4>
     39c:	200004b7          	lui	s1,0x20000
     3a0:	4401                	li	s0,0
     3a2:	00048493          	mv	s1,s1
     3a6:	4a31                	li	s4,12
     3a8:	81d1c783          	lbu	a5,-2019(gp) # 200000c5 <task_num>
     3ac:	00f46363          	bltu	s0,a5,3b2 <scheduler_run+0x1a>
     3b0:	bda9                	j	20a <__riscv_restore_4>
     3b2:	034407b3          	mul	a5,s0,s4
     3b6:	8201a683          	lw	a3,-2016(gp) # 200000c8 <uwtick>
     3ba:	97a6                	add	a5,a5,s1
     3bc:	43d8                	lw	a4,4(a5)
     3be:	4790                	lw	a2,8(a5)
     3c0:	9732                	add	a4,a4,a2
     3c2:	00e6e563          	bltu	a3,a4,3cc <scheduler_run+0x34>
     3c6:	c794                	sw	a3,8(a5)
     3c8:	439c                	lw	a5,0(a5)
     3ca:	9782                	jalr	a5
     3cc:	0405                	addi	s0,s0,1
     3ce:	0ff47413          	andi	s0,s0,255
     3d2:	bfd9                	j	3a8 <scheduler_run+0x10>

000003d4 <main>:
     3d4:	e1dff2ef          	jal	t0,1f0 <__riscv_save_0>
     3d8:	4509                	li	a0,2
     3da:	26f5                	jal	7c6 <NVIC_PriorityGroupConfig>
     3dc:	2ab9                	jal	53a <SystemCoreClockUpdate>
     3de:	6571                	lui	a0,0x1c
     3e0:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x18c2c>
     3e4:	730010ef          	jal	ra,1b14 <USART_Printf_Init>
     3e8:	6cc010ef          	jal	ra,1ab4 <Delay_Init>
     3ec:	54d000ef          	jal	ra,1138 <Heater_Init>
     3f0:	5ea010ef          	jal	ra,19da <WaterPump_Init>
     3f4:	58a010ef          	jal	ra,197e <WaterLevel_Init>
     3f8:	47e010ef          	jal	ra,1876 <TIM2_PWM_Init>
     3fc:	107000ef          	jal	ra,d02 <BrewingControl_Init>
     400:	22e010ef          	jal	ra,162e <LCD_Init>
     404:	6741                	lui	a4,0x10
     406:	177d                	addi	a4,a4,-1
     408:	07f00693          	li	a3,127
     40c:	07f00613          	li	a2,127
     410:	4581                	li	a1,0
     412:	4501                	li	a0,0
     414:	420010ef          	jal	ra,1834 <LCD_Fill>
     418:	557000ef          	jal	ra,116e <key_init>
     41c:	05f00593          	li	a1,95
     420:	3e800513          	li	a0,1000
     424:	3dcd                	jal	316 <Tim3_Init>
     426:	00003537          	lui	a0,0x3
     42a:	470d                	li	a4,3
     42c:	29c50513          	addi	a0,a0,668 # 329c <_read+0xe>
     430:	80e18ea3          	sb	a4,-2019(gp) # 200000c5 <task_num>
     434:	5af010ef          	jal	ra,21e2 <puts>
     438:	0eb000ef          	jal	ra,d22 <BrewingControl_Start>
     43c:	3fb1                	jal	398 <scheduler_run>
     43e:	bffd                	j	43c <main+0x68>

00000440 <SystemInit>:
     440:	400217b7          	lui	a5,0x40021
     444:	4398                	lw	a4,0(a5)
     446:	f0ff06b7          	lui	a3,0xf0ff0
     44a:	1141                	addi	sp,sp,-16
     44c:	00176713          	ori	a4,a4,1
     450:	c398                	sw	a4,0(a5)
     452:	43d8                	lw	a4,4(a5)
     454:	00020637          	lui	a2,0x20
     458:	8f75                	and	a4,a4,a3
     45a:	c3d8                	sw	a4,4(a5)
     45c:	4398                	lw	a4,0(a5)
     45e:	fef706b7          	lui	a3,0xfef70
     462:	16fd                	addi	a3,a3,-1
     464:	8f75                	and	a4,a4,a3
     466:	c398                	sw	a4,0(a5)
     468:	4398                	lw	a4,0(a5)
     46a:	fffc06b7          	lui	a3,0xfffc0
     46e:	16fd                	addi	a3,a3,-1
     470:	8f75                	and	a4,a4,a3
     472:	c398                	sw	a4,0(a5)
     474:	43d8                	lw	a4,4(a5)
     476:	ff0106b7          	lui	a3,0xff010
     47a:	16fd                	addi	a3,a3,-1
     47c:	8f75                	and	a4,a4,a3
     47e:	c3d8                	sw	a4,4(a5)
     480:	4398                	lw	a4,0(a5)
     482:	ec0006b7          	lui	a3,0xec000
     486:	16fd                	addi	a3,a3,-1
     488:	8f75                	and	a4,a4,a3
     48a:	c398                	sw	a4,0(a5)
     48c:	00ff0737          	lui	a4,0xff0
     490:	c798                	sw	a4,8(a5)
     492:	0207a623          	sw	zero,44(a5) # 4002102c <_eusrstack+0x2001902c>
     496:	c402                	sw	zero,8(sp)
     498:	c602                	sw	zero,12(sp)
     49a:	4398                	lw	a4,0(a5)
     49c:	66c1                	lui	a3,0x10
     49e:	8f55                	or	a4,a4,a3
     4a0:	c398                	sw	a4,0(a5)
     4a2:	400216b7          	lui	a3,0x40021
     4a6:	6705                	lui	a4,0x1
     4a8:	429c                	lw	a5,0(a3)
     4aa:	8ff1                	and	a5,a5,a2
     4ac:	c63e                	sw	a5,12(sp)
     4ae:	47a2                	lw	a5,8(sp)
     4b0:	0785                	addi	a5,a5,1
     4b2:	c43e                	sw	a5,8(sp)
     4b4:	47b2                	lw	a5,12(sp)
     4b6:	e781                	bnez	a5,4be <SystemInit+0x7e>
     4b8:	47a2                	lw	a5,8(sp)
     4ba:	fee797e3          	bne	a5,a4,4a8 <SystemInit+0x68>
     4be:	400217b7          	lui	a5,0x40021
     4c2:	439c                	lw	a5,0(a5)
     4c4:	00e79713          	slli	a4,a5,0xe
     4c8:	06075763          	bgez	a4,536 <SystemInit+0xf6>
     4cc:	4785                	li	a5,1
     4ce:	c63e                	sw	a5,12(sp)
     4d0:	4732                	lw	a4,12(sp)
     4d2:	4785                	li	a5,1
     4d4:	04f71f63          	bne	a4,a5,532 <SystemInit+0xf2>
     4d8:	400217b7          	lui	a5,0x40021
     4dc:	43d8                	lw	a4,4(a5)
     4de:	ffc106b7          	lui	a3,0xffc10
     4e2:	16fd                	addi	a3,a3,-1
     4e4:	c3d8                	sw	a4,4(a5)
     4e6:	43d8                	lw	a4,4(a5)
     4e8:	c3d8                	sw	a4,4(a5)
     4ea:	43d8                	lw	a4,4(a5)
     4ec:	40076713          	ori	a4,a4,1024
     4f0:	c3d8                	sw	a4,4(a5)
     4f2:	43d8                	lw	a4,4(a5)
     4f4:	8f75                	and	a4,a4,a3
     4f6:	c3d8                	sw	a4,4(a5)
     4f8:	43d8                	lw	a4,4(a5)
     4fa:	002906b7          	lui	a3,0x290
     4fe:	8f55                	or	a4,a4,a3
     500:	c3d8                	sw	a4,4(a5)
     502:	4398                	lw	a4,0(a5)
     504:	010006b7          	lui	a3,0x1000
     508:	8f55                	or	a4,a4,a3
     50a:	c398                	sw	a4,0(a5)
     50c:	4398                	lw	a4,0(a5)
     50e:	00671693          	slli	a3,a4,0x6
     512:	fe06dde3          	bgez	a3,50c <SystemInit+0xcc>
     516:	43d8                	lw	a4,4(a5)
     518:	400216b7          	lui	a3,0x40021
     51c:	9b71                	andi	a4,a4,-4
     51e:	c3d8                	sw	a4,4(a5)
     520:	43d8                	lw	a4,4(a5)
     522:	00276713          	ori	a4,a4,2
     526:	c3d8                	sw	a4,4(a5)
     528:	4721                	li	a4,8
     52a:	42dc                	lw	a5,4(a3)
     52c:	8bb1                	andi	a5,a5,12
     52e:	fee79ee3          	bne	a5,a4,52a <SystemInit+0xea>
     532:	0141                	addi	sp,sp,16
     534:	8082                	ret
     536:	c602                	sw	zero,12(sp)
     538:	bf61                	j	4d0 <SystemInit+0x90>

0000053a <SystemCoreClockUpdate>:
     53a:	400216b7          	lui	a3,0x40021
     53e:	42d8                	lw	a4,4(a3)
     540:	200007b7          	lui	a5,0x20000
     544:	4611                	li	a2,4
     546:	8b31                	andi	a4,a4,12
     548:	0a878793          	addi	a5,a5,168 # 200000a8 <SystemCoreClock>
     54c:	00c70563          	beq	a4,a2,556 <SystemCoreClockUpdate+0x1c>
     550:	4621                	li	a2,8
     552:	02c70863          	beq	a4,a2,582 <SystemCoreClockUpdate+0x48>
     556:	007a1737          	lui	a4,0x7a1
     55a:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
     55e:	c398                	sw	a4,0(a5)
     560:	40021737          	lui	a4,0x40021
     564:	4358                	lw	a4,4(a4)
     566:	8311                	srli	a4,a4,0x4
     568:	00f77693          	andi	a3,a4,15
     56c:	20000737          	lui	a4,0x20000
     570:	02470713          	addi	a4,a4,36 # 20000024 <AHBPrescTable>
     574:	9736                	add	a4,a4,a3
     576:	2314                	lbu	a3,0(a4)
     578:	4398                	lw	a4,0(a5)
     57a:	00d75733          	srl	a4,a4,a3
     57e:	c398                	sw	a4,0(a5)
     580:	8082                	ret
     582:	42d8                	lw	a4,4(a3)
     584:	42d4                	lw	a3,4(a3)
     586:	6641                	lui	a2,0x10
     588:	8349                	srli	a4,a4,0x12
     58a:	8b3d                	andi	a4,a4,15
     58c:	8ef1                	and	a3,a3,a2
     58e:	00270613          	addi	a2,a4,2
     592:	cf15                	beqz	a4,5ce <SystemCoreClockUpdate+0x94>
     594:	473d                	li	a4,15
     596:	02e60f63          	beq	a2,a4,5d4 <SystemCoreClockUpdate+0x9a>
     59a:	4741                	li	a4,16
     59c:	02e60f63          	beq	a2,a4,5da <SystemCoreClockUpdate+0xa0>
     5a0:	4745                	li	a4,17
     5a2:	4581                	li	a1,0
     5a4:	00e61363          	bne	a2,a4,5aa <SystemCoreClockUpdate+0x70>
     5a8:	4641                	li	a2,16
     5aa:	e2a1                	bnez	a3,5ea <SystemCoreClockUpdate+0xb0>
     5ac:	40024737          	lui	a4,0x40024
     5b0:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     5b4:	8b41                	andi	a4,a4,16
     5b6:	c70d                	beqz	a4,5e0 <SystemCoreClockUpdate+0xa6>
     5b8:	007a1737          	lui	a4,0x7a1
     5bc:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
     5c0:	02c70633          	mul	a2,a4,a2
     5c4:	c390                	sw	a2,0(a5)
     5c6:	ddc9                	beqz	a1,560 <SystemCoreClockUpdate+0x26>
     5c8:	4398                	lw	a4,0(a5)
     5ca:	8305                	srli	a4,a4,0x1
     5cc:	bf49                	j	55e <SystemCoreClockUpdate+0x24>
     5ce:	4581                	li	a1,0
     5d0:	4649                	li	a2,18
     5d2:	bfe1                	j	5aa <SystemCoreClockUpdate+0x70>
     5d4:	4585                	li	a1,1
     5d6:	4635                	li	a2,13
     5d8:	bfc9                	j	5aa <SystemCoreClockUpdate+0x70>
     5da:	4581                	li	a1,0
     5dc:	463d                	li	a2,15
     5de:	b7f1                	j	5aa <SystemCoreClockUpdate+0x70>
     5e0:	003d1737          	lui	a4,0x3d1
     5e4:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3cd32c>
     5e8:	bfe1                	j	5c0 <SystemCoreClockUpdate+0x86>
     5ea:	40021537          	lui	a0,0x40021
     5ee:	5558                	lw	a4,44(a0)
     5f0:	00f71693          	slli	a3,a4,0xf
     5f4:	5558                	lw	a4,44(a0)
     5f6:	0406df63          	bgez	a3,654 <SystemCoreClockUpdate+0x11a>
     5fa:	8311                	srli	a4,a4,0x4
     5fc:	8b3d                	andi	a4,a4,15
     5fe:	00170693          	addi	a3,a4,1
     602:	007a1737          	lui	a4,0x7a1
     606:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
     60a:	02d75733          	divu	a4,a4,a3
     60e:	c398                	sw	a4,0(a5)
     610:	5554                	lw	a3,44(a0)
     612:	82a1                	srli	a3,a3,0x8
     614:	8abd                	andi	a3,a3,15
     616:	e28d                	bnez	a3,638 <SystemCoreClockUpdate+0xfe>
     618:	4695                	li	a3,5
     61a:	02d70733          	mul	a4,a4,a3
     61e:	8305                	srli	a4,a4,0x1
     620:	c398                	sw	a4,0(a5)
     622:	40021737          	lui	a4,0x40021
     626:	5758                	lw	a4,44(a4)
     628:	4394                	lw	a3,0(a5)
     62a:	8b3d                	andi	a4,a4,15
     62c:	0705                	addi	a4,a4,1
     62e:	02e6d733          	divu	a4,a3,a4
     632:	c398                	sw	a4,0(a5)
     634:	4398                	lw	a4,0(a5)
     636:	b769                	j	5c0 <SystemCoreClockUpdate+0x86>
     638:	4505                	li	a0,1
     63a:	00a69463          	bne	a3,a0,642 <SystemCoreClockUpdate+0x108>
     63e:	46e5                	li	a3,25
     640:	bfe9                	j	61a <SystemCoreClockUpdate+0xe0>
     642:	453d                	li	a0,15
     644:	00a69663          	bne	a3,a0,650 <SystemCoreClockUpdate+0x116>
     648:	46d1                	li	a3,20
     64a:	02e68733          	mul	a4,a3,a4
     64e:	bfc9                	j	620 <SystemCoreClockUpdate+0xe6>
     650:	0689                	addi	a3,a3,2
     652:	bfe5                	j	64a <SystemCoreClockUpdate+0x110>
     654:	8b3d                	andi	a4,a4,15
     656:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     65a:	007a1737          	lui	a4,0x7a1
     65e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
     662:	02d75733          	divu	a4,a4,a3
     666:	b7f1                	j	632 <SystemCoreClockUpdate+0xf8>

00000668 <ADC1_2_IRQHandler>:
     668:	a001                	j	668 <ADC1_2_IRQHandler>

0000066a <handle_reset>:
     66a:	20000197          	auipc	gp,0x20000
     66e:	23e18193          	addi	gp,gp,574 # 200008a8 <__global_pointer$>
     672:	20008117          	auipc	sp,0x20008
     676:	98e10113          	addi	sp,sp,-1650 # 20008000 <_eusrstack>
     67a:	00003517          	auipc	a0,0x3
     67e:	f5a50513          	addi	a0,a0,-166 # 35d4 <_data_lma>
     682:	20000597          	auipc	a1,0x20000
     686:	97e58593          	addi	a1,a1,-1666 # 20000000 <_data_vma>
     68a:	81818613          	addi	a2,gp,-2024 # 200000c0 <_edata>
     68e:	00c5fa63          	bgeu	a1,a2,6a2 <handle_reset+0x38>
     692:	00052283          	lw	t0,0(a0)
     696:	0055a023          	sw	t0,0(a1)
     69a:	0511                	addi	a0,a0,4
     69c:	0591                	addi	a1,a1,4
     69e:	fec5eae3          	bltu	a1,a2,692 <handle_reset+0x28>
     6a2:	81818513          	addi	a0,gp,-2024 # 200000c0 <_edata>
     6a6:	88418593          	addi	a1,gp,-1916 # 2000012c <_ebss>
     6aa:	00b57763          	bgeu	a0,a1,6b8 <handle_reset+0x4e>
     6ae:	00052023          	sw	zero,0(a0)
     6b2:	0511                	addi	a0,a0,4
     6b4:	feb56de3          	bltu	a0,a1,6ae <handle_reset+0x44>
     6b8:	42fd                	li	t0,31
     6ba:	bc029073          	csrw	0xbc0,t0
     6be:	42ad                	li	t0,11
     6c0:	80429073          	csrw	0x804,t0
     6c4:	000062b7          	lui	t0,0x6
     6c8:	08828293          	addi	t0,t0,136 # 6088 <_data_lma+0x2ab4>
     6cc:	30029073          	csrw	mstatus,t0
     6d0:	00000297          	auipc	t0,0x0
     6d4:	93428293          	addi	t0,t0,-1740 # 4 <_einit>
     6d8:	0032e293          	ori	t0,t0,3
     6dc:	30529073          	csrw	mtvec,t0
     6e0:	d61ff0ef          	jal	ra,440 <SystemInit>
     6e4:	00000297          	auipc	t0,0x0
     6e8:	cf028293          	addi	t0,t0,-784 # 3d4 <main>
     6ec:	34129073          	csrw	mepc,t0
     6f0:	30200073          	mret

000006f4 <GPIO_Init>:
     6f4:	459c                	lw	a5,8(a1)
     6f6:	0107f713          	andi	a4,a5,16
     6fa:	00f7f813          	andi	a6,a5,15
     6fe:	c701                	beqz	a4,706 <GPIO_Init+0x12>
     700:	41d8                	lw	a4,4(a1)
     702:	00e86833          	or	a6,a6,a4
     706:	218e                	lhu	a1,0(a1)
     708:	0ff5f713          	andi	a4,a1,255
     70c:	c339                	beqz	a4,752 <GPIO_Init+0x5e>
     70e:	4118                	lw	a4,0(a0)
     710:	4681                	li	a3,0
     712:	4e85                	li	t4,1
     714:	4f3d                	li	t5,15
     716:	02800f93          	li	t6,40
     71a:	04800293          	li	t0,72
     71e:	4e21                	li	t3,8
     720:	00de9633          	sll	a2,t4,a3
     724:	00c5f8b3          	and	a7,a1,a2
     728:	03161163          	bne	a2,a7,74a <GPIO_Init+0x56>
     72c:	00269893          	slli	a7,a3,0x2
     730:	011f1333          	sll	t1,t5,a7
     734:	fff34313          	not	t1,t1
     738:	00e37733          	and	a4,t1,a4
     73c:	011818b3          	sll	a7,a6,a7
     740:	00e8e733          	or	a4,a7,a4
     744:	05f79f63          	bne	a5,t6,7a2 <GPIO_Init+0xae>
     748:	c950                	sw	a2,20(a0)
     74a:	0685                	addi	a3,a3,1
     74c:	fdc69ae3          	bne	a3,t3,720 <GPIO_Init+0x2c>
     750:	c118                	sw	a4,0(a0)
     752:	0ff00713          	li	a4,255
     756:	04b77563          	bgeu	a4,a1,7a0 <GPIO_Init+0xac>
     75a:	4154                	lw	a3,4(a0)
     75c:	4621                	li	a2,8
     75e:	4e85                	li	t4,1
     760:	4f3d                	li	t5,15
     762:	02800f93          	li	t6,40
     766:	04800293          	li	t0,72
     76a:	4e41                	li	t3,16
     76c:	00ce98b3          	sll	a7,t4,a2
     770:	0115f733          	and	a4,a1,a7
     774:	02e89263          	bne	a7,a4,798 <GPIO_Init+0xa4>
     778:	00261713          	slli	a4,a2,0x2
     77c:	1701                	addi	a4,a4,-32
     77e:	00ef1333          	sll	t1,t5,a4
     782:	fff34313          	not	t1,t1
     786:	00d376b3          	and	a3,t1,a3
     78a:	00e81733          	sll	a4,a6,a4
     78e:	8ed9                	or	a3,a3,a4
     790:	01f79d63          	bne	a5,t6,7aa <GPIO_Init+0xb6>
     794:	01152a23          	sw	a7,20(a0)
     798:	0605                	addi	a2,a2,1
     79a:	fdc619e3          	bne	a2,t3,76c <GPIO_Init+0x78>
     79e:	c154                	sw	a3,4(a0)
     7a0:	8082                	ret
     7a2:	fa5794e3          	bne	a5,t0,74a <GPIO_Init+0x56>
     7a6:	c910                	sw	a2,16(a0)
     7a8:	b74d                	j	74a <GPIO_Init+0x56>
     7aa:	fe5797e3          	bne	a5,t0,798 <GPIO_Init+0xa4>
     7ae:	01152823          	sw	a7,16(a0)
     7b2:	b7dd                	j	798 <GPIO_Init+0xa4>

000007b4 <GPIO_ReadInputDataBit>:
     7b4:	4508                	lw	a0,8(a0)
     7b6:	8d6d                	and	a0,a0,a1
     7b8:	00a03533          	snez	a0,a0
     7bc:	8082                	ret

000007be <GPIO_SetBits>:
     7be:	c90c                	sw	a1,16(a0)
     7c0:	8082                	ret

000007c2 <GPIO_ResetBits>:
     7c2:	c94c                	sw	a1,20(a0)
     7c4:	8082                	ret

000007c6 <NVIC_PriorityGroupConfig>:
     7c6:	82a1a223          	sw	a0,-2012(gp) # 200000cc <NVIC_Priority_Group>
     7ca:	8082                	ret

000007cc <NVIC_Init>:
     7cc:	8241a703          	lw	a4,-2012(gp) # 200000cc <NVIC_Priority_Group>
     7d0:	4789                	li	a5,2
     7d2:	2110                	lbu	a2,0(a0)
     7d4:	02f71163          	bne	a4,a5,7f6 <NVIC_Init+0x2a>
     7d8:	3114                	lbu	a3,1(a0)
     7da:	478d                	li	a5,3
     7dc:	00d7ed63          	bltu	a5,a3,7f6 <NVIC_Init+0x2a>
     7e0:	213c                	lbu	a5,2(a0)
     7e2:	069a                	slli	a3,a3,0x6
     7e4:	e000e737          	lui	a4,0xe000e
     7e8:	0796                	slli	a5,a5,0x5
     7ea:	8fd5                	or	a5,a5,a3
     7ec:	0ff7f793          	andi	a5,a5,255
     7f0:	9732                	add	a4,a4,a2
     7f2:	40f70023          	sb	a5,1024(a4) # e000e400 <_eusrstack+0xc0006400>
     7f6:	4154                	lw	a3,4(a0)
     7f8:	4705                	li	a4,1
     7fa:	00565793          	srli	a5,a2,0x5
     7fe:	00c71733          	sll	a4,a4,a2
     802:	ca89                	beqz	a3,814 <__stack_size+0x14>
     804:	04078793          	addi	a5,a5,64
     808:	078a                	slli	a5,a5,0x2
     80a:	e000e6b7          	lui	a3,0xe000e
     80e:	97b6                	add	a5,a5,a3
     810:	c398                	sw	a4,0(a5)
     812:	8082                	ret
     814:	06078793          	addi	a5,a5,96
     818:	bfc5                	j	808 <__stack_size+0x8>

0000081a <RCC_GetClocksFreq>:
     81a:	40021737          	lui	a4,0x40021
     81e:	435c                	lw	a5,4(a4)
     820:	4691                	li	a3,4
     822:	8bb1                	andi	a5,a5,12
     824:	00d78563          	beq	a5,a3,82e <RCC_GetClocksFreq+0x14>
     828:	46a1                	li	a3,8
     82a:	06d78263          	beq	a5,a3,88e <RCC_GetClocksFreq+0x74>
     82e:	007a17b7          	lui	a5,0x7a1
     832:	20078793          	addi	a5,a5,512 # 7a1200 <_data_lma+0x79dc2c>
     836:	c11c                	sw	a5,0(a0)
     838:	40021637          	lui	a2,0x40021
     83c:	425c                	lw	a5,4(a2)
     83e:	20000737          	lui	a4,0x20000
     842:	03470713          	addi	a4,a4,52 # 20000034 <APBAHBPrescTable>
     846:	8391                	srli	a5,a5,0x4
     848:	8bbd                	andi	a5,a5,15
     84a:	97ba                	add	a5,a5,a4
     84c:	2394                	lbu	a3,0(a5)
     84e:	411c                	lw	a5,0(a0)
     850:	00d7d7b3          	srl	a5,a5,a3
     854:	c15c                	sw	a5,4(a0)
     856:	4254                	lw	a3,4(a2)
     858:	82a1                	srli	a3,a3,0x8
     85a:	8a9d                	andi	a3,a3,7
     85c:	96ba                	add	a3,a3,a4
     85e:	2294                	lbu	a3,0(a3)
     860:	00d7d6b3          	srl	a3,a5,a3
     864:	c514                	sw	a3,8(a0)
     866:	4254                	lw	a3,4(a2)
     868:	82ad                	srli	a3,a3,0xb
     86a:	8a9d                	andi	a3,a3,7
     86c:	9736                	add	a4,a4,a3
     86e:	2318                	lbu	a4,0(a4)
     870:	00e7d7b3          	srl	a5,a5,a4
     874:	c55c                	sw	a5,12(a0)
     876:	4258                	lw	a4,4(a2)
     878:	8339                	srli	a4,a4,0xe
     87a:	00377693          	andi	a3,a4,3
     87e:	80418713          	addi	a4,gp,-2044 # 200000ac <ADCPrescTable>
     882:	9736                	add	a4,a4,a3
     884:	2318                	lbu	a4,0(a4)
     886:	02e7d7b3          	divu	a5,a5,a4
     88a:	c91c                	sw	a5,16(a0)
     88c:	8082                	ret
     88e:	435c                	lw	a5,4(a4)
     890:	4358                	lw	a4,4(a4)
     892:	66c1                	lui	a3,0x10
     894:	83c9                	srli	a5,a5,0x12
     896:	8f75                	and	a4,a4,a3
     898:	1ffff6b7          	lui	a3,0x1ffff
     89c:	70c6a683          	lw	a3,1804(a3) # 1ffff70c <_data_lma+0x1fffc138>
     8a0:	8bbd                	andi	a5,a5,15
     8a2:	0789                	addi	a5,a5,2
     8a4:	01169613          	slli	a2,a3,0x11
     8a8:	00064863          	bltz	a2,8b8 <RCC_GetClocksFreq+0x9e>
     8ac:	46c5                	li	a3,17
     8ae:	4601                	li	a2,0
     8b0:	02d79263          	bne	a5,a3,8d4 <RCC_GetClocksFreq+0xba>
     8b4:	47c9                	li	a5,18
     8b6:	a839                	j	8d4 <RCC_GetClocksFreq+0xba>
     8b8:	4689                	li	a3,2
     8ba:	02d78f63          	beq	a5,a3,8f8 <RCC_GetClocksFreq+0xde>
     8be:	46bd                	li	a3,15
     8c0:	02d78e63          	beq	a5,a3,8fc <RCC_GetClocksFreq+0xe2>
     8c4:	46c1                	li	a3,16
     8c6:	02d78e63          	beq	a5,a3,902 <RCC_GetClocksFreq+0xe8>
     8ca:	46c5                	li	a3,17
     8cc:	4601                	li	a2,0
     8ce:	00d79363          	bne	a5,a3,8d4 <RCC_GetClocksFreq+0xba>
     8d2:	47c1                	li	a5,16
     8d4:	ef1d                	bnez	a4,912 <RCC_GetClocksFreq+0xf8>
     8d6:	40024737          	lui	a4,0x40024
     8da:	80072703          	lw	a4,-2048(a4) # 40023800 <_eusrstack+0x2001b800>
     8de:	8b41                	andi	a4,a4,16
     8e0:	c705                	beqz	a4,908 <RCC_GetClocksFreq+0xee>
     8e2:	007a1737          	lui	a4,0x7a1
     8e6:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
     8ea:	02f707b3          	mul	a5,a4,a5
     8ee:	c11c                	sw	a5,0(a0)
     8f0:	d621                	beqz	a2,838 <RCC_GetClocksFreq+0x1e>
     8f2:	411c                	lw	a5,0(a0)
     8f4:	8385                	srli	a5,a5,0x1
     8f6:	b781                	j	836 <RCC_GetClocksFreq+0x1c>
     8f8:	4601                	li	a2,0
     8fa:	bf6d                	j	8b4 <RCC_GetClocksFreq+0x9a>
     8fc:	4605                	li	a2,1
     8fe:	47b5                	li	a5,13
     900:	bfd1                	j	8d4 <RCC_GetClocksFreq+0xba>
     902:	4601                	li	a2,0
     904:	47bd                	li	a5,15
     906:	b7f9                	j	8d4 <RCC_GetClocksFreq+0xba>
     908:	003d1737          	lui	a4,0x3d1
     90c:	90070713          	addi	a4,a4,-1792 # 3d0900 <_data_lma+0x3cd32c>
     910:	bfe9                	j	8ea <RCC_GetClocksFreq+0xd0>
     912:	400215b7          	lui	a1,0x40021
     916:	55d8                	lw	a4,44(a1)
     918:	00f71693          	slli	a3,a4,0xf
     91c:	55d8                	lw	a4,44(a1)
     91e:	0406df63          	bgez	a3,97c <RCC_GetClocksFreq+0x162>
     922:	8311                	srli	a4,a4,0x4
     924:	8b3d                	andi	a4,a4,15
     926:	00170693          	addi	a3,a4,1
     92a:	007a1737          	lui	a4,0x7a1
     92e:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
     932:	02d75733          	divu	a4,a4,a3
     936:	c118                	sw	a4,0(a0)
     938:	55d4                	lw	a3,44(a1)
     93a:	82a1                	srli	a3,a3,0x8
     93c:	8abd                	andi	a3,a3,15
     93e:	e28d                	bnez	a3,960 <RCC_GetClocksFreq+0x146>
     940:	4695                	li	a3,5
     942:	02d70733          	mul	a4,a4,a3
     946:	8305                	srli	a4,a4,0x1
     948:	c118                	sw	a4,0(a0)
     94a:	40021737          	lui	a4,0x40021
     94e:	5758                	lw	a4,44(a4)
     950:	4114                	lw	a3,0(a0)
     952:	8b3d                	andi	a4,a4,15
     954:	0705                	addi	a4,a4,1
     956:	02e6d733          	divu	a4,a3,a4
     95a:	c118                	sw	a4,0(a0)
     95c:	4118                	lw	a4,0(a0)
     95e:	b771                	j	8ea <RCC_GetClocksFreq+0xd0>
     960:	4585                	li	a1,1
     962:	00b69463          	bne	a3,a1,96a <RCC_GetClocksFreq+0x150>
     966:	46e5                	li	a3,25
     968:	bfe9                	j	942 <RCC_GetClocksFreq+0x128>
     96a:	45bd                	li	a1,15
     96c:	00b69663          	bne	a3,a1,978 <RCC_GetClocksFreq+0x15e>
     970:	46d1                	li	a3,20
     972:	02e68733          	mul	a4,a3,a4
     976:	bfc9                	j	948 <RCC_GetClocksFreq+0x12e>
     978:	0689                	addi	a3,a3,2
     97a:	bfe5                	j	972 <RCC_GetClocksFreq+0x158>
     97c:	8b3d                	andi	a4,a4,15
     97e:	00170693          	addi	a3,a4,1 # 40021001 <_eusrstack+0x20019001>
     982:	007a1737          	lui	a4,0x7a1
     986:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
     98a:	02d75733          	divu	a4,a4,a3
     98e:	b7f1                	j	95a <RCC_GetClocksFreq+0x140>

00000990 <RCC_APB2PeriphClockCmd>:
     990:	c599                	beqz	a1,99e <RCC_APB2PeriphClockCmd+0xe>
     992:	40021737          	lui	a4,0x40021
     996:	4f1c                	lw	a5,24(a4)
     998:	8d5d                	or	a0,a0,a5
     99a:	cf08                	sw	a0,24(a4)
     99c:	8082                	ret
     99e:	400217b7          	lui	a5,0x40021
     9a2:	4f98                	lw	a4,24(a5)
     9a4:	fff54513          	not	a0,a0
     9a8:	8d79                	and	a0,a0,a4
     9aa:	cf88                	sw	a0,24(a5)
     9ac:	8082                	ret

000009ae <RCC_APB1PeriphClockCmd>:
     9ae:	c599                	beqz	a1,9bc <RCC_APB1PeriphClockCmd+0xe>
     9b0:	40021737          	lui	a4,0x40021
     9b4:	4f5c                	lw	a5,28(a4)
     9b6:	8d5d                	or	a0,a0,a5
     9b8:	cf48                	sw	a0,28(a4)
     9ba:	8082                	ret
     9bc:	400217b7          	lui	a5,0x40021
     9c0:	4fd8                	lw	a4,28(a5)
     9c2:	fff54513          	not	a0,a0
     9c6:	8d79                	and	a0,a0,a4
     9c8:	cfc8                	sw	a0,28(a5)
     9ca:	8082                	ret

000009cc <SPI_Init>:
     9cc:	211a                	lhu	a4,0(a0)
     9ce:	678d                	lui	a5,0x3
     9d0:	04078793          	addi	a5,a5,64 # 3040 <_printf_i+0x274>
     9d4:	21b6                	lhu	a3,2(a1)
     9d6:	8f7d                	and	a4,a4,a5
     9d8:	219e                	lhu	a5,0(a1)
     9da:	8fd5                	or	a5,a5,a3
     9dc:	21d6                	lhu	a3,4(a1)
     9de:	8fd5                	or	a5,a5,a3
     9e0:	21f6                	lhu	a3,6(a1)
     9e2:	8fd5                	or	a5,a5,a3
     9e4:	2596                	lhu	a3,8(a1)
     9e6:	8fd5                	or	a5,a5,a3
     9e8:	25b6                	lhu	a3,10(a1)
     9ea:	8fd5                	or	a5,a5,a3
     9ec:	25d6                	lhu	a3,12(a1)
     9ee:	8fd5                	or	a5,a5,a3
     9f0:	25f6                	lhu	a3,14(a1)
     9f2:	8fd5                	or	a5,a5,a3
     9f4:	8fd9                	or	a5,a5,a4
     9f6:	a11e                	sh	a5,0(a0)
     9f8:	2d5a                	lhu	a4,28(a0)
     9fa:	77fd                	lui	a5,0xfffff
     9fc:	7ff78793          	addi	a5,a5,2047 # fffff7ff <_eusrstack+0xdfff77ff>
     a00:	8ff9                	and	a5,a5,a4
     a02:	ad5e                	sh	a5,28(a0)
     a04:	299e                	lhu	a5,16(a1)
     a06:	a91e                	sh	a5,16(a0)
     a08:	8082                	ret

00000a0a <SPI_Cmd>:
     a0a:	211e                	lhu	a5,0(a0)
     a0c:	c589                	beqz	a1,a16 <SPI_Cmd+0xc>
     a0e:	0407e793          	ori	a5,a5,64
     a12:	a11e                	sh	a5,0(a0)
     a14:	8082                	ret
     a16:	07c2                	slli	a5,a5,0x10
     a18:	83c1                	srli	a5,a5,0x10
     a1a:	fbf7f793          	andi	a5,a5,-65
     a1e:	07c2                	slli	a5,a5,0x10
     a20:	83c1                	srli	a5,a5,0x10
     a22:	bfc5                	j	a12 <SPI_Cmd+0x8>

00000a24 <SPI_I2S_SendData>:
     a24:	a54e                	sh	a1,12(a0)
     a26:	8082                	ret

00000a28 <SPI_I2S_ReceiveData>:
     a28:	254a                	lhu	a0,12(a0)
     a2a:	8082                	ret

00000a2c <SPI_I2S_GetFlagStatus>:
     a2c:	250a                	lhu	a0,8(a0)
     a2e:	8d6d                	and	a0,a0,a1
     a30:	00a03533          	snez	a0,a0
     a34:	8082                	ret

00000a36 <TIM_TimeBaseInit>:
     a36:	211e                	lhu	a5,0(a0)
     a38:	40013737          	lui	a4,0x40013
     a3c:	c0070693          	addi	a3,a4,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     a40:	07c2                	slli	a5,a5,0x10
     a42:	83c1                	srli	a5,a5,0x10
     a44:	04d50063          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a48:	400006b7          	lui	a3,0x40000
     a4c:	02d50c63          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a50:	40068693          	addi	a3,a3,1024 # 40000400 <_eusrstack+0x1fff8400>
     a54:	02d50863          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a58:	400016b7          	lui	a3,0x40001
     a5c:	80068613          	addi	a2,a3,-2048 # 40000800 <_eusrstack+0x1fff8800>
     a60:	02c50263          	beq	a0,a2,a84 <TIM_TimeBaseInit+0x4e>
     a64:	c0068693          	addi	a3,a3,-1024
     a68:	00d50e63          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a6c:	40070713          	addi	a4,a4,1024
     a70:	00e50a63          	beq	a0,a4,a84 <TIM_TimeBaseInit+0x4e>
     a74:	40015737          	lui	a4,0x40015
     a78:	c0070693          	addi	a3,a4,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     a7c:	00d50463          	beq	a0,a3,a84 <TIM_TimeBaseInit+0x4e>
     a80:	00e51663          	bne	a0,a4,a8c <TIM_TimeBaseInit+0x56>
     a84:	21ba                	lhu	a4,2(a1)
     a86:	f8f7f793          	andi	a5,a5,-113
     a8a:	8fd9                	or	a5,a5,a4
     a8c:	40001737          	lui	a4,0x40001
     a90:	00e50c63          	beq	a0,a4,aa8 <TIM_TimeBaseInit+0x72>
     a94:	40070713          	addi	a4,a4,1024 # 40001400 <_eusrstack+0x1fff9400>
     a98:	00e50863          	beq	a0,a4,aa8 <TIM_TimeBaseInit+0x72>
     a9c:	cff7f793          	andi	a5,a5,-769
     aa0:	21fa                	lhu	a4,6(a1)
     aa2:	07c2                	slli	a5,a5,0x10
     aa4:	83c1                	srli	a5,a5,0x10
     aa6:	8fd9                	or	a5,a5,a4
     aa8:	a11e                	sh	a5,0(a0)
     aaa:	21de                	lhu	a5,4(a1)
     aac:	b55e                	sh	a5,44(a0)
     aae:	219e                	lhu	a5,0(a1)
     ab0:	b51e                	sh	a5,40(a0)
     ab2:	400137b7          	lui	a5,0x40013
     ab6:	c0078713          	addi	a4,a5,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     aba:	00e50e63          	beq	a0,a4,ad6 <TIM_TimeBaseInit+0xa0>
     abe:	40078793          	addi	a5,a5,1024
     ac2:	00f50a63          	beq	a0,a5,ad6 <TIM_TimeBaseInit+0xa0>
     ac6:	400157b7          	lui	a5,0x40015
     aca:	c0078713          	addi	a4,a5,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     ace:	00e50463          	beq	a0,a4,ad6 <TIM_TimeBaseInit+0xa0>
     ad2:	00f51463          	bne	a0,a5,ada <TIM_TimeBaseInit+0xa4>
     ad6:	259c                	lbu	a5,8(a1)
     ad8:	b91e                	sh	a5,48(a0)
     ada:	4785                	li	a5,1
     adc:	a95e                	sh	a5,20(a0)
     ade:	8082                	ret

00000ae0 <TIM_OC1Init>:
     ae0:	311e                	lhu	a5,32(a0)
     ae2:	2192                	lhu	a2,0(a1)
     ae4:	0025d803          	lhu	a6,2(a1) # 40021002 <_eusrstack+0x20019002>
     ae8:	07c2                	slli	a5,a5,0x10
     aea:	83c1                	srli	a5,a5,0x10
     aec:	9bf9                	andi	a5,a5,-2
     aee:	07c2                	slli	a5,a5,0x10
     af0:	83c1                	srli	a5,a5,0x10
     af2:	b11e                	sh	a5,32(a0)
     af4:	311e                	lhu	a5,32(a0)
     af6:	2156                	lhu	a3,4(a0)
     af8:	2d1a                	lhu	a4,24(a0)
     afa:	07c2                	slli	a5,a5,0x10
     afc:	83c1                	srli	a5,a5,0x10
     afe:	0742                	slli	a4,a4,0x10
     b00:	8341                	srli	a4,a4,0x10
     b02:	f8c77713          	andi	a4,a4,-116
     b06:	8f51                	or	a4,a4,a2
     b08:	2592                	lhu	a2,8(a1)
     b0a:	9bf5                	andi	a5,a5,-3
     b0c:	06c2                	slli	a3,a3,0x10
     b0e:	01066633          	or	a2,a2,a6
     b12:	8fd1                	or	a5,a5,a2
     b14:	40013637          	lui	a2,0x40013
     b18:	c0060813          	addi	a6,a2,-1024 # 40012c00 <_eusrstack+0x2000ac00>
     b1c:	82c1                	srli	a3,a3,0x10
     b1e:	01050e63          	beq	a0,a6,b3a <TIM_OC1Init+0x5a>
     b22:	40060613          	addi	a2,a2,1024
     b26:	00c50a63          	beq	a0,a2,b3a <TIM_OC1Init+0x5a>
     b2a:	40015637          	lui	a2,0x40015
     b2e:	c0060813          	addi	a6,a2,-1024 # 40014c00 <_eusrstack+0x2000cc00>
     b32:	01050463          	beq	a0,a6,b3a <TIM_OC1Init+0x5a>
     b36:	02c51063          	bne	a0,a2,b56 <TIM_OC1Init+0x76>
     b3a:	25b2                	lhu	a2,10(a1)
     b3c:	9bdd                	andi	a5,a5,-9
     b3e:	00e5d803          	lhu	a6,14(a1)
     b42:	8fd1                	or	a5,a5,a2
     b44:	21d2                	lhu	a2,4(a1)
     b46:	9bed                	andi	a5,a5,-5
     b48:	cff6f693          	andi	a3,a3,-769
     b4c:	8fd1                	or	a5,a5,a2
     b4e:	25d2                	lhu	a2,12(a1)
     b50:	01066633          	or	a2,a2,a6
     b54:	8ed1                	or	a3,a3,a2
     b56:	a156                	sh	a3,4(a0)
     b58:	ad1a                	sh	a4,24(a0)
     b5a:	21fa                	lhu	a4,6(a1)
     b5c:	b95a                	sh	a4,52(a0)
     b5e:	b11e                	sh	a5,32(a0)
     b60:	8082                	ret

00000b62 <TIM_Cmd>:
     b62:	211e                	lhu	a5,0(a0)
     b64:	c589                	beqz	a1,b6e <TIM_Cmd+0xc>
     b66:	0017e793          	ori	a5,a5,1
     b6a:	a11e                	sh	a5,0(a0)
     b6c:	8082                	ret
     b6e:	07c2                	slli	a5,a5,0x10
     b70:	83c1                	srli	a5,a5,0x10
     b72:	9bf9                	andi	a5,a5,-2
     b74:	07c2                	slli	a5,a5,0x10
     b76:	83c1                	srli	a5,a5,0x10
     b78:	bfcd                	j	b6a <TIM_Cmd+0x8>

00000b7a <TIM_ITConfig>:
     b7a:	255e                	lhu	a5,12(a0)
     b7c:	c601                	beqz	a2,b84 <TIM_ITConfig+0xa>
     b7e:	8ddd                	or	a1,a1,a5
     b80:	a54e                	sh	a1,12(a0)
     b82:	8082                	ret
     b84:	fff5c593          	not	a1,a1
     b88:	8dfd                	and	a1,a1,a5
     b8a:	bfdd                	j	b80 <TIM_ITConfig+0x6>

00000b8c <TIM_ARRPreloadConfig>:
     b8c:	211e                	lhu	a5,0(a0)
     b8e:	c589                	beqz	a1,b98 <TIM_ARRPreloadConfig+0xc>
     b90:	0807e793          	ori	a5,a5,128
     b94:	a11e                	sh	a5,0(a0)
     b96:	8082                	ret
     b98:	07c2                	slli	a5,a5,0x10
     b9a:	83c1                	srli	a5,a5,0x10
     b9c:	f7f7f793          	andi	a5,a5,-129
     ba0:	07c2                	slli	a5,a5,0x10
     ba2:	83c1                	srli	a5,a5,0x10
     ba4:	bfc5                	j	b94 <TIM_ARRPreloadConfig+0x8>

00000ba6 <TIM_OC1PreloadConfig>:
     ba6:	2d1e                	lhu	a5,24(a0)
     ba8:	07c2                	slli	a5,a5,0x10
     baa:	83c1                	srli	a5,a5,0x10
     bac:	9bdd                	andi	a5,a5,-9
     bae:	8ddd                	or	a1,a1,a5
     bb0:	ad0e                	sh	a1,24(a0)
     bb2:	8082                	ret

00000bb4 <TIM_SetCompare1>:
     bb4:	b94e                	sh	a1,52(a0)
     bb6:	8082                	ret

00000bb8 <TIM_GetITStatus>:
     bb8:	291e                	lhu	a5,16(a0)
     bba:	254a                	lhu	a0,12(a0)
     bbc:	8fed                	and	a5,a5,a1
     bbe:	0542                	slli	a0,a0,0x10
     bc0:	8141                	srli	a0,a0,0x10
     bc2:	c789                	beqz	a5,bcc <TIM_GetITStatus+0x14>
     bc4:	8d6d                	and	a0,a0,a1
     bc6:	00a03533          	snez	a0,a0
     bca:	8082                	ret
     bcc:	4501                	li	a0,0
     bce:	8082                	ret

00000bd0 <TIM_ClearITPendingBit>:
     bd0:	fff5c593          	not	a1,a1
     bd4:	05c2                	slli	a1,a1,0x10
     bd6:	81c1                	srli	a1,a1,0x10
     bd8:	a90e                	sh	a1,16(a0)
     bda:	8082                	ret

00000bdc <USART_Init>:
     bdc:	e14ff2ef          	jal	t0,1f0 <__riscv_save_0>
     be0:	2916                	lhu	a3,16(a0)
     be2:	77f5                	lui	a5,0xffffd
     be4:	17fd                	addi	a5,a5,-1
     be6:	8ff5                	and	a5,a5,a3
     be8:	21f6                	lhu	a3,6(a1)
     bea:	25da                	lhu	a4,12(a1)
     bec:	7179                	addi	sp,sp,-48
     bee:	8fd5                	or	a5,a5,a3
     bf0:	a91e                	sh	a5,16(a0)
     bf2:	2556                	lhu	a3,12(a0)
     bf4:	77fd                	lui	a5,0xfffff
     bf6:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <_eusrstack+0xdfff69f3>
     bfa:	8ff5                	and	a5,a5,a3
     bfc:	21d6                	lhu	a3,4(a1)
     bfe:	842a                	mv	s0,a0
     c00:	c62e                	sw	a1,12(sp)
     c02:	8fd5                	or	a5,a5,a3
     c04:	2596                	lhu	a3,8(a1)
     c06:	8fd5                	or	a5,a5,a3
     c08:	25b6                	lhu	a3,10(a1)
     c0a:	8fd5                	or	a5,a5,a3
     c0c:	a55e                	sh	a5,12(a0)
     c0e:	295e                	lhu	a5,20(a0)
     c10:	07c2                	slli	a5,a5,0x10
     c12:	83c1                	srli	a5,a5,0x10
     c14:	cff7f793          	andi	a5,a5,-769
     c18:	8fd9                	or	a5,a5,a4
     c1a:	a95e                	sh	a5,20(a0)
     c1c:	0868                	addi	a0,sp,28
     c1e:	3ef5                	jal	81a <RCC_GetClocksFreq>
     c20:	400147b7          	lui	a5,0x40014
     c24:	80078793          	addi	a5,a5,-2048 # 40013800 <_eusrstack+0x2000b800>
     c28:	45b2                	lw	a1,12(sp)
     c2a:	02f41e63          	bne	s0,a5,c66 <USART_Init+0x8a>
     c2e:	57a2                	lw	a5,40(sp)
     c30:	4765                	li	a4,25
     c32:	02e787b3          	mul	a5,a5,a4
     c36:	4198                	lw	a4,0(a1)
     c38:	06400693          	li	a3,100
     c3c:	070a                	slli	a4,a4,0x2
     c3e:	02e7d7b3          	divu	a5,a5,a4
     c42:	02d7d733          	divu	a4,a5,a3
     c46:	02d7f7b3          	remu	a5,a5,a3
     c4a:	0712                	slli	a4,a4,0x4
     c4c:	0792                	slli	a5,a5,0x4
     c4e:	03278793          	addi	a5,a5,50
     c52:	02d7d7b3          	divu	a5,a5,a3
     c56:	8bbd                	andi	a5,a5,15
     c58:	8fd9                	or	a5,a5,a4
     c5a:	07c2                	slli	a5,a5,0x10
     c5c:	83c1                	srli	a5,a5,0x10
     c5e:	a41e                	sh	a5,8(s0)
     c60:	6145                	addi	sp,sp,48
     c62:	db2ff06f          	j	214 <__riscv_restore_0>
     c66:	5792                	lw	a5,36(sp)
     c68:	b7e1                	j	c30 <USART_Init+0x54>

00000c6a <USART_Cmd>:
     c6a:	c591                	beqz	a1,c76 <USART_Cmd+0xc>
     c6c:	255e                	lhu	a5,12(a0)
     c6e:	6709                	lui	a4,0x2
     c70:	8fd9                	or	a5,a5,a4
     c72:	a55e                	sh	a5,12(a0)
     c74:	8082                	ret
     c76:	255a                	lhu	a4,12(a0)
     c78:	77f9                	lui	a5,0xffffe
     c7a:	17fd                	addi	a5,a5,-1
     c7c:	8ff9                	and	a5,a5,a4
     c7e:	bfd5                	j	c72 <USART_Cmd+0x8>

00000c80 <USART_SendData>:
     c80:	1ff5f593          	andi	a1,a1,511
     c84:	a14e                	sh	a1,4(a0)
     c86:	8082                	ret

00000c88 <USART_GetFlagStatus>:
     c88:	210a                	lhu	a0,0(a0)
     c8a:	8d6d                	and	a0,a0,a1
     c8c:	00a03533          	snez	a0,a0
     c90:	8082                	ret

00000c92 <Delay_us>:
     c92:	82a1c783          	lbu	a5,-2006(gp) # 200000d2 <fac_us>
     c96:	e000f637          	lui	a2,0xe000f
     c9a:	02a78533          	mul	a0,a5,a0
     c9e:	e000f7b7          	lui	a5,0xe000f
     ca2:	0087a803          	lw	a6,8(a5) # e000f008 <_eusrstack+0xc0007008>
     ca6:	00c7a883          	lw	a7,12(a5)
     caa:	00862303          	lw	t1,8(a2) # e000f008 <_eusrstack+0xc0007008>
     cae:	00c62383          	lw	t2,12(a2)
     cb2:	410306b3          	sub	a3,t1,a6
     cb6:	00d33733          	sltu	a4,t1,a3
     cba:	00771463          	bne	a4,t2,cc2 <Delay_us+0x30>
     cbe:	fea6e6e3          	bltu	a3,a0,caa <Delay_us+0x18>
     cc2:	8082                	ret

00000cc4 <Delay_ms>:
     cc4:	8281d783          	lhu	a5,-2008(gp) # 200000d0 <fac_ms>
     cc8:	e000f337          	lui	t1,0xe000f
     ccc:	02a78533          	mul	a0,a5,a0
     cd0:	e000f7b7          	lui	a5,0xe000f
     cd4:	0087a803          	lw	a6,8(a5) # e000f008 <_eusrstack+0xc0007008>
     cd8:	00c7a883          	lw	a7,12(a5)
     cdc:	41f55593          	srai	a1,a0,0x1f
     ce0:	00832603          	lw	a2,8(t1) # e000f008 <_eusrstack+0xc0007008>
     ce4:	00c32683          	lw	a3,12(t1)
     ce8:	41060733          	sub	a4,a2,a6
     cec:	00e637b3          	sltu	a5,a2,a4
     cf0:	40f687b3          	sub	a5,a3,a5
     cf4:	feb7e6e3          	bltu	a5,a1,ce0 <Delay_ms+0x1c>
     cf8:	00f59463          	bne	a1,a5,d00 <Delay_ms+0x3c>
     cfc:	fea762e3          	bltu	a4,a0,ce0 <Delay_ms+0x1c>
     d00:	8082                	ret

00000d02 <BrewingControl_Init>:
     d02:	84818793          	addi	a5,gp,-1976 # 200000f0 <g_brewing_ctrl>
     d06:	00000713          	li	a4,0
     d0a:	0007a023          	sw	zero,0(a5)
     d0e:	0007a223          	sw	zero,4(a5)
     d12:	0007a423          	sw	zero,8(a5)
     d16:	0007a623          	sw	zero,12(a5)
     d1a:	cb98                	sw	a4,16(a5)
     d1c:	00079a23          	sh	zero,20(a5)
     d20:	8082                	ret

00000d22 <BrewingControl_Start>:
     d22:	84818793          	addi	a5,gp,-1976 # 200000f0 <g_brewing_ctrl>
     d26:	4398                	lw	a4,0(a5)
     d28:	ef09                	bnez	a4,d42 <BrewingControl_Start+0x20>
     d2a:	cc6ff2ef          	jal	t0,1f0 <__riscv_save_0>
     d2e:	00003537          	lui	a0,0x3
     d32:	4705                	li	a4,1
     d34:	32c50513          	addi	a0,a0,812 # 332c <_read+0x9e>
     d38:	c398                	sw	a4,0(a5)
     d3a:	4a8010ef          	jal	ra,21e2 <puts>
     d3e:	cd6ff06f          	j	214 <__riscv_restore_0>
     d42:	8082                	ret

00000d44 <BrewingControl_KeyHandler>:
     d44:	cacff2ef          	jal	t0,1f0 <__riscv_save_0>
     d48:	84818413          	addi	s0,gp,-1976 # 200000f0 <g_brewing_ctrl>
     d4c:	401c                	lw	a5,0(s0)
     d4e:	470d                	li	a4,3
     d50:	04e78e63          	beq	a5,a4,dac <BrewingControl_KeyHandler+0x68>
     d54:	00f76763          	bltu	a4,a5,d62 <BrewingControl_KeyHandler+0x1e>
     d58:	4705                	li	a4,1
     d5a:	02e78763          	beq	a5,a4,d88 <BrewingControl_KeyHandler+0x44>
     d5e:	cb6ff06f          	j	214 <__riscv_restore_0>
     d62:	4695                	li	a3,5
     d64:	06d78c63          	beq	a5,a3,ddc <BrewingControl_KeyHandler+0x98>
     d68:	4719                	li	a4,6
     d6a:	fee79ae3          	bne	a5,a4,d5e <BrewingControl_KeyHandler+0x1a>
     d6e:	4791                	li	a5,4
     d70:	fef517e3          	bne	a0,a5,d5e <BrewingControl_KeyHandler+0x1a>
     d74:	479d                	li	a5,7
     d76:	4501                	li	a0,0
     d78:	c01c                	sw	a5,0(s0)
     d7a:	3e5000ef          	jal	ra,195e <stir>
     d7e:	00003537          	lui	a0,0x3
     d82:	32050513          	addi	a0,a0,800 # 3320 <_read+0x92>
     d86:	a005                	j	da6 <BrewingControl_KeyHandler+0x62>
     d88:	fcf51be3          	bne	a0,a5,d5e <BrewingControl_KeyHandler+0x1a>
     d8c:	4789                	li	a5,2
     d8e:	c01c                	sw	a5,0(s0)
     d90:	8201a783          	lw	a5,-2016(gp) # 200000c8 <uwtick>
     d94:	4501                	li	a0,0
     d96:	4585                	li	a1,1
     d98:	c05c                	sw	a5,4(s0)
     d9a:	4c3000ef          	jal	ra,1a5c <WaterPump_Control>
     d9e:	00003537          	lui	a0,0x3
     da2:	2e050513          	addi	a0,a0,736 # 32e0 <_read+0x52>
     da6:	43c010ef          	jal	ra,21e2 <puts>
     daa:	bf55                	j	d5e <BrewingControl_KeyHandler+0x1a>
     dac:	4789                	li	a5,2
     dae:	faf518e3          	bne	a0,a5,d5e <BrewingControl_KeyHandler+0x1a>
     db2:	4791                	li	a5,4
     db4:	83818513          	addi	a0,gp,-1992 # 200000e0 <ds18b20>
     db8:	c01c                	sw	a5,0(s0)
     dba:	2629                	jal	10c4 <DS18B20_ReadRealtimeTemp>
     dbc:	8381a783          	lw	a5,-1992(gp) # 200000e0 <ds18b20>
     dc0:	c81c                	sw	a5,16(s0)
     dc2:	26a9                	jal	110c <Heater_Start>
     dc4:	4808                	lw	a0,16(s0)
     dc6:	1ee010ef          	jal	ra,1fb4 <__extendsfdf2>
     dca:	862a                	mv	a2,a0
     dcc:	00003537          	lui	a0,0x3
     dd0:	86ae                	mv	a3,a1
     dd2:	2f450513          	addi	a0,a0,756 # 32f4 <_read+0x66>
     dd6:	2f8010ef          	jal	ra,20ce <iprintf>
     dda:	b751                	j	d5e <BrewingControl_KeyHandler+0x1a>
     ddc:	f8e511e3          	bne	a0,a4,d5e <BrewingControl_KeyHandler+0x1a>
     de0:	4799                	li	a5,6
     de2:	4505                	li	a0,1
     de4:	06400593          	li	a1,100
     de8:	c01c                	sw	a5,0(s0)
     dea:	317000ef          	jal	ra,1900 <stir_360>
     dee:	00003537          	lui	a0,0x3
     df2:	31450513          	addi	a0,a0,788 # 3314 <_read+0x86>
     df6:	bf45                	j	da6 <BrewingControl_KeyHandler+0x62>

00000df8 <BrewingControl_Task>:
     df8:	bf8ff2ef          	jal	t0,1f0 <__riscv_save_0>
     dfc:	8201a483          	lw	s1,-2016(gp) # 200000c8 <uwtick>
     e00:	8481a783          	lw	a5,-1976(gp) # 200000f0 <g_brewing_ctrl>
     e04:	471d                	li	a4,7
     e06:	17f9                	addi	a5,a5,-2
     e08:	06f76763          	bltu	a4,a5,e76 <BrewingControl_Task+0x7e>
     e0c:	670d                	lui	a4,0x3
     e0e:	078a                	slli	a5,a5,0x2
     e10:	2c070713          	addi	a4,a4,704 # 32c0 <_read+0x32>
     e14:	97ba                	add	a5,a5,a4
     e16:	439c                	lw	a5,0(a5)
     e18:	84818413          	addi	s0,gp,-1976 # 200000f0 <g_brewing_ctrl>
     e1c:	8782                	jr	a5
     e1e:	651d                	lui	a0,0x7
     e20:	53050513          	addi	a0,a0,1328 # 7530 <_data_lma+0x3f5c>
     e24:	39d000ef          	jal	ra,19c0 <delay_ms>
     e28:	405c                	lw	a5,4(s0)
     e2a:	4581                	li	a1,0
     e2c:	4501                	li	a0,0
     e2e:	40f487b3          	sub	a5,s1,a5
     e32:	c41c                	sw	a5,8(s0)
     e34:	429000ef          	jal	ra,1a5c <WaterPump_Control>
     e38:	440c                	lw	a1,8(s0)
     e3a:	00003537          	lui	a0,0x3
     e3e:	478d                	li	a5,3
     e40:	34c50513          	addi	a0,a0,844 # 334c <_read+0xbe>
     e44:	c01c                	sw	a5,0(s0)
     e46:	288010ef          	jal	ra,20ce <iprintf>
     e4a:	651d                	lui	a0,0x7
     e4c:	53050513          	addi	a0,a0,1328 # 7530 <_data_lma+0x3f5c>
     e50:	3d95                	jal	cc4 <Delay_ms>
     e52:	2cc1                	jal	1122 <Heater_Stop>
     e54:	00003537          	lui	a0,0x3
     e58:	4795                	li	a5,5
     e5a:	37850513          	addi	a0,a0,888 # 3378 <_read+0xea>
     e5e:	c01c                	sw	a5,0(s0)
     e60:	382010ef          	jal	ra,21e2 <puts>
     e64:	47a1                	li	a5,8
     e66:	00003537          	lui	a0,0x3
     e6a:	c01c                	sw	a5,0(s0)
     e6c:	c444                	sw	s1,12(s0)
     e6e:	3a450513          	addi	a0,a0,932 # 33a4 <_read+0x116>
     e72:	370010ef          	jal	ra,21e2 <puts>
     e76:	b9eff06f          	j	214 <__riscv_restore_0>
     e7a:	445c                	lw	a5,12(s0)
     e7c:	8c9d                	sub	s1,s1,a5
     e7e:	679d                	lui	a5,0x7
     e80:	52f78793          	addi	a5,a5,1327 # 752f <_data_lma+0x3f5b>
     e84:	fe97f9e3          	bgeu	a5,s1,e76 <BrewingControl_Task+0x7e>
     e88:	47a5                	li	a5,9
     e8a:	4505                	li	a0,1
     e8c:	4585                	li	a1,1
     e8e:	c01c                	sw	a5,0(s0)
     e90:	3cd000ef          	jal	ra,1a5c <WaterPump_Control>
     e94:	00003537          	lui	a0,0x3
     e98:	3b450513          	addi	a0,a0,948 # 33b4 <_read+0x126>
     e9c:	bfd9                	j	e72 <BrewingControl_Task+0x7a>
     e9e:	77e5                	lui	a5,0xffff9
     ea0:	ad078793          	addi	a5,a5,-1328 # ffff8ad0 <_eusrstack+0xdfff0ad0>
     ea4:	94be                	add	s1,s1,a5
     ea6:	445c                	lw	a5,12(s0)
     ea8:	8c9d                	sub	s1,s1,a5
     eaa:	441c                	lw	a5,8(s0)
     eac:	fcf4e5e3          	bltu	s1,a5,e76 <BrewingControl_Task+0x7e>
     eb0:	4505                	li	a0,1
     eb2:	4581                	li	a1,0
     eb4:	3a9000ef          	jal	ra,1a5c <WaterPump_Control>
     eb8:	47a9                	li	a5,10
     eba:	00003537          	lui	a0,0x3
     ebe:	c01c                	sw	a5,0(s0)
     ec0:	3d450513          	addi	a0,a0,980 # 33d4 <_read+0x146>
     ec4:	b77d                	j	e72 <BrewingControl_Task+0x7a>

00000ec6 <DS18B20_IO_OUT>:
     ec6:	b2aff2ef          	jal	t0,1f0 <__riscv_save_0>
     eca:	1141                	addi	sp,sp,-16
     ecc:	4789                	li	a5,2
     ece:	827c                	sh	a5,4(sp)
     ed0:	40011537          	lui	a0,0x40011
     ed4:	47d1                	li	a5,20
     ed6:	c63e                	sw	a5,12(sp)
     ed8:	004c                	addi	a1,sp,4
     eda:	478d                	li	a5,3
     edc:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
     ee0:	c43e                	sw	a5,8(sp)
     ee2:	813ff0ef          	jal	ra,6f4 <GPIO_Init>
     ee6:	0141                	addi	sp,sp,16
     ee8:	b2cff06f          	j	214 <__riscv_restore_0>

00000eec <DS18B20_IO_IN>:
     eec:	b04ff2ef          	jal	t0,1f0 <__riscv_save_0>
     ef0:	1141                	addi	sp,sp,-16
     ef2:	4789                	li	a5,2
     ef4:	40011537          	lui	a0,0x40011
     ef8:	827c                	sh	a5,4(sp)
     efa:	004c                	addi	a1,sp,4
     efc:	04800793          	li	a5,72
     f00:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
     f04:	c63e                	sw	a5,12(sp)
     f06:	feeff0ef          	jal	ra,6f4 <GPIO_Init>
     f0a:	0141                	addi	sp,sp,16
     f0c:	b08ff06f          	j	214 <__riscv_restore_0>

00000f10 <DS18B20_Reset>:
     f10:	ae0ff2ef          	jal	t0,1f0 <__riscv_save_0>
     f14:	3f4d                	jal	ec6 <DS18B20_IO_OUT>
     f16:	40011437          	lui	s0,0x40011
     f1a:	4589                	li	a1,2
     f1c:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
     f20:	8a3ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
     f24:	1e000513          	li	a0,480
     f28:	33ad                	jal	c92 <Delay_us>
     f2a:	4589                	li	a1,2
     f2c:	80040513          	addi	a0,s0,-2048
     f30:	88fff0ef          	jal	ra,7be <GPIO_SetBits>
     f34:	04600513          	li	a0,70
     f38:	3ba9                	jal	c92 <Delay_us>
     f3a:	3f4d                	jal	eec <DS18B20_IO_IN>
     f3c:	ad8ff06f          	j	214 <__riscv_restore_0>

00000f40 <DS18B20_Check>:
     f40:	ab0ff2ef          	jal	t0,1f0 <__riscv_save_0>
     f44:	400114b7          	lui	s1,0x40011
     f48:	06500413          	li	s0,101
     f4c:	80048493          	addi	s1,s1,-2048 # 40010800 <_eusrstack+0x20008800>
     f50:	4589                	li	a1,2
     f52:	8526                	mv	a0,s1
     f54:	861ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
     f58:	ed11                	bnez	a0,f74 <DS18B20_Check+0x34>
     f5a:	400114b7          	lui	s1,0x40011
     f5e:	0f100413          	li	s0,241
     f62:	80048493          	addi	s1,s1,-2048 # 40010800 <_eusrstack+0x20008800>
     f66:	4589                	li	a1,2
     f68:	8526                	mv	a0,s1
     f6a:	84bff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
     f6e:	cd01                	beqz	a0,f86 <DS18B20_Check+0x46>
     f70:	4505                	li	a0,1
     f72:	a801                	j	f82 <DS18B20_Check+0x42>
     f74:	147d                	addi	s0,s0,-1
     f76:	4505                	li	a0,1
     f78:	0ff47413          	andi	s0,s0,255
     f7c:	3b19                	jal	c92 <Delay_us>
     f7e:	f869                	bnez	s0,f50 <DS18B20_Check+0x10>
     f80:	4501                	li	a0,0
     f82:	a92ff06f          	j	214 <__riscv_restore_0>
     f86:	147d                	addi	s0,s0,-1
     f88:	4505                	li	a0,1
     f8a:	0ff47413          	andi	s0,s0,255
     f8e:	3311                	jal	c92 <Delay_us>
     f90:	f879                	bnez	s0,f66 <DS18B20_Check+0x26>
     f92:	b7fd                	j	f80 <DS18B20_Check+0x40>

00000f94 <DS18B20_WriteBit>:
     f94:	a5cff2ef          	jal	t0,1f0 <__riscv_save_0>
     f98:	84aa                	mv	s1,a0
     f9a:	40011437          	lui	s0,0x40011
     f9e:	3725                	jal	ec6 <DS18B20_IO_OUT>
     fa0:	4589                	li	a1,2
     fa2:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
     fa6:	81dff0ef          	jal	ra,7c2 <GPIO_ResetBits>
     faa:	cc89                	beqz	s1,fc4 <DS18B20_WriteBit+0x30>
     fac:	4529                	li	a0,10
     fae:	31d5                	jal	c92 <Delay_us>
     fb0:	80040513          	addi	a0,s0,-2048
     fb4:	4589                	li	a1,2
     fb6:	809ff0ef          	jal	ra,7be <GPIO_SetBits>
     fba:	03700513          	li	a0,55
     fbe:	39d1                	jal	c92 <Delay_us>
     fc0:	a54ff06f          	j	214 <__riscv_restore_0>
     fc4:	04100513          	li	a0,65
     fc8:	31e9                	jal	c92 <Delay_us>
     fca:	80040513          	addi	a0,s0,-2048
     fce:	4589                	li	a1,2
     fd0:	feeff0ef          	jal	ra,7be <GPIO_SetBits>
     fd4:	4515                	li	a0,5
     fd6:	b7e5                	j	fbe <DS18B20_WriteBit+0x2a>

00000fd8 <DS18B20_ReadBit>:
     fd8:	a18ff2ef          	jal	t0,1f0 <__riscv_save_0>
     fdc:	35ed                	jal	ec6 <DS18B20_IO_OUT>
     fde:	40011437          	lui	s0,0x40011
     fe2:	4589                	li	a1,2
     fe4:	80040513          	addi	a0,s0,-2048 # 40010800 <_eusrstack+0x20008800>
     fe8:	fdaff0ef          	jal	ra,7c2 <GPIO_ResetBits>
     fec:	450d                	li	a0,3
     fee:	3155                	jal	c92 <Delay_us>
     ff0:	4589                	li	a1,2
     ff2:	80040513          	addi	a0,s0,-2048
     ff6:	fc8ff0ef          	jal	ra,7be <GPIO_SetBits>
     ffa:	3dcd                	jal	eec <DS18B20_IO_IN>
     ffc:	4529                	li	a0,10
     ffe:	3951                	jal	c92 <Delay_us>
    1000:	4589                	li	a1,2
    1002:	80040513          	addi	a0,s0,-2048
    1006:	faeff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    100a:	842a                	mv	s0,a0
    100c:	03500513          	li	a0,53
    1010:	3149                	jal	c92 <Delay_us>
    1012:	8522                	mv	a0,s0
    1014:	a00ff06f          	j	214 <__riscv_restore_0>

00001018 <DS18B20_WriteByte>:
    1018:	9d8ff2ef          	jal	t0,1f0 <__riscv_save_0>
    101c:	84aa                	mv	s1,a0
    101e:	4421                	li	s0,8
    1020:	147d                	addi	s0,s0,-1
    1022:	0014f513          	andi	a0,s1,1
    1026:	0ff47413          	andi	s0,s0,255
    102a:	37ad                	jal	f94 <DS18B20_WriteBit>
    102c:	8085                	srli	s1,s1,0x1
    102e:	f86d                	bnez	s0,1020 <DS18B20_WriteByte+0x8>
    1030:	9e4ff06f          	j	214 <__riscv_restore_0>

00001034 <DS18B20_ReadByte>:
    1034:	9bcff2ef          	jal	t0,1f0 <__riscv_save_0>
    1038:	44a1                	li	s1,8
    103a:	4401                	li	s0,0
    103c:	8005                	srli	s0,s0,0x1
    103e:	3f69                	jal	fd8 <DS18B20_ReadBit>
    1040:	c509                	beqz	a0,104a <DS18B20_ReadByte+0x16>
    1042:	f8046413          	ori	s0,s0,-128
    1046:	0ff47413          	andi	s0,s0,255
    104a:	14fd                	addi	s1,s1,-1
    104c:	0ff4f493          	andi	s1,s1,255
    1050:	f4f5                	bnez	s1,103c <DS18B20_ReadByte+0x8>
    1052:	8522                	mv	a0,s0
    1054:	9c0ff06f          	j	214 <__riscv_restore_0>

00001058 <DS18B20_StartConvert>:
    1058:	998ff2ef          	jal	t0,1f0 <__riscv_save_0>
    105c:	3d55                	jal	f10 <DS18B20_Reset>
    105e:	35cd                	jal	f40 <DS18B20_Check>
    1060:	4785                	li	a5,1
    1062:	c901                	beqz	a0,1072 <DS18B20_StartConvert+0x1a>
    1064:	0cc00513          	li	a0,204
    1068:	3f45                	jal	1018 <DS18B20_WriteByte>
    106a:	04400513          	li	a0,68
    106e:	376d                	jal	1018 <DS18B20_WriteByte>
    1070:	4781                	li	a5,0
    1072:	853e                	mv	a0,a5
    1074:	9a0ff06f          	j	214 <__riscv_restore_0>

00001078 <DS18B20_ReadTempRaw>:
    1078:	978ff2ef          	jal	t0,1f0 <__riscv_save_0>
    107c:	3ff1                	jal	1058 <DS18B20_StartConvert>
    107e:	c519                	beqz	a0,108c <DS18B20_ReadTempRaw+0x14>
    1080:	000037b7          	lui	a5,0x3
    1084:	3f07a503          	lw	a0,1008(a5) # 33f0 <_read+0x162>
    1088:	98cff06f          	j	214 <__riscv_restore_0>
    108c:	2ee00513          	li	a0,750
    1090:	3915                	jal	cc4 <Delay_ms>
    1092:	3dbd                	jal	f10 <DS18B20_Reset>
    1094:	3575                	jal	f40 <DS18B20_Check>
    1096:	d56d                	beqz	a0,1080 <DS18B20_ReadTempRaw+0x8>
    1098:	0cc00513          	li	a0,204
    109c:	3fb5                	jal	1018 <DS18B20_WriteByte>
    109e:	0be00513          	li	a0,190
    10a2:	3f9d                	jal	1018 <DS18B20_WriteByte>
    10a4:	3f41                	jal	1034 <DS18B20_ReadByte>
    10a6:	842a                	mv	s0,a0
    10a8:	3771                	jal	1034 <DS18B20_ReadByte>
    10aa:	0522                	slli	a0,a0,0x8
    10ac:	8d41                	or	a0,a0,s0
    10ae:	0542                	slli	a0,a0,0x10
    10b0:	8541                	srai	a0,a0,0x10
    10b2:	63d000ef          	jal	ra,1eee <__floatsisf>
    10b6:	000037b7          	lui	a5,0x3
    10ba:	3f47a583          	lw	a1,1012(a5) # 33f4 <_read+0x166>
    10be:	3a1000ef          	jal	ra,1c5e <__mulsf3>
    10c2:	b7d9                	j	1088 <DS18B20_ReadTempRaw+0x10>

000010c4 <DS18B20_ReadRealtimeTemp>:
    10c4:	92cff2ef          	jal	t0,1f0 <__riscv_save_0>
    10c8:	842a                	mv	s0,a0
    10ca:	377d                	jal	1078 <DS18B20_ReadTempRaw>
    10cc:	000037b7          	lui	a5,0x3
    10d0:	3ec7a583          	lw	a1,1004(a5) # 33ec <_read+0x15e>
    10d4:	84aa                	mv	s1,a0
    10d6:	2fd000ef          	jal	ra,1bd2 <__lesf2>
    10da:	02a05763          	blez	a0,1108 <DS18B20_ReadRealtimeTemp+0x44>
    10de:	4048                	lw	a0,4(s0)
    10e0:	c004                	sw	s1,0(s0)
    10e2:	85a6                	mv	a1,s1
    10e4:	2ef000ef          	jal	ra,1bd2 <__lesf2>
    10e8:	00a04d63          	bgtz	a0,1102 <DS18B20_ReadRealtimeTemp+0x3e>
    10ec:	241c                	lbu	a5,8(s0)
    10ee:	e789                	bnez	a5,10f8 <DS18B20_ReadRealtimeTemp+0x34>
    10f0:	445c                	lw	a5,12(s0)
    10f2:	c399                	beqz	a5,10f8 <DS18B20_ReadRealtimeTemp+0x34>
    10f4:	8526                	mv	a0,s1
    10f6:	9782                	jalr	a5
    10f8:	4785                	li	a5,1
    10fa:	a41c                	sb	a5,8(s0)
    10fc:	4501                	li	a0,0
    10fe:	916ff06f          	j	214 <__riscv_restore_0>
    1102:	00040423          	sb	zero,8(s0)
    1106:	bfdd                	j	10fc <DS18B20_ReadRealtimeTemp+0x38>
    1108:	4509                	li	a0,2
    110a:	bfd5                	j	10fe <DS18B20_ReadRealtimeTemp+0x3a>

0000110c <Heater_Start>:
    110c:	8e4ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1110:	40011537          	lui	a0,0x40011
    1114:	45c1                	li	a1,16
    1116:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    111a:	ea8ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    111e:	8f6ff06f          	j	214 <__riscv_restore_0>

00001122 <Heater_Stop>:
    1122:	8ceff2ef          	jal	t0,1f0 <__riscv_save_0>
    1126:	40011537          	lui	a0,0x40011
    112a:	45c1                	li	a1,16
    112c:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1130:	e8eff0ef          	jal	ra,7be <GPIO_SetBits>
    1134:	8e0ff06f          	j	214 <__riscv_restore_0>

00001138 <Heater_Init>:
    1138:	8b8ff2ef          	jal	t0,1f0 <__riscv_save_0>
    113c:	1141                	addi	sp,sp,-16
    113e:	4585                	li	a1,1
    1140:	4511                	li	a0,4
    1142:	c202                	sw	zero,4(sp)
    1144:	c402                	sw	zero,8(sp)
    1146:	c602                	sw	zero,12(sp)
    1148:	849ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    114c:	47c1                	li	a5,16
    114e:	827c                	sh	a5,4(sp)
    1150:	40011537          	lui	a0,0x40011
    1154:	47c1                	li	a5,16
    1156:	004c                	addi	a1,sp,4
    1158:	c63e                	sw	a5,12(sp)
    115a:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    115e:	478d                	li	a5,3
    1160:	c43e                	sw	a5,8(sp)
    1162:	d92ff0ef          	jal	ra,6f4 <GPIO_Init>
    1166:	3f75                	jal	1122 <Heater_Stop>
    1168:	0141                	addi	sp,sp,16
    116a:	8aaff06f          	j	214 <__riscv_restore_0>

0000116e <key_init>:
    116e:	882ff2ef          	jal	t0,1f0 <__riscv_save_0>
    1172:	1141                	addi	sp,sp,-16
    1174:	4585                	li	a1,1
    1176:	02000513          	li	a0,32
    117a:	817ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    117e:	6485                	lui	s1,0x1
    1180:	40011537          	lui	a0,0x40011
    1184:	440d                	li	s0,3
    1186:	a0048793          	addi	a5,s1,-1536 # a00 <SPI_Init+0x34>
    118a:	4941                	li	s2,16
    118c:	004c                	addi	a1,sp,4
    118e:	40050513          	addi	a0,a0,1024 # 40011400 <_eusrstack+0x20009400>
    1192:	827c                	sh	a5,4(sp)
    1194:	c64a                	sw	s2,12(sp)
    1196:	c422                	sw	s0,8(sp)
    1198:	d5cff0ef          	jal	ra,6f4 <GPIO_Init>
    119c:	4585                	li	a1,1
    119e:	04000513          	li	a0,64
    11a2:	feeff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    11a6:	c64a                	sw	s2,12(sp)
    11a8:	40012937          	lui	s2,0x40012
    11ac:	77e9                	lui	a5,0xffffa
    11ae:	004c                	addi	a1,sp,4
    11b0:	80090513          	addi	a0,s2,-2048 # 40011800 <_eusrstack+0x20009800>
    11b4:	827c                	sh	a5,4(sp)
    11b6:	c422                	sw	s0,8(sp)
    11b8:	d3cff0ef          	jal	ra,6f4 <GPIO_Init>
    11bc:	4585                	li	a1,1
    11be:	04000513          	li	a0,64
    11c2:	fceff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    11c6:	a8048493          	addi	s1,s1,-1408
    11ca:	8264                	sh	s1,4(sp)
    11cc:	004c                	addi	a1,sp,4
    11ce:	04800493          	li	s1,72
    11d2:	80090513          	addi	a0,s2,-2048
    11d6:	c626                	sw	s1,12(sp)
    11d8:	c422                	sw	s0,8(sp)
    11da:	d1aff0ef          	jal	ra,6f4 <GPIO_Init>
    11de:	4585                	li	a1,1
    11e0:	4541                	li	a0,16
    11e2:	faeff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    11e6:	02000793          	li	a5,32
    11ea:	004c                	addi	a1,sp,4
    11ec:	40011537          	lui	a0,0x40011
    11f0:	827c                	sh	a5,4(sp)
    11f2:	c626                	sw	s1,12(sp)
    11f4:	c422                	sw	s0,8(sp)
    11f6:	cfeff0ef          	jal	ra,6f4 <GPIO_Init>
    11fa:	0141                	addi	sp,sp,16
    11fc:	818ff06f          	j	214 <__riscv_restore_0>

00001200 <key_read>:
    1200:	ff1fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1204:	6405                	lui	s0,0x1
    1206:	400114b7          	lui	s1,0x40011
    120a:	80040593          	addi	a1,s0,-2048 # 800 <__stack_size>
    120e:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1212:	db0ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1216:	40048513          	addi	a0,s1,1024
    121a:	20000593          	li	a1,512
    121e:	400124b7          	lui	s1,0x40012
    1222:	d9cff0ef          	jal	ra,7be <GPIO_SetBits>
    1226:	65a1                	lui	a1,0x8
    1228:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    122c:	d92ff0ef          	jal	ra,7be <GPIO_SetBits>
    1230:	6589                	lui	a1,0x2
    1232:	80048513          	addi	a0,s1,-2048
    1236:	d88ff0ef          	jal	ra,7be <GPIO_SetBits>
    123a:	80040593          	addi	a1,s0,-2048
    123e:	80048513          	addi	a0,s1,-2048
    1242:	d72ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1246:	00153413          	seqz	s0,a0
    124a:	20000593          	li	a1,512
    124e:	80048513          	addi	a0,s1,-2048
    1252:	d62ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1256:	040a                	slli	s0,s0,0x2
    1258:	e111                	bnez	a0,125c <key_read+0x5c>
    125a:	440d                	li	s0,3
    125c:	40012537          	lui	a0,0x40012
    1260:	08000593          	li	a1,128
    1264:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    1268:	d4cff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    126c:	e111                	bnez	a0,1270 <key_read+0x70>
    126e:	4409                	li	s0,2
    1270:	02000593          	li	a1,32
    1274:	40011537          	lui	a0,0x40011
    1278:	d3cff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    127c:	e111                	bnez	a0,1280 <key_read+0x80>
    127e:	4405                	li	s0,1
    1280:	400114b7          	lui	s1,0x40011
    1284:	6905                	lui	s2,0x1
    1286:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    128a:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    128e:	d30ff0ef          	jal	ra,7be <GPIO_SetBits>
    1292:	40048513          	addi	a0,s1,1024
    1296:	20000593          	li	a1,512
    129a:	d28ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    129e:	400124b7          	lui	s1,0x40012
    12a2:	65a1                	lui	a1,0x8
    12a4:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    12a8:	d16ff0ef          	jal	ra,7be <GPIO_SetBits>
    12ac:	6589                	lui	a1,0x2
    12ae:	80048513          	addi	a0,s1,-2048
    12b2:	d0cff0ef          	jal	ra,7be <GPIO_SetBits>
    12b6:	80090593          	addi	a1,s2,-2048
    12ba:	80048513          	addi	a0,s1,-2048
    12be:	cf6ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    12c2:	e111                	bnez	a0,12c6 <key_read+0xc6>
    12c4:	4421                	li	s0,8
    12c6:	40012537          	lui	a0,0x40012
    12ca:	20000593          	li	a1,512
    12ce:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    12d2:	ce2ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    12d6:	e111                	bnez	a0,12da <key_read+0xda>
    12d8:	441d                	li	s0,7
    12da:	40012537          	lui	a0,0x40012
    12de:	08000593          	li	a1,128
    12e2:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    12e6:	cceff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    12ea:	e111                	bnez	a0,12ee <key_read+0xee>
    12ec:	4419                	li	s0,6
    12ee:	02000593          	li	a1,32
    12f2:	40011537          	lui	a0,0x40011
    12f6:	cbeff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    12fa:	e111                	bnez	a0,12fe <key_read+0xfe>
    12fc:	4415                	li	s0,5
    12fe:	400114b7          	lui	s1,0x40011
    1302:	6905                	lui	s2,0x1
    1304:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1308:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    130c:	cb2ff0ef          	jal	ra,7be <GPIO_SetBits>
    1310:	40048513          	addi	a0,s1,1024
    1314:	20000593          	li	a1,512
    1318:	ca6ff0ef          	jal	ra,7be <GPIO_SetBits>
    131c:	400124b7          	lui	s1,0x40012
    1320:	65a1                	lui	a1,0x8
    1322:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    1326:	c9cff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    132a:	6589                	lui	a1,0x2
    132c:	80048513          	addi	a0,s1,-2048
    1330:	c8eff0ef          	jal	ra,7be <GPIO_SetBits>
    1334:	80090593          	addi	a1,s2,-2048
    1338:	80048513          	addi	a0,s1,-2048
    133c:	c78ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1340:	e111                	bnez	a0,1344 <key_read+0x144>
    1342:	4431                	li	s0,12
    1344:	40012537          	lui	a0,0x40012
    1348:	20000593          	li	a1,512
    134c:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    1350:	c64ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1354:	e111                	bnez	a0,1358 <key_read+0x158>
    1356:	442d                	li	s0,11
    1358:	40012537          	lui	a0,0x40012
    135c:	08000593          	li	a1,128
    1360:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    1364:	c50ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1368:	e111                	bnez	a0,136c <key_read+0x16c>
    136a:	4429                	li	s0,10
    136c:	02000593          	li	a1,32
    1370:	40011537          	lui	a0,0x40011
    1374:	c40ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    1378:	e111                	bnez	a0,137c <key_read+0x17c>
    137a:	4425                	li	s0,9
    137c:	400114b7          	lui	s1,0x40011
    1380:	6905                	lui	s2,0x1
    1382:	40048513          	addi	a0,s1,1024 # 40011400 <_eusrstack+0x20009400>
    1386:	80090593          	addi	a1,s2,-2048 # 800 <__stack_size>
    138a:	c34ff0ef          	jal	ra,7be <GPIO_SetBits>
    138e:	40048513          	addi	a0,s1,1024
    1392:	20000593          	li	a1,512
    1396:	c28ff0ef          	jal	ra,7be <GPIO_SetBits>
    139a:	400124b7          	lui	s1,0x40012
    139e:	65a1                	lui	a1,0x8
    13a0:	80048513          	addi	a0,s1,-2048 # 40011800 <_eusrstack+0x20009800>
    13a4:	c1aff0ef          	jal	ra,7be <GPIO_SetBits>
    13a8:	6589                	lui	a1,0x2
    13aa:	80048513          	addi	a0,s1,-2048
    13ae:	c14ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    13b2:	80090593          	addi	a1,s2,-2048
    13b6:	80048513          	addi	a0,s1,-2048
    13ba:	bfaff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13be:	e111                	bnez	a0,13c2 <key_read+0x1c2>
    13c0:	4441                	li	s0,16
    13c2:	40012537          	lui	a0,0x40012
    13c6:	20000593          	li	a1,512
    13ca:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    13ce:	be6ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13d2:	e111                	bnez	a0,13d6 <key_read+0x1d6>
    13d4:	443d                	li	s0,15
    13d6:	40012537          	lui	a0,0x40012
    13da:	08000593          	li	a1,128
    13de:	80050513          	addi	a0,a0,-2048 # 40011800 <_eusrstack+0x20009800>
    13e2:	bd2ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13e6:	e111                	bnez	a0,13ea <key_read+0x1ea>
    13e8:	4439                	li	s0,14
    13ea:	02000593          	li	a1,32
    13ee:	40011537          	lui	a0,0x40011
    13f2:	bc2ff0ef          	jal	ra,7b4 <GPIO_ReadInputDataBit>
    13f6:	e111                	bnez	a0,13fa <key_read+0x1fa>
    13f8:	4435                	li	s0,13
    13fa:	8522                	mv	a0,s0
    13fc:	e19fe06f          	j	214 <__riscv_restore_0>

00001400 <SPI_LCD_Init>:
    1400:	dd7fe2ef          	jal	t0,1d6 <__riscv_save_4>
    1404:	1101                	addi	sp,sp,-32
    1406:	4585                	li	a1,1
    1408:	02c00513          	li	a0,44
    140c:	d84ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1410:	4585                	li	a1,1
    1412:	6521                	lui	a0,0x8
    1414:	40011437          	lui	s0,0x40011
    1418:	d96ff0ef          	jal	ra,9ae <RCC_APB1PeriphClockCmd>
    141c:	03800793          	li	a5,56
    1420:	4941                	li	s2,16
    1422:	448d                	li	s1,3
    1424:	858a                	mv	a1,sp
    1426:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    142a:	807c                	sh	a5,0(sp)
    142c:	c44a                	sw	s2,8(sp)
    142e:	c226                	sw	s1,4(sp)
    1430:	ac4ff0ef          	jal	ra,6f4 <GPIO_Init>
    1434:	40040513          	addi	a0,s0,1024
    1438:	45a1                	li	a1,8
    143a:	b84ff0ef          	jal	ra,7be <GPIO_SetBits>
    143e:	40040513          	addi	a0,s0,1024
    1442:	45c1                	li	a1,16
    1444:	b7aff0ef          	jal	ra,7be <GPIO_SetBits>
    1448:	40040513          	addi	a0,s0,1024
    144c:	02000593          	li	a1,32
    1450:	b6eff0ef          	jal	ra,7be <GPIO_SetBits>
    1454:	77e1                	lui	a5,0xffff8
    1456:	858a                	mv	a1,sp
    1458:	80040513          	addi	a0,s0,-2048
    145c:	807c                	sh	a5,0(sp)
    145e:	c44a                	sw	s2,8(sp)
    1460:	c226                	sw	s1,4(sp)
    1462:	a92ff0ef          	jal	ra,6f4 <GPIO_Init>
    1466:	80040513          	addi	a0,s0,-2048
    146a:	65a1                	lui	a1,0x8
    146c:	b52ff0ef          	jal	ra,7be <GPIO_SetBits>
    1470:	47a1                	li	a5,8
    1472:	49e1                	li	s3,24
    1474:	858a                	mv	a1,sp
    1476:	c0040513          	addi	a0,s0,-1024
    147a:	807c                	sh	a5,0(sp)
    147c:	c44e                	sw	s3,8(sp)
    147e:	c226                	sw	s1,4(sp)
    1480:	a74ff0ef          	jal	ra,6f4 <GPIO_Init>
    1484:	858a                	mv	a1,sp
    1486:	c0040513          	addi	a0,s0,-1024
    148a:	01211023          	sh	s2,0(sp)
    148e:	c44a                	sw	s2,8(sp)
    1490:	a64ff0ef          	jal	ra,6f4 <GPIO_Init>
    1494:	02000793          	li	a5,32
    1498:	858a                	mv	a1,sp
    149a:	c0040513          	addi	a0,s0,-1024
    149e:	807c                	sh	a5,0(sp)
    14a0:	c44e                	sw	s3,8(sp)
    14a2:	c226                	sw	s1,4(sp)
    14a4:	a50ff0ef          	jal	ra,6f4 <GPIO_Init>
    14a8:	010407b7          	lui	a5,0x1040
    14ac:	c63e                	sw	a5,12(sp)
    14ae:	020007b7          	lui	a5,0x2000
    14b2:	40004437          	lui	s0,0x40004
    14b6:	ca3e                	sw	a5,20(sp)
    14b8:	47a1                	li	a5,8
    14ba:	cc3e                	sw	a5,24(sp)
    14bc:	006c                	addi	a1,sp,12
    14be:	479d                	li	a5,7
    14c0:	c0040513          	addi	a0,s0,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    14c4:	86fc                	sh	a5,28(sp)
    14c6:	c802                	sw	zero,16(sp)
    14c8:	d04ff0ef          	jal	ra,9cc <SPI_Init>
    14cc:	4585                	li	a1,1
    14ce:	c0040513          	addi	a0,s0,-1024
    14d2:	d38ff0ef          	jal	ra,a0a <SPI_Cmd>
    14d6:	6105                	addi	sp,sp,32
    14d8:	d33fe06f          	j	20a <__riscv_restore_4>

000014dc <SPI3_IRQHandler>:
    14dc:	30200073          	mret

000014e0 <spi_readwrite>:
    14e0:	cf7fe2ef          	jal	t0,1d6 <__riscv_save_4>
    14e4:	400044b7          	lui	s1,0x40004
    14e8:	892a                	mv	s2,a0
    14ea:	0c900413          	li	s0,201
    14ee:	c0048993          	addi	s3,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    14f2:	4589                	li	a1,2
    14f4:	854e                	mv	a0,s3
    14f6:	d36ff0ef          	jal	ra,a2c <SPI_I2S_GetFlagStatus>
    14fa:	c905                	beqz	a0,152a <spi_readwrite+0x4a>
    14fc:	85ca                	mv	a1,s2
    14fe:	c0048513          	addi	a0,s1,-1024
    1502:	400044b7          	lui	s1,0x40004
    1506:	d1eff0ef          	jal	ra,a24 <SPI_I2S_SendData>
    150a:	0c900413          	li	s0,201
    150e:	c0048913          	addi	s2,s1,-1024 # 40003c00 <_eusrstack+0x1fffbc00>
    1512:	4585                	li	a1,1
    1514:	854a                	mv	a0,s2
    1516:	d16ff0ef          	jal	ra,a2c <SPI_I2S_GetFlagStatus>
    151a:	cd19                	beqz	a0,1538 <spi_readwrite+0x58>
    151c:	c0048513          	addi	a0,s1,-1024
    1520:	d08ff0ef          	jal	ra,a28 <SPI_I2S_ReceiveData>
    1524:	0ff57513          	andi	a0,a0,255
    1528:	a031                	j	1534 <spi_readwrite+0x54>
    152a:	147d                	addi	s0,s0,-1
    152c:	0ff47413          	andi	s0,s0,255
    1530:	f069                	bnez	s0,14f2 <spi_readwrite+0x12>
    1532:	4501                	li	a0,0
    1534:	cd7fe06f          	j	20a <__riscv_restore_4>
    1538:	147d                	addi	s0,s0,-1
    153a:	0ff47413          	andi	s0,s0,255
    153e:	f871                	bnez	s0,1512 <spi_readwrite+0x32>
    1540:	bfcd                	j	1532 <spi_readwrite+0x52>

00001542 <LCD_WR_DATA8>:
    1542:	caffe2ef          	jal	t0,1f0 <__riscv_save_0>
    1546:	40011437          	lui	s0,0x40011
    154a:	84aa                	mv	s1,a0
    154c:	45c1                	li	a1,16
    154e:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    1552:	a70ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1556:	45a1                	li	a1,8
    1558:	40040513          	addi	a0,s0,1024
    155c:	a62ff0ef          	jal	ra,7be <GPIO_SetBits>
    1560:	8526                	mv	a0,s1
    1562:	3fbd                	jal	14e0 <spi_readwrite>
    1564:	45c1                	li	a1,16
    1566:	40040513          	addi	a0,s0,1024
    156a:	a54ff0ef          	jal	ra,7be <GPIO_SetBits>
    156e:	ca7fe06f          	j	214 <__riscv_restore_0>

00001572 <LCD_WR_DATA>:
    1572:	c7ffe2ef          	jal	t0,1f0 <__riscv_save_0>
    1576:	40011437          	lui	s0,0x40011
    157a:	84aa                	mv	s1,a0
    157c:	45c1                	li	a1,16
    157e:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    1582:	a40ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1586:	45a1                	li	a1,8
    1588:	40040513          	addi	a0,s0,1024
    158c:	a32ff0ef          	jal	ra,7be <GPIO_SetBits>
    1590:	0084d513          	srli	a0,s1,0x8
    1594:	37b1                	jal	14e0 <spi_readwrite>
    1596:	0ff4f513          	andi	a0,s1,255
    159a:	3799                	jal	14e0 <spi_readwrite>
    159c:	45c1                	li	a1,16
    159e:	40040513          	addi	a0,s0,1024
    15a2:	a1cff0ef          	jal	ra,7be <GPIO_SetBits>
    15a6:	c6ffe06f          	j	214 <__riscv_restore_0>

000015aa <LCD_WR_REG>:
    15aa:	c47fe2ef          	jal	t0,1f0 <__riscv_save_0>
    15ae:	40011437          	lui	s0,0x40011
    15b2:	84aa                	mv	s1,a0
    15b4:	45c1                	li	a1,16
    15b6:	40040513          	addi	a0,s0,1024 # 40011400 <_eusrstack+0x20009400>
    15ba:	a08ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    15be:	45a1                	li	a1,8
    15c0:	40040513          	addi	a0,s0,1024
    15c4:	9feff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    15c8:	8526                	mv	a0,s1
    15ca:	3f19                	jal	14e0 <spi_readwrite>
    15cc:	45c1                	li	a1,16
    15ce:	40040513          	addi	a0,s0,1024
    15d2:	9ecff0ef          	jal	ra,7be <GPIO_SetBits>
    15d6:	c3ffe06f          	j	214 <__riscv_restore_0>

000015da <LCD_Address_Set>:
    15da:	c17fe2ef          	jal	t0,1f0 <__riscv_save_0>
    15de:	1141                	addi	sp,sp,-16
    15e0:	842a                	mv	s0,a0
    15e2:	02a00513          	li	a0,42
    15e6:	c236                	sw	a3,4(sp)
    15e8:	c62e                	sw	a1,12(sp)
    15ea:	c432                	sw	a2,8(sp)
    15ec:	3f7d                	jal	15aa <LCD_WR_REG>
    15ee:	00240513          	addi	a0,s0,2
    15f2:	0542                	slli	a0,a0,0x10
    15f4:	8141                	srli	a0,a0,0x10
    15f6:	3fb5                	jal	1572 <LCD_WR_DATA>
    15f8:	4622                	lw	a2,8(sp)
    15fa:	0609                	addi	a2,a2,2
    15fc:	01061513          	slli	a0,a2,0x10
    1600:	8141                	srli	a0,a0,0x10
    1602:	3f85                	jal	1572 <LCD_WR_DATA>
    1604:	02b00513          	li	a0,43
    1608:	374d                	jal	15aa <LCD_WR_REG>
    160a:	45b2                	lw	a1,12(sp)
    160c:	058d                	addi	a1,a1,3
    160e:	01059513          	slli	a0,a1,0x10
    1612:	8141                	srli	a0,a0,0x10
    1614:	3fb9                	jal	1572 <LCD_WR_DATA>
    1616:	4692                	lw	a3,4(sp)
    1618:	068d                	addi	a3,a3,3
    161a:	01069513          	slli	a0,a3,0x10
    161e:	8141                	srli	a0,a0,0x10
    1620:	3f89                	jal	1572 <LCD_WR_DATA>
    1622:	02c00513          	li	a0,44
    1626:	3751                	jal	15aa <LCD_WR_REG>
    1628:	0141                	addi	sp,sp,16
    162a:	bebfe06f          	j	214 <__riscv_restore_0>

0000162e <LCD_Init>:
    162e:	bc3fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1632:	33f9                	jal	1400 <SPI_LCD_Init>
    1634:	40011437          	lui	s0,0x40011
    1638:	45c1                	li	a1,16
    163a:	c0040513          	addi	a0,s0,-1024 # 40010c00 <_eusrstack+0x20008c00>
    163e:	984ff0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1642:	06400513          	li	a0,100
    1646:	2961                	jal	1ade <Delay_Ms>
    1648:	45c1                	li	a1,16
    164a:	c0040513          	addi	a0,s0,-1024
    164e:	970ff0ef          	jal	ra,7be <GPIO_SetBits>
    1652:	06400513          	li	a0,100
    1656:	2161                	jal	1ade <Delay_Ms>
    1658:	02000593          	li	a1,32
    165c:	40040513          	addi	a0,s0,1024
    1660:	95eff0ef          	jal	ra,7be <GPIO_SetBits>
    1664:	06400513          	li	a0,100
    1668:	299d                	jal	1ade <Delay_Ms>
    166a:	4545                	li	a0,17
    166c:	3f3d                	jal	15aa <LCD_WR_REG>
    166e:	07800513          	li	a0,120
    1672:	21b5                	jal	1ade <Delay_Ms>
    1674:	0b100513          	li	a0,177
    1678:	3f0d                	jal	15aa <LCD_WR_REG>
    167a:	4515                	li	a0,5
    167c:	35d9                	jal	1542 <LCD_WR_DATA8>
    167e:	03c00513          	li	a0,60
    1682:	35c1                	jal	1542 <LCD_WR_DATA8>
    1684:	03c00513          	li	a0,60
    1688:	3d6d                	jal	1542 <LCD_WR_DATA8>
    168a:	0b200513          	li	a0,178
    168e:	3f31                	jal	15aa <LCD_WR_REG>
    1690:	4515                	li	a0,5
    1692:	3d45                	jal	1542 <LCD_WR_DATA8>
    1694:	03c00513          	li	a0,60
    1698:	356d                	jal	1542 <LCD_WR_DATA8>
    169a:	03c00513          	li	a0,60
    169e:	3555                	jal	1542 <LCD_WR_DATA8>
    16a0:	0b300513          	li	a0,179
    16a4:	3719                	jal	15aa <LCD_WR_REG>
    16a6:	4515                	li	a0,5
    16a8:	3d69                	jal	1542 <LCD_WR_DATA8>
    16aa:	03c00513          	li	a0,60
    16ae:	3d51                	jal	1542 <LCD_WR_DATA8>
    16b0:	03c00513          	li	a0,60
    16b4:	3579                	jal	1542 <LCD_WR_DATA8>
    16b6:	4515                	li	a0,5
    16b8:	3569                	jal	1542 <LCD_WR_DATA8>
    16ba:	03c00513          	li	a0,60
    16be:	3551                	jal	1542 <LCD_WR_DATA8>
    16c0:	03c00513          	li	a0,60
    16c4:	3dbd                	jal	1542 <LCD_WR_DATA8>
    16c6:	0b400513          	li	a0,180
    16ca:	35c5                	jal	15aa <LCD_WR_REG>
    16cc:	450d                	li	a0,3
    16ce:	3d95                	jal	1542 <LCD_WR_DATA8>
    16d0:	03a00513          	li	a0,58
    16d4:	3dd9                	jal	15aa <LCD_WR_REG>
    16d6:	4515                	li	a0,5
    16d8:	35ad                	jal	1542 <LCD_WR_DATA8>
    16da:	0c000513          	li	a0,192
    16de:	35f1                	jal	15aa <LCD_WR_REG>
    16e0:	0a200513          	li	a0,162
    16e4:	3db9                	jal	1542 <LCD_WR_DATA8>
    16e6:	4509                	li	a0,2
    16e8:	3da9                	jal	1542 <LCD_WR_DATA8>
    16ea:	08400513          	li	a0,132
    16ee:	3d91                	jal	1542 <LCD_WR_DATA8>
    16f0:	0c100513          	li	a0,193
    16f4:	3d5d                	jal	15aa <LCD_WR_REG>
    16f6:	0c500513          	li	a0,197
    16fa:	35a1                	jal	1542 <LCD_WR_DATA8>
    16fc:	0c200513          	li	a0,194
    1700:	356d                	jal	15aa <LCD_WR_REG>
    1702:	4535                	li	a0,13
    1704:	3d3d                	jal	1542 <LCD_WR_DATA8>
    1706:	4501                	li	a0,0
    1708:	3d2d                	jal	1542 <LCD_WR_DATA8>
    170a:	0c300513          	li	a0,195
    170e:	3d71                	jal	15aa <LCD_WR_REG>
    1710:	08d00513          	li	a0,141
    1714:	353d                	jal	1542 <LCD_WR_DATA8>
    1716:	02a00513          	li	a0,42
    171a:	3525                	jal	1542 <LCD_WR_DATA8>
    171c:	0c400513          	li	a0,196
    1720:	3569                	jal	15aa <LCD_WR_REG>
    1722:	08d00513          	li	a0,141
    1726:	3d31                	jal	1542 <LCD_WR_DATA8>
    1728:	0ee00513          	li	a0,238
    172c:	3d19                	jal	1542 <LCD_WR_DATA8>
    172e:	0c500513          	li	a0,197
    1732:	3da5                	jal	15aa <LCD_WR_REG>
    1734:	4529                	li	a0,10
    1736:	3531                	jal	1542 <LCD_WR_DATA8>
    1738:	03600513          	li	a0,54
    173c:	35bd                	jal	15aa <LCD_WR_REG>
    173e:	0c800513          	li	a0,200
    1742:	3501                	jal	1542 <LCD_WR_DATA8>
    1744:	0e000513          	li	a0,224
    1748:	358d                	jal	15aa <LCD_WR_REG>
    174a:	4549                	li	a0,18
    174c:	3bdd                	jal	1542 <LCD_WR_DATA8>
    174e:	4571                	li	a0,28
    1750:	3bcd                	jal	1542 <LCD_WR_DATA8>
    1752:	4541                	li	a0,16
    1754:	33fd                	jal	1542 <LCD_WR_DATA8>
    1756:	4561                	li	a0,24
    1758:	33ed                	jal	1542 <LCD_WR_DATA8>
    175a:	03300513          	li	a0,51
    175e:	33d5                	jal	1542 <LCD_WR_DATA8>
    1760:	02c00513          	li	a0,44
    1764:	3bf9                	jal	1542 <LCD_WR_DATA8>
    1766:	02500513          	li	a0,37
    176a:	3be1                	jal	1542 <LCD_WR_DATA8>
    176c:	02800513          	li	a0,40
    1770:	3bc9                	jal	1542 <LCD_WR_DATA8>
    1772:	02800513          	li	a0,40
    1776:	33f1                	jal	1542 <LCD_WR_DATA8>
    1778:	02700513          	li	a0,39
    177c:	33d9                	jal	1542 <LCD_WR_DATA8>
    177e:	02f00513          	li	a0,47
    1782:	33c1                	jal	1542 <LCD_WR_DATA8>
    1784:	03c00513          	li	a0,60
    1788:	3b6d                	jal	1542 <LCD_WR_DATA8>
    178a:	4501                	li	a0,0
    178c:	3b5d                	jal	1542 <LCD_WR_DATA8>
    178e:	450d                	li	a0,3
    1790:	3b4d                	jal	1542 <LCD_WR_DATA8>
    1792:	450d                	li	a0,3
    1794:	337d                	jal	1542 <LCD_WR_DATA8>
    1796:	4541                	li	a0,16
    1798:	336d                	jal	1542 <LCD_WR_DATA8>
    179a:	0e100513          	li	a0,225
    179e:	3531                	jal	15aa <LCD_WR_REG>
    17a0:	4549                	li	a0,18
    17a2:	3345                	jal	1542 <LCD_WR_DATA8>
    17a4:	4571                	li	a0,28
    17a6:	3b71                	jal	1542 <LCD_WR_DATA8>
    17a8:	4541                	li	a0,16
    17aa:	3b61                	jal	1542 <LCD_WR_DATA8>
    17ac:	4561                	li	a0,24
    17ae:	3b51                	jal	1542 <LCD_WR_DATA8>
    17b0:	02d00513          	li	a0,45
    17b4:	3379                	jal	1542 <LCD_WR_DATA8>
    17b6:	02800513          	li	a0,40
    17ba:	3361                	jal	1542 <LCD_WR_DATA8>
    17bc:	02300513          	li	a0,35
    17c0:	3349                	jal	1542 <LCD_WR_DATA8>
    17c2:	02800513          	li	a0,40
    17c6:	3bb5                	jal	1542 <LCD_WR_DATA8>
    17c8:	02800513          	li	a0,40
    17cc:	3b9d                	jal	1542 <LCD_WR_DATA8>
    17ce:	02600513          	li	a0,38
    17d2:	3b85                	jal	1542 <LCD_WR_DATA8>
    17d4:	02f00513          	li	a0,47
    17d8:	33ad                	jal	1542 <LCD_WR_DATA8>
    17da:	03b00513          	li	a0,59
    17de:	3395                	jal	1542 <LCD_WR_DATA8>
    17e0:	4501                	li	a0,0
    17e2:	3385                	jal	1542 <LCD_WR_DATA8>
    17e4:	450d                	li	a0,3
    17e6:	3bb1                	jal	1542 <LCD_WR_DATA8>
    17e8:	450d                	li	a0,3
    17ea:	3ba1                	jal	1542 <LCD_WR_DATA8>
    17ec:	4541                	li	a0,16
    17ee:	3b91                	jal	1542 <LCD_WR_DATA8>
    17f0:	02000513          	li	a0,32
    17f4:	3b5d                	jal	15aa <LCD_WR_REG>
    17f6:	454d                	li	a0,19
    17f8:	3b4d                	jal	15aa <LCD_WR_REG>
    17fa:	02a00513          	li	a0,42
    17fe:	3375                	jal	15aa <LCD_WR_REG>
    1800:	4501                	li	a0,0
    1802:	3381                	jal	1542 <LCD_WR_DATA8>
    1804:	4501                	li	a0,0
    1806:	3b35                	jal	1542 <LCD_WR_DATA8>
    1808:	4501                	li	a0,0
    180a:	3b25                	jal	1542 <LCD_WR_DATA8>
    180c:	07f00513          	li	a0,127
    1810:	3b0d                	jal	1542 <LCD_WR_DATA8>
    1812:	02b00513          	li	a0,43
    1816:	3b51                	jal	15aa <LCD_WR_REG>
    1818:	4501                	li	a0,0
    181a:	3325                	jal	1542 <LCD_WR_DATA8>
    181c:	4501                	li	a0,0
    181e:	3315                	jal	1542 <LCD_WR_DATA8>
    1820:	4501                	li	a0,0
    1822:	3305                	jal	1542 <LCD_WR_DATA8>
    1824:	07f00513          	li	a0,127
    1828:	3b29                	jal	1542 <LCD_WR_DATA8>
    182a:	02900513          	li	a0,41
    182e:	3bb5                	jal	15aa <LCD_WR_REG>
    1830:	9e5fe06f          	j	214 <__riscv_restore_0>

00001834 <LCD_Fill>:
    1834:	9a3fe2ef          	jal	t0,1d6 <__riscv_save_4>
    1838:	89b2                	mv	s3,a2
    183a:	8936                	mv	s2,a3
    183c:	167d                	addi	a2,a2,-1
    183e:	16fd                	addi	a3,a3,-1
    1840:	06c2                	slli	a3,a3,0x10
    1842:	0642                	slli	a2,a2,0x10
    1844:	82c1                	srli	a3,a3,0x10
    1846:	8241                	srli	a2,a2,0x10
    1848:	8a2a                	mv	s4,a0
    184a:	842e                	mv	s0,a1
    184c:	8aba                	mv	s5,a4
    184e:	d8dff0ef          	jal	ra,15da <LCD_Address_Set>
    1852:	03246063          	bltu	s0,s2,1872 <LCD_Fill+0x3e>
    1856:	9b5fe06f          	j	20a <__riscv_restore_4>
    185a:	0485                	addi	s1,s1,1
    185c:	8556                	mv	a0,s5
    185e:	04c2                	slli	s1,s1,0x10
    1860:	d13ff0ef          	jal	ra,1572 <LCD_WR_DATA>
    1864:	80c1                	srli	s1,s1,0x10
    1866:	ff34eae3          	bltu	s1,s3,185a <LCD_Fill+0x26>
    186a:	0405                	addi	s0,s0,1
    186c:	0442                	slli	s0,s0,0x10
    186e:	8041                	srli	s0,s0,0x10
    1870:	b7cd                	j	1852 <LCD_Fill+0x1e>
    1872:	84d2                	mv	s1,s4
    1874:	bfcd                	j	1866 <LCD_Fill+0x32>

00001876 <TIM2_PWM_Init>:
    1876:	97bfe2ef          	jal	t0,1f0 <__riscv_save_0>
    187a:	7179                	addi	sp,sp,-48
    187c:	4585                	li	a1,1
    187e:	4505                	li	a0,1
    1880:	92eff0ef          	jal	ra,9ae <RCC_APB1PeriphClockCmd>
    1884:	05f00793          	li	a5,95
    1888:	c43e                	sw	a5,8(sp)
    188a:	6795                	lui	a5,0x5
    188c:	e1f78793          	addi	a5,a5,-481 # 4e1f <_data_lma+0x184b>
    1890:	002c                	addi	a1,sp,8
    1892:	40000537          	lui	a0,0x40000
    1896:	c63e                	sw	a5,12(sp)
    1898:	99eff0ef          	jal	ra,a36 <TIM_TimeBaseInit>
    189c:	67c1                	lui	a5,0x10
    189e:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xca8c>
    18a2:	d03e                	sw	a5,32(sp)
    18a4:	100c                	addi	a1,sp,32
    18a6:	5dc00793          	li	a5,1500
    18aa:	40000537          	lui	a0,0x40000
    18ae:	02f11323          	sh	a5,38(sp)
    18b2:	02011423          	sh	zero,40(sp)
    18b6:	a2aff0ef          	jal	ra,ae0 <TIM_OC1Init>
    18ba:	45a1                	li	a1,8
    18bc:	40000537          	lui	a0,0x40000
    18c0:	ae6ff0ef          	jal	ra,ba6 <TIM_OC1PreloadConfig>
    18c4:	4585                	li	a1,1
    18c6:	40000537          	lui	a0,0x40000
    18ca:	ac2ff0ef          	jal	ra,b8c <TIM_ARRPreloadConfig>
    18ce:	4585                	li	a1,1
    18d0:	40000537          	lui	a0,0x40000
    18d4:	a8eff0ef          	jal	ra,b62 <TIM_Cmd>
    18d8:	4585                	li	a1,1
    18da:	4511                	li	a0,4
    18dc:	8b4ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    18e0:	4785                	li	a5,1
    18e2:	82fc                	sh	a5,20(sp)
    18e4:	40011537          	lui	a0,0x40011
    18e8:	47e1                	li	a5,24
    18ea:	ce3e                	sw	a5,28(sp)
    18ec:	084c                	addi	a1,sp,20
    18ee:	478d                	li	a5,3
    18f0:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    18f4:	cc3e                	sw	a5,24(sp)
    18f6:	dfffe0ef          	jal	ra,6f4 <GPIO_Init>
    18fa:	6145                	addi	sp,sp,48
    18fc:	919fe06f          	j	214 <__riscv_restore_0>

00001900 <stir_360>:
    1900:	8f1fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1904:	4785                	li	a5,1
    1906:	02f51963          	bne	a0,a5,1938 <stir_360+0x38>
    190a:	06400713          	li	a4,100
    190e:	0ff5f793          	andi	a5,a1,255
    1912:	00b77463          	bgeu	a4,a1,191a <stir_360+0x1a>
    1916:	06400793          	li	a5,100
    191a:	45a9                	li	a1,10
    191c:	02b785b3          	mul	a1,a5,a1
    1920:	5dc00793          	li	a5,1500
    1924:	8f8d                	sub	a5,a5,a1
    1926:	07c2                	slli	a5,a5,0x10
    1928:	83c1                	srli	a5,a5,0x10
    192a:	85be                	mv	a1,a5
    192c:	40000537          	lui	a0,0x40000
    1930:	a84ff0ef          	jal	ra,bb4 <TIM_SetCompare1>
    1934:	8e1fe06f          	j	214 <__riscv_restore_0>
    1938:	4709                	li	a4,2
    193a:	5dc00793          	li	a5,1500
    193e:	fee516e3          	bne	a0,a4,192a <stir_360+0x2a>
    1942:	06400713          	li	a4,100
    1946:	0ff5f793          	andi	a5,a1,255
    194a:	00b77463          	bgeu	a4,a1,1952 <stir_360+0x52>
    194e:	06400793          	li	a5,100
    1952:	45a9                	li	a1,10
    1954:	02b787b3          	mul	a5,a5,a1
    1958:	5dc78793          	addi	a5,a5,1500
    195c:	b7f9                	j	192a <stir_360+0x2a>

0000195e <stir>:
    195e:	893fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1962:	c519                	beqz	a0,1970 <stir+0x12>
    1964:	03200593          	li	a1,50
    1968:	4505                	li	a0,1
    196a:	3f59                	jal	1900 <stir_360>
    196c:	8a9fe06f          	j	214 <__riscv_restore_0>
    1970:	5dc00593          	li	a1,1500
    1974:	40000537          	lui	a0,0x40000
    1978:	a3cff0ef          	jal	ra,bb4 <TIM_SetCompare1>
    197c:	bfc5                	j	196c <stir+0xe>

0000197e <WaterLevel_Init>:
    197e:	873fe2ef          	jal	t0,1f0 <__riscv_save_0>
    1982:	1141                	addi	sp,sp,-16
    1984:	4585                	li	a1,1
    1986:	4541                	li	a0,16
    1988:	c202                	sw	zero,4(sp)
    198a:	c402                	sw	zero,8(sp)
    198c:	c602                	sw	zero,12(sp)
    198e:	802ff0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1992:	47c1                	li	a5,16
    1994:	827c                	sh	a5,4(sp)
    1996:	478d                	li	a5,3
    1998:	c43e                	sw	a5,8(sp)
    199a:	004c                	addi	a1,sp,4
    199c:	04800793          	li	a5,72
    19a0:	40011537          	lui	a0,0x40011
    19a4:	c63e                	sw	a5,12(sp)
    19a6:	d4ffe0ef          	jal	ra,6f4 <GPIO_Init>
    19aa:	0141                	addi	sp,sp,16
    19ac:	869fe06f          	j	214 <__riscv_restore_0>

000019b0 <delay_us>:
    19b0:	050e                	slli	a0,a0,0x3
    19b2:	4781                	li	a5,0
    19b4:	00f51363          	bne	a0,a5,19ba <delay_us+0xa>
    19b8:	8082                	ret
    19ba:	0001                	nop
    19bc:	0785                	addi	a5,a5,1
    19be:	bfdd                	j	19b4 <delay_us+0x4>

000019c0 <delay_ms>:
    19c0:	831fe2ef          	jal	t0,1f0 <__riscv_save_0>
    19c4:	84aa                	mv	s1,a0
    19c6:	4401                	li	s0,0
    19c8:	00941463          	bne	s0,s1,19d0 <delay_ms+0x10>
    19cc:	849fe06f          	j	214 <__riscv_restore_0>
    19d0:	3e800513          	li	a0,1000
    19d4:	3ff1                	jal	19b0 <delay_us>
    19d6:	0405                	addi	s0,s0,1
    19d8:	bfc5                	j	19c8 <delay_ms+0x8>

000019da <WaterPump_Init>:
    19da:	817fe2ef          	jal	t0,1f0 <__riscv_save_0>
    19de:	1141                	addi	sp,sp,-16
    19e0:	4585                	li	a1,1
    19e2:	4541                	li	a0,16
    19e4:	fadfe0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    19e8:	6789                	lui	a5,0x2
    19ea:	440d                	li	s0,3
    19ec:	44c1                	li	s1,16
    19ee:	004c                	addi	a1,sp,4
    19f0:	40011537          	lui	a0,0x40011
    19f4:	827c                	sh	a5,4(sp)
    19f6:	c626                	sw	s1,12(sp)
    19f8:	c422                	sw	s0,8(sp)
    19fa:	cfbfe0ef          	jal	ra,6f4 <GPIO_Init>
    19fe:	6589                	lui	a1,0x2
    1a00:	40011537          	lui	a0,0x40011
    1a04:	dbffe0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1a08:	4585                	li	a1,1
    1a0a:	4541                	li	a0,16
    1a0c:	f85fe0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1a10:	6785                	lui	a5,0x1
    1a12:	80078793          	addi	a5,a5,-2048 # 800 <__stack_size>
    1a16:	004c                	addi	a1,sp,4
    1a18:	40011537          	lui	a0,0x40011
    1a1c:	827c                	sh	a5,4(sp)
    1a1e:	c626                	sw	s1,12(sp)
    1a20:	c422                	sw	s0,8(sp)
    1a22:	cd3fe0ef          	jal	ra,6f4 <GPIO_Init>
    1a26:	6585                	lui	a1,0x1
    1a28:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1a2c:	40011537          	lui	a0,0x40011
    1a30:	d93fe0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1a34:	86018793          	addi	a5,gp,-1952 # 20000108 <g_pump_ctrl>
    1a38:	4705                	li	a4,1
    1a3a:	0007a023          	sw	zero,0(a5)
    1a3e:	a3da                	sh	a4,4(a5)
    1a40:	0007a423          	sw	zero,8(a5)
    1a44:	0007a623          	sw	zero,12(a5)
    1a48:	0007a823          	sw	zero,16(a5)
    1a4c:	abda                	sh	a4,20(a5)
    1a4e:	0007ac23          	sw	zero,24(a5)
    1a52:	0007ae23          	sw	zero,28(a5)
    1a56:	0141                	addi	sp,sp,16
    1a58:	fbcfe06f          	j	214 <__riscv_restore_0>

00001a5c <WaterPump_Control>:
    1a5c:	4705                	li	a4,1
    1a5e:	04a76a63          	bltu	a4,a0,1ab2 <WaterPump_Control+0x56>
    1a62:	f8efe2ef          	jal	t0,1f0 <__riscv_save_0>
    1a66:	86018793          	addi	a5,gp,-1952 # 20000108 <g_pump_ctrl>
    1a6a:	00451493          	slli	s1,a0,0x4
    1a6e:	97a6                	add	a5,a5,s1
    1a70:	23dc                	lbu	a5,4(a5)
    1a72:	86018413          	addi	s0,gp,-1952 # 20000108 <g_pump_ctrl>
    1a76:	cf99                	beqz	a5,1a94 <WaterPump_Control+0x38>
    1a78:	02e59063          	bne	a1,a4,1a98 <WaterPump_Control+0x3c>
    1a7c:	6589                	lui	a1,0x2
    1a7e:	c501                	beqz	a0,1a86 <WaterPump_Control+0x2a>
    1a80:	6585                	lui	a1,0x1
    1a82:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1a86:	40011537          	lui	a0,0x40011
    1a8a:	d35fe0ef          	jal	ra,7be <GPIO_SetBits>
    1a8e:	9426                	add	s0,s0,s1
    1a90:	4785                	li	a5,1
    1a92:	c01c                	sw	a5,0(s0)
    1a94:	f80fe06f          	j	214 <__riscv_restore_0>
    1a98:	6589                	lui	a1,0x2
    1a9a:	c501                	beqz	a0,1aa2 <WaterPump_Control+0x46>
    1a9c:	6585                	lui	a1,0x1
    1a9e:	80058593          	addi	a1,a1,-2048 # 800 <__stack_size>
    1aa2:	40011537          	lui	a0,0x40011
    1aa6:	9426                	add	s0,s0,s1
    1aa8:	d1bfe0ef          	jal	ra,7c2 <GPIO_ResetBits>
    1aac:	00042023          	sw	zero,0(s0)
    1ab0:	b7d5                	j	1a94 <WaterPump_Control+0x38>
    1ab2:	8082                	ret

00001ab4 <Delay_Init>:
    1ab4:	200007b7          	lui	a5,0x20000
    1ab8:	0a87a783          	lw	a5,168(a5) # 200000a8 <SystemCoreClock>
    1abc:	007a1737          	lui	a4,0x7a1
    1ac0:	20070713          	addi	a4,a4,512 # 7a1200 <_data_lma+0x79dc2c>
    1ac4:	02e7d7b3          	divu	a5,a5,a4
    1ac8:	0ff7f793          	andi	a5,a5,255
    1acc:	82f18723          	sb	a5,-2002(gp) # 200000d6 <p_us>
    1ad0:	3e800713          	li	a4,1000
    1ad4:	02e787b3          	mul	a5,a5,a4
    1ad8:	82f19623          	sh	a5,-2004(gp) # 200000d4 <p_ms>
    1adc:	8082                	ret

00001ade <Delay_Ms>:
    1ade:	e000f7b7          	lui	a5,0xe000f
    1ae2:	43d8                	lw	a4,4(a5)
    1ae4:	4681                	li	a3,0
    1ae6:	9b79                	andi	a4,a4,-2
    1ae8:	c3d8                	sw	a4,4(a5)
    1aea:	82c1d703          	lhu	a4,-2004(gp) # 200000d4 <p_ms>
    1aee:	02a70633          	mul	a2,a4,a0
    1af2:	cb90                	sw	a2,16(a5)
    1af4:	cbd4                	sw	a3,20(a5)
    1af6:	4398                	lw	a4,0(a5)
    1af8:	01076713          	ori	a4,a4,16
    1afc:	c398                	sw	a4,0(a5)
    1afe:	4398                	lw	a4,0(a5)
    1b00:	02176713          	ori	a4,a4,33
    1b04:	c398                	sw	a4,0(a5)
    1b06:	43d8                	lw	a4,4(a5)
    1b08:	8b05                	andi	a4,a4,1
    1b0a:	df75                	beqz	a4,1b06 <Delay_Ms+0x28>
    1b0c:	4398                	lw	a4,0(a5)
    1b0e:	9b79                	andi	a4,a4,-2
    1b10:	c398                	sw	a4,0(a5)
    1b12:	8082                	ret

00001b14 <USART_Printf_Init>:
    1b14:	edcfe2ef          	jal	t0,1f0 <__riscv_save_0>
    1b18:	842a                	mv	s0,a0
    1b1a:	6511                	lui	a0,0x4
    1b1c:	1101                	addi	sp,sp,-32
    1b1e:	4585                	li	a1,1
    1b20:	0511                	addi	a0,a0,4
    1b22:	e6ffe0ef          	jal	ra,990 <RCC_APB2PeriphClockCmd>
    1b26:	20000793          	li	a5,512
    1b2a:	827c                	sh	a5,4(sp)
    1b2c:	40011537          	lui	a0,0x40011
    1b30:	478d                	li	a5,3
    1b32:	c43e                	sw	a5,8(sp)
    1b34:	004c                	addi	a1,sp,4
    1b36:	47e1                	li	a5,24
    1b38:	80050513          	addi	a0,a0,-2048 # 40010800 <_eusrstack+0x20008800>
    1b3c:	c63e                	sw	a5,12(sp)
    1b3e:	bb7fe0ef          	jal	ra,6f4 <GPIO_Init>
    1b42:	c822                	sw	s0,16(sp)
    1b44:	40014437          	lui	s0,0x40014
    1b48:	000807b7          	lui	a5,0x80
    1b4c:	080c                	addi	a1,sp,16
    1b4e:	80040513          	addi	a0,s0,-2048 # 40013800 <_eusrstack+0x2000b800>
    1b52:	cc3e                	sw	a5,24(sp)
    1b54:	ca02                	sw	zero,20(sp)
    1b56:	00011e23          	sh	zero,28(sp)
    1b5a:	882ff0ef          	jal	ra,bdc <USART_Init>
    1b5e:	4585                	li	a1,1
    1b60:	80040513          	addi	a0,s0,-2048
    1b64:	906ff0ef          	jal	ra,c6a <USART_Cmd>
    1b68:	6105                	addi	sp,sp,32
    1b6a:	eaafe06f          	j	214 <__riscv_restore_0>

00001b6e <_write>:
    1b6e:	e68fe2ef          	jal	t0,1d6 <__riscv_save_4>
    1b72:	400144b7          	lui	s1,0x40014
    1b76:	89ae                	mv	s3,a1
    1b78:	8932                	mv	s2,a2
    1b7a:	4401                	li	s0,0
    1b7c:	80048493          	addi	s1,s1,-2048 # 40013800 <_eusrstack+0x2000b800>
    1b80:	01244563          	blt	s0,s2,1b8a <_write+0x1c>
    1b84:	854a                	mv	a0,s2
    1b86:	e84fe06f          	j	20a <__riscv_restore_4>
    1b8a:	04000593          	li	a1,64
    1b8e:	8526                	mv	a0,s1
    1b90:	8f8ff0ef          	jal	ra,c88 <USART_GetFlagStatus>
    1b94:	d97d                	beqz	a0,1b8a <_write+0x1c>
    1b96:	008987b3          	add	a5,s3,s0
    1b9a:	00078583          	lb	a1,0(a5) # 80000 <_data_lma+0x7ca2c>
    1b9e:	8526                	mv	a0,s1
    1ba0:	0405                	addi	s0,s0,1
    1ba2:	05c2                	slli	a1,a1,0x10
    1ba4:	81c1                	srli	a1,a1,0x10
    1ba6:	8daff0ef          	jal	ra,c80 <USART_SendData>
    1baa:	bfd9                	j	1b80 <_write+0x12>

00001bac <_sbrk>:
    1bac:	80818713          	addi	a4,gp,-2040 # 200000b0 <curbrk.5274>
    1bb0:	431c                	lw	a5,0(a4)
    1bb2:	88418693          	addi	a3,gp,-1916 # 2000012c <_ebss>
    1bb6:	953e                	add	a0,a0,a5
    1bb8:	00d56b63          	bltu	a0,a3,1bce <_sbrk+0x22>
    1bbc:	200086b7          	lui	a3,0x20008
    1bc0:	80068693          	addi	a3,a3,-2048 # 20007800 <_heap_end>
    1bc4:	00a6e563          	bltu	a3,a0,1bce <_sbrk+0x22>
    1bc8:	c308                	sw	a0,0(a4)
    1bca:	853e                	mv	a0,a5
    1bcc:	8082                	ret
    1bce:	57fd                	li	a5,-1
    1bd0:	bfed                	j	1bca <_sbrk+0x1e>

00001bd2 <__lesf2>:
    1bd2:	01755693          	srli	a3,a0,0x17
    1bd6:	008007b7          	lui	a5,0x800
    1bda:	17fd                	addi	a5,a5,-1
    1bdc:	0175d613          	srli	a2,a1,0x17
    1be0:	0ff6f693          	andi	a3,a3,255
    1be4:	0ff00813          	li	a6,255
    1be8:	00a7f8b3          	and	a7,a5,a0
    1bec:	01f55713          	srli	a4,a0,0x1f
    1bf0:	8fed                	and	a5,a5,a1
    1bf2:	0ff67613          	andi	a2,a2,255
    1bf6:	81fd                	srli	a1,a1,0x1f
    1bf8:	03068763          	beq	a3,a6,1c26 <__lesf2+0x54>
    1bfc:	01060963          	beq	a2,a6,1c0e <__lesf2+0x3c>
    1c00:	ea85                	bnez	a3,1c30 <__lesf2+0x5e>
    1c02:	ea11                	bnez	a2,1c16 <__lesf2+0x44>
    1c04:	eb89                	bnez	a5,1c16 <__lesf2+0x44>
    1c06:	4501                	li	a0,0
    1c08:	00089b63          	bnez	a7,1c1e <__lesf2+0x4c>
    1c0c:	8082                	ret
    1c0e:	4509                	li	a0,2
    1c10:	fff5                	bnez	a5,1c0c <__lesf2+0x3a>
    1c12:	dae5                	beqz	a3,1c02 <__lesf2+0x30>
    1c14:	a831                	j	1c30 <__lesf2+0x5e>
    1c16:	02088c63          	beqz	a7,1c4e <__lesf2+0x7c>
    1c1a:	04b70063          	beq	a4,a1,1c5a <__lesf2+0x88>
    1c1e:	4505                	li	a0,1
    1c20:	d775                	beqz	a4,1c0c <__lesf2+0x3a>
    1c22:	557d                	li	a0,-1
    1c24:	8082                	ret
    1c26:	4509                	li	a0,2
    1c28:	02089863          	bnez	a7,1c58 <__lesf2+0x86>
    1c2c:	02d60463          	beq	a2,a3,1c54 <__lesf2+0x82>
    1c30:	e211                	bnez	a2,1c34 <__lesf2+0x62>
    1c32:	d7f5                	beqz	a5,1c1e <__lesf2+0x4c>
    1c34:	feb715e3          	bne	a4,a1,1c1e <__lesf2+0x4c>
    1c38:	fed643e3          	blt	a2,a3,1c1e <__lesf2+0x4c>
    1c3c:	00c6c763          	blt	a3,a2,1c4a <__lesf2+0x78>
    1c40:	fd17efe3          	bltu	a5,a7,1c1e <__lesf2+0x4c>
    1c44:	4501                	li	a0,0
    1c46:	fcf8f3e3          	bgeu	a7,a5,1c0c <__lesf2+0x3a>
    1c4a:	e319                	bnez	a4,1c50 <__lesf2+0x7e>
    1c4c:	bfd9                	j	1c22 <__lesf2+0x50>
    1c4e:	d9f1                	beqz	a1,1c22 <__lesf2+0x50>
    1c50:	4505                	li	a0,1
    1c52:	8082                	ret
    1c54:	d3e5                	beqz	a5,1c34 <__lesf2+0x62>
    1c56:	8082                	ret
    1c58:	8082                	ret
    1c5a:	4681                	li	a3,0
    1c5c:	b7c5                	j	1c3c <__lesf2+0x6a>

00001c5e <__mulsf3>:
    1c5e:	7179                	addi	sp,sp,-48
    1c60:	d422                	sw	s0,40(sp)
    1c62:	01755413          	srli	s0,a0,0x17
    1c66:	ce4e                	sw	s3,28(sp)
    1c68:	cc52                	sw	s4,24(sp)
    1c6a:	00951993          	slli	s3,a0,0x9
    1c6e:	d606                	sw	ra,44(sp)
    1c70:	d226                	sw	s1,36(sp)
    1c72:	d04a                	sw	s2,32(sp)
    1c74:	ca56                	sw	s5,20(sp)
    1c76:	c85a                	sw	s6,16(sp)
    1c78:	0ff47413          	andi	s0,s0,255
    1c7c:	0099d993          	srli	s3,s3,0x9
    1c80:	01f55a13          	srli	s4,a0,0x1f
    1c84:	c469                	beqz	s0,1d4e <__mulsf3+0xf0>
    1c86:	0ff00793          	li	a5,255
    1c8a:	0ef40863          	beq	s0,a5,1d7a <__mulsf3+0x11c>
    1c8e:	00399793          	slli	a5,s3,0x3
    1c92:	04000737          	lui	a4,0x4000
    1c96:	00e7e9b3          	or	s3,a5,a4
    1c9a:	f8140413          	addi	s0,s0,-127
    1c9e:	4481                	li	s1,0
    1ca0:	4b01                	li	s6,0
    1ca2:	0175d713          	srli	a4,a1,0x17
    1ca6:	00959a93          	slli	s5,a1,0x9
    1caa:	0ff77713          	andi	a4,a4,255
    1cae:	009ada93          	srli	s5,s5,0x9
    1cb2:	01f5d913          	srli	s2,a1,0x1f
    1cb6:	cf45                	beqz	a4,1d6e <__mulsf3+0x110>
    1cb8:	0ff00793          	li	a5,255
    1cbc:	02f70c63          	beq	a4,a5,1cf4 <__mulsf3+0x96>
    1cc0:	0a8e                	slli	s5,s5,0x3
    1cc2:	f8170713          	addi	a4,a4,-127 # 3ffff81 <_data_lma+0x3ffc9ad>
    1cc6:	040007b7          	lui	a5,0x4000
    1cca:	00faeab3          	or	s5,s5,a5
    1cce:	943a                	add	s0,s0,a4
    1cd0:	4601                	li	a2,0
    1cd2:	012a4533          	xor	a0,s4,s2
    1cd6:	47bd                	li	a5,15
    1cd8:	86aa                	mv	a3,a0
    1cda:	00140593          	addi	a1,s0,1
    1cde:	1097e063          	bltu	a5,s1,1dde <__mulsf3+0x180>
    1ce2:	00001717          	auipc	a4,0x1
    1ce6:	71670713          	addi	a4,a4,1814 # 33f8 <_read+0x16a>
    1cea:	048a                	slli	s1,s1,0x2
    1cec:	94ba                	add	s1,s1,a4
    1cee:	409c                	lw	a5,0(s1)
    1cf0:	97ba                	add	a5,a5,a4
    1cf2:	8782                	jr	a5
    1cf4:	0ff40413          	addi	s0,s0,255
    1cf8:	0c0a9663          	bnez	s5,1dc4 <__mulsf3+0x166>
    1cfc:	0024e493          	ori	s1,s1,2
    1d00:	4609                	li	a2,2
    1d02:	bfc1                	j	1cd2 <__mulsf3+0x74>
    1d04:	4501                	li	a0,0
    1d06:	0ff00713          	li	a4,255
    1d0a:	004007b7          	lui	a5,0x400
    1d0e:	50b2                	lw	ra,44(sp)
    1d10:	5422                	lw	s0,40(sp)
    1d12:	07a6                	slli	a5,a5,0x9
    1d14:	075e                	slli	a4,a4,0x17
    1d16:	83a5                	srli	a5,a5,0x9
    1d18:	057e                	slli	a0,a0,0x1f
    1d1a:	8fd9                	or	a5,a5,a4
    1d1c:	5492                	lw	s1,36(sp)
    1d1e:	5902                	lw	s2,32(sp)
    1d20:	49f2                	lw	s3,28(sp)
    1d22:	4a62                	lw	s4,24(sp)
    1d24:	4ad2                	lw	s5,20(sp)
    1d26:	4b42                	lw	s6,16(sp)
    1d28:	8d5d                	or	a0,a0,a5
    1d2a:	6145                	addi	sp,sp,48
    1d2c:	8082                	ret
    1d2e:	86ca                	mv	a3,s2
    1d30:	89d6                	mv	s3,s5
    1d32:	8b32                	mv	s6,a2
    1d34:	4789                	li	a5,2
    1d36:	08fb0f63          	beq	s6,a5,1dd4 <__mulsf3+0x176>
    1d3a:	478d                	li	a5,3
    1d3c:	fcfb04e3          	beq	s6,a5,1d04 <__mulsf3+0xa6>
    1d40:	4785                	li	a5,1
    1d42:	8536                	mv	a0,a3
    1d44:	1afb1063          	bne	s6,a5,1ee4 <__mulsf3+0x286>
    1d48:	4701                	li	a4,0
    1d4a:	4781                	li	a5,0
    1d4c:	b7c9                	j	1d0e <__mulsf3+0xb0>
    1d4e:	04099d63          	bnez	s3,1da8 <__mulsf3+0x14a>
    1d52:	0175d713          	srli	a4,a1,0x17
    1d56:	00959a93          	slli	s5,a1,0x9
    1d5a:	0ff77713          	andi	a4,a4,255
    1d5e:	4491                	li	s1,4
    1d60:	4401                	li	s0,0
    1d62:	4b05                	li	s6,1
    1d64:	009ada93          	srli	s5,s5,0x9
    1d68:	01f5d913          	srli	s2,a1,0x1f
    1d6c:	f731                	bnez	a4,1cb8 <__mulsf3+0x5a>
    1d6e:	000a9d63          	bnez	s5,1d88 <__mulsf3+0x12a>
    1d72:	0014e493          	ori	s1,s1,1
    1d76:	4605                	li	a2,1
    1d78:	bfa9                	j	1cd2 <__mulsf3+0x74>
    1d7a:	02099263          	bnez	s3,1d9e <__mulsf3+0x140>
    1d7e:	44a1                	li	s1,8
    1d80:	0ff00413          	li	s0,255
    1d84:	4b09                	li	s6,2
    1d86:	bf31                	j	1ca2 <__mulsf3+0x44>
    1d88:	8556                	mv	a0,s5
    1d8a:	2cd9                	jal	2060 <__clzsi2>
    1d8c:	ffb50793          	addi	a5,a0,-5
    1d90:	8c09                	sub	s0,s0,a0
    1d92:	00fa9ab3          	sll	s5,s5,a5
    1d96:	f8a40413          	addi	s0,s0,-118
    1d9a:	4601                	li	a2,0
    1d9c:	bf1d                	j	1cd2 <__mulsf3+0x74>
    1d9e:	44b1                	li	s1,12
    1da0:	0ff00413          	li	s0,255
    1da4:	4b0d                	li	s6,3
    1da6:	bdf5                	j	1ca2 <__mulsf3+0x44>
    1da8:	854e                	mv	a0,s3
    1daa:	c62e                	sw	a1,12(sp)
    1dac:	2c55                	jal	2060 <__clzsi2>
    1dae:	ffb50793          	addi	a5,a0,-5
    1db2:	f8a00413          	li	s0,-118
    1db6:	00f999b3          	sll	s3,s3,a5
    1dba:	8c09                	sub	s0,s0,a0
    1dbc:	4481                	li	s1,0
    1dbe:	4b01                	li	s6,0
    1dc0:	45b2                	lw	a1,12(sp)
    1dc2:	b5c5                	j	1ca2 <__mulsf3+0x44>
    1dc4:	0034e493          	ori	s1,s1,3
    1dc8:	460d                	li	a2,3
    1dca:	b721                	j	1cd2 <__mulsf3+0x74>
    1dcc:	4789                	li	a5,2
    1dce:	86d2                	mv	a3,s4
    1dd0:	f6fb15e3          	bne	s6,a5,1d3a <__mulsf3+0xdc>
    1dd4:	8536                	mv	a0,a3
    1dd6:	0ff00713          	li	a4,255
    1dda:	4781                	li	a5,0
    1ddc:	bf0d                	j	1d0e <__mulsf3+0xb0>
    1dde:	6341                	lui	t1,0x10
    1de0:	fff30693          	addi	a3,t1,-1 # ffff <_data_lma+0xca2b>
    1de4:	0109d613          	srli	a2,s3,0x10
    1de8:	010ad893          	srli	a7,s5,0x10
    1dec:	00d9f7b3          	and	a5,s3,a3
    1df0:	00dafab3          	and	s5,s5,a3
    1df4:	03578833          	mul	a6,a5,s5
    1df8:	02f889b3          	mul	s3,a7,a5
    1dfc:	01085713          	srli	a4,a6,0x10
    1e00:	03560ab3          	mul	s5,a2,s5
    1e04:	99d6                	add	s3,s3,s5
    1e06:	974e                	add	a4,a4,s3
    1e08:	03160633          	mul	a2,a2,a7
    1e0c:	01577363          	bgeu	a4,s5,1e12 <__mulsf3+0x1b4>
    1e10:	961a                	add	a2,a2,t1
    1e12:	67c1                	lui	a5,0x10
    1e14:	17fd                	addi	a5,a5,-1
    1e16:	00f776b3          	and	a3,a4,a5
    1e1a:	00f87833          	and	a6,a6,a5
    1e1e:	06c2                	slli	a3,a3,0x10
    1e20:	96c2                	add	a3,a3,a6
    1e22:	00669993          	slli	s3,a3,0x6
    1e26:	01075793          	srli	a5,a4,0x10
    1e2a:	013039b3          	snez	s3,s3
    1e2e:	82e9                	srli	a3,a3,0x1a
    1e30:	97b2                	add	a5,a5,a2
    1e32:	079a                	slli	a5,a5,0x6
    1e34:	00d9e6b3          	or	a3,s3,a3
    1e38:	00d7e9b3          	or	s3,a5,a3
    1e3c:	00499793          	slli	a5,s3,0x4
    1e40:	0007d963          	bgez	a5,1e52 <__mulsf3+0x1f4>
    1e44:	0019d713          	srli	a4,s3,0x1
    1e48:	0019f793          	andi	a5,s3,1
    1e4c:	00f769b3          	or	s3,a4,a5
    1e50:	842e                	mv	s0,a1
    1e52:	07f40713          	addi	a4,s0,127
    1e56:	04e05063          	blez	a4,1e96 <__mulsf3+0x238>
    1e5a:	0079f793          	andi	a5,s3,7
    1e5e:	c799                	beqz	a5,1e6c <__mulsf3+0x20e>
    1e60:	00f9f793          	andi	a5,s3,15
    1e64:	4691                	li	a3,4
    1e66:	00d78363          	beq	a5,a3,1e6c <__mulsf3+0x20e>
    1e6a:	0991                	addi	s3,s3,4
    1e6c:	00499793          	slli	a5,s3,0x4
    1e70:	0007d963          	bgez	a5,1e82 <__mulsf3+0x224>
    1e74:	f80007b7          	lui	a5,0xf8000
    1e78:	17fd                	addi	a5,a5,-1
    1e7a:	00f9f9b3          	and	s3,s3,a5
    1e7e:	08040713          	addi	a4,s0,128
    1e82:	0fe00793          	li	a5,254
    1e86:	04e7cb63          	blt	a5,a4,1edc <__mulsf3+0x27e>
    1e8a:	00699793          	slli	a5,s3,0x6
    1e8e:	83a5                	srli	a5,a5,0x9
    1e90:	0ff77713          	andi	a4,a4,255
    1e94:	bdad                	j	1d0e <__mulsf3+0xb0>
    1e96:	4785                	li	a5,1
    1e98:	40e786b3          	sub	a3,a5,a4
    1e9c:	c711                	beqz	a4,1ea8 <__mulsf3+0x24a>
    1e9e:	466d                	li	a2,27
    1ea0:	4701                	li	a4,0
    1ea2:	4781                	li	a5,0
    1ea4:	e6d645e3          	blt	a2,a3,1d0e <__mulsf3+0xb0>
    1ea8:	09e40713          	addi	a4,s0,158
    1eac:	00e99733          	sll	a4,s3,a4
    1eb0:	00e03733          	snez	a4,a4
    1eb4:	00d9d7b3          	srl	a5,s3,a3
    1eb8:	8fd9                	or	a5,a5,a4
    1eba:	0077f713          	andi	a4,a5,7
    1ebe:	c719                	beqz	a4,1ecc <__mulsf3+0x26e>
    1ec0:	00f7f713          	andi	a4,a5,15
    1ec4:	4691                	li	a3,4
    1ec6:	00d70363          	beq	a4,a3,1ecc <__mulsf3+0x26e>
    1eca:	0791                	addi	a5,a5,4
    1ecc:	00579713          	slli	a4,a5,0x5
    1ed0:	00074c63          	bltz	a4,1ee8 <__mulsf3+0x28a>
    1ed4:	079a                	slli	a5,a5,0x6
    1ed6:	83a5                	srli	a5,a5,0x9
    1ed8:	4701                	li	a4,0
    1eda:	bd15                	j	1d0e <__mulsf3+0xb0>
    1edc:	0ff00713          	li	a4,255
    1ee0:	4781                	li	a5,0
    1ee2:	b535                	j	1d0e <__mulsf3+0xb0>
    1ee4:	842e                	mv	s0,a1
    1ee6:	b7b5                	j	1e52 <__mulsf3+0x1f4>
    1ee8:	4705                	li	a4,1
    1eea:	4781                	li	a5,0
    1eec:	b50d                	j	1d0e <__mulsf3+0xb0>

00001eee <__floatsisf>:
    1eee:	1141                	addi	sp,sp,-16
    1ef0:	c606                	sw	ra,12(sp)
    1ef2:	c422                	sw	s0,8(sp)
    1ef4:	c226                	sw	s1,4(sp)
    1ef6:	cd0d                	beqz	a0,1f30 <__floatsisf+0x42>
    1ef8:	41f55793          	srai	a5,a0,0x1f
    1efc:	00a7c433          	xor	s0,a5,a0
    1f00:	8c1d                	sub	s0,s0,a5
    1f02:	84aa                	mv	s1,a0
    1f04:	8522                	mv	a0,s0
    1f06:	2aa9                	jal	2060 <__clzsi2>
    1f08:	09e00793          	li	a5,158
    1f0c:	40a78733          	sub	a4,a5,a0
    1f10:	09600793          	li	a5,150
    1f14:	80fd                	srli	s1,s1,0x1f
    1f16:	02e7cc63          	blt	a5,a4,1f4e <__floatsisf+0x60>
    1f1a:	46a1                	li	a3,8
    1f1c:	0ff77793          	andi	a5,a4,255
    1f20:	00a6d563          	bge	a3,a0,1f2a <__floatsisf+0x3c>
    1f24:	1561                	addi	a0,a0,-8
    1f26:	00a41433          	sll	s0,s0,a0
    1f2a:	0426                	slli	s0,s0,0x9
    1f2c:	8025                	srli	s0,s0,0x9
    1f2e:	a021                	j	1f36 <__floatsisf+0x48>
    1f30:	4481                	li	s1,0
    1f32:	4781                	li	a5,0
    1f34:	4401                	li	s0,0
    1f36:	0426                	slli	s0,s0,0x9
    1f38:	00945513          	srli	a0,s0,0x9
    1f3c:	40b2                	lw	ra,12(sp)
    1f3e:	4422                	lw	s0,8(sp)
    1f40:	07de                	slli	a5,a5,0x17
    1f42:	04fe                	slli	s1,s1,0x1f
    1f44:	8d5d                	or	a0,a0,a5
    1f46:	8d45                	or	a0,a0,s1
    1f48:	4492                	lw	s1,4(sp)
    1f4a:	0141                	addi	sp,sp,16
    1f4c:	8082                	ret
    1f4e:	09900793          	li	a5,153
    1f52:	00e7dd63          	bge	a5,a4,1f6c <__floatsisf+0x7e>
    1f56:	01b50793          	addi	a5,a0,27
    1f5a:	4695                	li	a3,5
    1f5c:	00f417b3          	sll	a5,s0,a5
    1f60:	8e89                	sub	a3,a3,a0
    1f62:	00d45433          	srl	s0,s0,a3
    1f66:	00f037b3          	snez	a5,a5
    1f6a:	8c5d                	or	s0,s0,a5
    1f6c:	4795                	li	a5,5
    1f6e:	00a7d663          	bge	a5,a0,1f7a <__floatsisf+0x8c>
    1f72:	ffb50793          	addi	a5,a0,-5
    1f76:	00f41433          	sll	s0,s0,a5
    1f7a:	fc0006b7          	lui	a3,0xfc000
    1f7e:	16fd                	addi	a3,a3,-1
    1f80:	00747793          	andi	a5,s0,7
    1f84:	00d47633          	and	a2,s0,a3
    1f88:	c385                	beqz	a5,1fa8 <__floatsisf+0xba>
    1f8a:	00f47793          	andi	a5,s0,15
    1f8e:	4591                	li	a1,4
    1f90:	00b78c63          	beq	a5,a1,1fa8 <__floatsisf+0xba>
    1f94:	0611                	addi	a2,a2,4
    1f96:	00561793          	slli	a5,a2,0x5
    1f9a:	0007d763          	bgez	a5,1fa8 <__floatsisf+0xba>
    1f9e:	09f00793          	li	a5,159
    1fa2:	8e75                	and	a2,a2,a3
    1fa4:	40a78733          	sub	a4,a5,a0
    1fa8:	00661413          	slli	s0,a2,0x6
    1fac:	8025                	srli	s0,s0,0x9
    1fae:	0ff77793          	andi	a5,a4,255
    1fb2:	b751                	j	1f36 <__floatsisf+0x48>

00001fb4 <__extendsfdf2>:
    1fb4:	01755713          	srli	a4,a0,0x17
    1fb8:	0ff77713          	andi	a4,a4,255
    1fbc:	1141                	addi	sp,sp,-16
    1fbe:	00170793          	addi	a5,a4,1
    1fc2:	c422                	sw	s0,8(sp)
    1fc4:	c226                	sw	s1,4(sp)
    1fc6:	00951413          	slli	s0,a0,0x9
    1fca:	c606                	sw	ra,12(sp)
    1fcc:	0fe7f793          	andi	a5,a5,254
    1fd0:	8025                	srli	s0,s0,0x9
    1fd2:	01f55493          	srli	s1,a0,0x1f
    1fd6:	c785                	beqz	a5,1ffe <__extendsfdf2+0x4a>
    1fd8:	00345793          	srli	a5,s0,0x3
    1fdc:	38070713          	addi	a4,a4,896
    1fe0:	0476                	slli	s0,s0,0x1d
    1fe2:	07b2                	slli	a5,a5,0xc
    1fe4:	0752                	slli	a4,a4,0x14
    1fe6:	83b1                	srli	a5,a5,0xc
    1fe8:	01f49513          	slli	a0,s1,0x1f
    1fec:	8fd9                	or	a5,a5,a4
    1fee:	8fc9                	or	a5,a5,a0
    1ff0:	40b2                	lw	ra,12(sp)
    1ff2:	8522                	mv	a0,s0
    1ff4:	4422                	lw	s0,8(sp)
    1ff6:	4492                	lw	s1,4(sp)
    1ff8:	85be                	mv	a1,a5
    1ffa:	0141                	addi	sp,sp,16
    1ffc:	8082                	ret
    1ffe:	eb05                	bnez	a4,202e <__extendsfdf2+0x7a>
    2000:	c439                	beqz	s0,204e <__extendsfdf2+0x9a>
    2002:	8522                	mv	a0,s0
    2004:	28b1                	jal	2060 <__clzsi2>
    2006:	47a9                	li	a5,10
    2008:	04a7c663          	blt	a5,a0,2054 <__extendsfdf2+0xa0>
    200c:	472d                	li	a4,11
    200e:	8f09                	sub	a4,a4,a0
    2010:	01550793          	addi	a5,a0,21
    2014:	00e45733          	srl	a4,s0,a4
    2018:	00f41433          	sll	s0,s0,a5
    201c:	00c71793          	slli	a5,a4,0xc
    2020:	38900713          	li	a4,905
    2024:	8f09                	sub	a4,a4,a0
    2026:	83b1                	srli	a5,a5,0xc
    2028:	7ff77713          	andi	a4,a4,2047
    202c:	bf5d                	j	1fe2 <__extendsfdf2+0x2e>
    202e:	cc01                	beqz	s0,2046 <__extendsfdf2+0x92>
    2030:	00345713          	srli	a4,s0,0x3
    2034:	000807b7          	lui	a5,0x80
    2038:	8fd9                	or	a5,a5,a4
    203a:	07b2                	slli	a5,a5,0xc
    203c:	0476                	slli	s0,s0,0x1d
    203e:	83b1                	srli	a5,a5,0xc
    2040:	7ff00713          	li	a4,2047
    2044:	bf79                	j	1fe2 <__extendsfdf2+0x2e>
    2046:	7ff00713          	li	a4,2047
    204a:	4781                	li	a5,0
    204c:	bf59                	j	1fe2 <__extendsfdf2+0x2e>
    204e:	4701                	li	a4,0
    2050:	4781                	li	a5,0
    2052:	bf41                	j	1fe2 <__extendsfdf2+0x2e>
    2054:	ff550713          	addi	a4,a0,-11
    2058:	00e41733          	sll	a4,s0,a4
    205c:	4401                	li	s0,0
    205e:	bf7d                	j	201c <__extendsfdf2+0x68>

00002060 <__clzsi2>:
    2060:	67c1                	lui	a5,0x10
    2062:	02f57c63          	bgeu	a0,a5,209a <__clzsi2+0x3a>
    2066:	0ff00793          	li	a5,255
    206a:	02000713          	li	a4,32
    206e:	00a7eb63          	bltu	a5,a0,2084 <__clzsi2+0x24>
    2072:	00001797          	auipc	a5,0x1
    2076:	3c678793          	addi	a5,a5,966 # 3438 <__clz_tab>
    207a:	97aa                	add	a5,a5,a0
    207c:	2388                	lbu	a0,0(a5)
    207e:	40a70533          	sub	a0,a4,a0
    2082:	8082                	ret
    2084:	8121                	srli	a0,a0,0x8
    2086:	00001797          	auipc	a5,0x1
    208a:	3b278793          	addi	a5,a5,946 # 3438 <__clz_tab>
    208e:	97aa                	add	a5,a5,a0
    2090:	2388                	lbu	a0,0(a5)
    2092:	4761                	li	a4,24
    2094:	40a70533          	sub	a0,a4,a0
    2098:	8082                	ret
    209a:	010007b7          	lui	a5,0x1000
    209e:	00f56d63          	bltu	a0,a5,20b8 <__clzsi2+0x58>
    20a2:	8161                	srli	a0,a0,0x18
    20a4:	00001797          	auipc	a5,0x1
    20a8:	39478793          	addi	a5,a5,916 # 3438 <__clz_tab>
    20ac:	97aa                	add	a5,a5,a0
    20ae:	2388                	lbu	a0,0(a5)
    20b0:	4721                	li	a4,8
    20b2:	40a70533          	sub	a0,a4,a0
    20b6:	8082                	ret
    20b8:	8141                	srli	a0,a0,0x10
    20ba:	00001797          	auipc	a5,0x1
    20be:	37e78793          	addi	a5,a5,894 # 3438 <__clz_tab>
    20c2:	97aa                	add	a5,a5,a0
    20c4:	2388                	lbu	a0,0(a5)
    20c6:	4741                	li	a4,16
    20c8:	40a70533          	sub	a0,a4,a0
    20cc:	8082                	ret

000020ce <iprintf>:
    20ce:	7139                	addi	sp,sp,-64
    20d0:	da3e                	sw	a5,52(sp)
    20d2:	d22e                	sw	a1,36(sp)
    20d4:	d432                	sw	a2,40(sp)
    20d6:	d636                	sw	a3,44(sp)
    20d8:	d83a                	sw	a4,48(sp)
    20da:	dc42                	sw	a6,56(sp)
    20dc:	de46                	sw	a7,60(sp)
    20de:	80c18793          	addi	a5,gp,-2036 # 200000b4 <_impure_ptr>
    20e2:	cc22                	sw	s0,24(sp)
    20e4:	4380                	lw	s0,0(a5)
    20e6:	ca26                	sw	s1,20(sp)
    20e8:	ce06                	sw	ra,28(sp)
    20ea:	84aa                	mv	s1,a0
    20ec:	c409                	beqz	s0,20f6 <iprintf+0x28>
    20ee:	4c1c                	lw	a5,24(s0)
    20f0:	e399                	bnez	a5,20f6 <iprintf+0x28>
    20f2:	8522                	mv	a0,s0
    20f4:	29fd                	jal	25f2 <__sinit>
    20f6:	440c                	lw	a1,8(s0)
    20f8:	1054                	addi	a3,sp,36
    20fa:	8626                	mv	a2,s1
    20fc:	8522                	mv	a0,s0
    20fe:	c636                	sw	a3,12(sp)
    2100:	139000ef          	jal	ra,2a38 <_vfiprintf_r>
    2104:	40f2                	lw	ra,28(sp)
    2106:	4462                	lw	s0,24(sp)
    2108:	44d2                	lw	s1,20(sp)
    210a:	6121                	addi	sp,sp,64
    210c:	8082                	ret

0000210e <_puts_r>:
    210e:	1101                	addi	sp,sp,-32
    2110:	ca26                	sw	s1,20(sp)
    2112:	c84a                	sw	s2,16(sp)
    2114:	ce06                	sw	ra,28(sp)
    2116:	cc22                	sw	s0,24(sp)
    2118:	c64e                	sw	s3,12(sp)
    211a:	c452                	sw	s4,8(sp)
    211c:	84aa                	mv	s1,a0
    211e:	892e                	mv	s2,a1
    2120:	c501                	beqz	a0,2128 <_puts_r+0x1a>
    2122:	4d1c                	lw	a5,24(a0)
    2124:	e391                	bnez	a5,2128 <_puts_r+0x1a>
    2126:	21f1                	jal	25f2 <__sinit>
    2128:	4c9c                	lw	a5,24(s1)
    212a:	4480                	lw	s0,8(s1)
    212c:	e399                	bnez	a5,2132 <_puts_r+0x24>
    212e:	8526                	mv	a0,s1
    2130:	21c9                	jal	25f2 <__sinit>
    2132:	00001797          	auipc	a5,0x1
    2136:	42678793          	addi	a5,a5,1062 # 3558 <__sf_fake_stdin>
    213a:	02f41b63          	bne	s0,a5,2170 <_puts_r+0x62>
    213e:	40c0                	lw	s0,4(s1)
    2140:	245e                	lhu	a5,12(s0)
    2142:	8ba1                	andi	a5,a5,8
    2144:	c7b1                	beqz	a5,2190 <_puts_r+0x82>
    2146:	481c                	lw	a5,16(s0)
    2148:	c7a1                	beqz	a5,2190 <_puts_r+0x82>
    214a:	59fd                	li	s3,-1
    214c:	4a29                	li	s4,10
    214e:	441c                	lw	a5,8(s0)
    2150:	00094583          	lbu	a1,0(s2)
    2154:	17fd                	addi	a5,a5,-1
    2156:	e9b1                	bnez	a1,21aa <_puts_r+0x9c>
    2158:	c41c                	sw	a5,8(s0)
    215a:	0607dd63          	bgez	a5,21d4 <_puts_r+0xc6>
    215e:	8622                	mv	a2,s0
    2160:	45a9                	li	a1,10
    2162:	8526                	mv	a0,s1
    2164:	2069                	jal	21ee <__swbuf_r>
    2166:	57fd                	li	a5,-1
    2168:	02f50863          	beq	a0,a5,2198 <_puts_r+0x8a>
    216c:	4529                	li	a0,10
    216e:	a035                	j	219a <_puts_r+0x8c>
    2170:	00001797          	auipc	a5,0x1
    2174:	40878793          	addi	a5,a5,1032 # 3578 <__sf_fake_stdout>
    2178:	00f41463          	bne	s0,a5,2180 <_puts_r+0x72>
    217c:	4480                	lw	s0,8(s1)
    217e:	b7c9                	j	2140 <_puts_r+0x32>
    2180:	00001797          	auipc	a5,0x1
    2184:	3b878793          	addi	a5,a5,952 # 3538 <__sf_fake_stderr>
    2188:	faf41ce3          	bne	s0,a5,2140 <_puts_r+0x32>
    218c:	44c0                	lw	s0,12(s1)
    218e:	bf4d                	j	2140 <_puts_r+0x32>
    2190:	85a2                	mv	a1,s0
    2192:	8526                	mv	a0,s1
    2194:	2a19                	jal	22aa <__swsetup_r>
    2196:	d955                	beqz	a0,214a <_puts_r+0x3c>
    2198:	557d                	li	a0,-1
    219a:	40f2                	lw	ra,28(sp)
    219c:	4462                	lw	s0,24(sp)
    219e:	44d2                	lw	s1,20(sp)
    21a0:	4942                	lw	s2,16(sp)
    21a2:	49b2                	lw	s3,12(sp)
    21a4:	4a22                	lw	s4,8(sp)
    21a6:	6105                	addi	sp,sp,32
    21a8:	8082                	ret
    21aa:	c41c                	sw	a5,8(s0)
    21ac:	0905                	addi	s2,s2,1
    21ae:	0007d763          	bgez	a5,21bc <_puts_r+0xae>
    21b2:	4c18                	lw	a4,24(s0)
    21b4:	00e7ca63          	blt	a5,a4,21c8 <_puts_r+0xba>
    21b8:	01458863          	beq	a1,s4,21c8 <_puts_r+0xba>
    21bc:	401c                	lw	a5,0(s0)
    21be:	00178713          	addi	a4,a5,1
    21c2:	c018                	sw	a4,0(s0)
    21c4:	a38c                	sb	a1,0(a5)
    21c6:	b761                	j	214e <_puts_r+0x40>
    21c8:	8622                	mv	a2,s0
    21ca:	8526                	mv	a0,s1
    21cc:	200d                	jal	21ee <__swbuf_r>
    21ce:	f93510e3          	bne	a0,s3,214e <_puts_r+0x40>
    21d2:	b7d9                	j	2198 <_puts_r+0x8a>
    21d4:	401c                	lw	a5,0(s0)
    21d6:	00178713          	addi	a4,a5,1
    21da:	c018                	sw	a4,0(s0)
    21dc:	4729                	li	a4,10
    21de:	a398                	sb	a4,0(a5)
    21e0:	b771                	j	216c <_puts_r+0x5e>

000021e2 <puts>:
    21e2:	80c18793          	addi	a5,gp,-2036 # 200000b4 <_impure_ptr>
    21e6:	85aa                	mv	a1,a0
    21e8:	4388                	lw	a0,0(a5)
    21ea:	f25ff06f          	j	210e <_puts_r>

000021ee <__swbuf_r>:
    21ee:	1101                	addi	sp,sp,-32
    21f0:	cc22                	sw	s0,24(sp)
    21f2:	ca26                	sw	s1,20(sp)
    21f4:	c84a                	sw	s2,16(sp)
    21f6:	ce06                	sw	ra,28(sp)
    21f8:	c64e                	sw	s3,12(sp)
    21fa:	84aa                	mv	s1,a0
    21fc:	892e                	mv	s2,a1
    21fe:	8432                	mv	s0,a2
    2200:	c501                	beqz	a0,2208 <__swbuf_r+0x1a>
    2202:	4d1c                	lw	a5,24(a0)
    2204:	e391                	bnez	a5,2208 <__swbuf_r+0x1a>
    2206:	26f5                	jal	25f2 <__sinit>
    2208:	00001797          	auipc	a5,0x1
    220c:	35078793          	addi	a5,a5,848 # 3558 <__sf_fake_stdin>
    2210:	06f41763          	bne	s0,a5,227e <__swbuf_r+0x90>
    2214:	40c0                	lw	s0,4(s1)
    2216:	4c1c                	lw	a5,24(s0)
    2218:	c41c                	sw	a5,8(s0)
    221a:	245e                	lhu	a5,12(s0)
    221c:	8ba1                	andi	a5,a5,8
    221e:	c3c1                	beqz	a5,229e <__swbuf_r+0xb0>
    2220:	481c                	lw	a5,16(s0)
    2222:	cfb5                	beqz	a5,229e <__swbuf_r+0xb0>
    2224:	481c                	lw	a5,16(s0)
    2226:	4008                	lw	a0,0(s0)
    2228:	0ff97993          	andi	s3,s2,255
    222c:	0ff97913          	andi	s2,s2,255
    2230:	8d1d                	sub	a0,a0,a5
    2232:	485c                	lw	a5,20(s0)
    2234:	00f54663          	blt	a0,a5,2240 <__swbuf_r+0x52>
    2238:	85a2                	mv	a1,s0
    223a:	8526                	mv	a0,s1
    223c:	2c69                	jal	24d6 <_fflush_r>
    223e:	e525                	bnez	a0,22a6 <__swbuf_r+0xb8>
    2240:	441c                	lw	a5,8(s0)
    2242:	0505                	addi	a0,a0,1
    2244:	17fd                	addi	a5,a5,-1
    2246:	c41c                	sw	a5,8(s0)
    2248:	401c                	lw	a5,0(s0)
    224a:	00178713          	addi	a4,a5,1
    224e:	c018                	sw	a4,0(s0)
    2250:	01378023          	sb	s3,0(a5)
    2254:	485c                	lw	a5,20(s0)
    2256:	00a78863          	beq	a5,a0,2266 <__swbuf_r+0x78>
    225a:	245e                	lhu	a5,12(s0)
    225c:	8b85                	andi	a5,a5,1
    225e:	cb81                	beqz	a5,226e <__swbuf_r+0x80>
    2260:	47a9                	li	a5,10
    2262:	00f91663          	bne	s2,a5,226e <__swbuf_r+0x80>
    2266:	85a2                	mv	a1,s0
    2268:	8526                	mv	a0,s1
    226a:	24b5                	jal	24d6 <_fflush_r>
    226c:	ed0d                	bnez	a0,22a6 <__swbuf_r+0xb8>
    226e:	40f2                	lw	ra,28(sp)
    2270:	4462                	lw	s0,24(sp)
    2272:	854a                	mv	a0,s2
    2274:	44d2                	lw	s1,20(sp)
    2276:	4942                	lw	s2,16(sp)
    2278:	49b2                	lw	s3,12(sp)
    227a:	6105                	addi	sp,sp,32
    227c:	8082                	ret
    227e:	00001797          	auipc	a5,0x1
    2282:	2fa78793          	addi	a5,a5,762 # 3578 <__sf_fake_stdout>
    2286:	00f41463          	bne	s0,a5,228e <__swbuf_r+0xa0>
    228a:	4480                	lw	s0,8(s1)
    228c:	b769                	j	2216 <__swbuf_r+0x28>
    228e:	00001797          	auipc	a5,0x1
    2292:	2aa78793          	addi	a5,a5,682 # 3538 <__sf_fake_stderr>
    2296:	f8f410e3          	bne	s0,a5,2216 <__swbuf_r+0x28>
    229a:	44c0                	lw	s0,12(s1)
    229c:	bfad                	j	2216 <__swbuf_r+0x28>
    229e:	85a2                	mv	a1,s0
    22a0:	8526                	mv	a0,s1
    22a2:	2021                	jal	22aa <__swsetup_r>
    22a4:	d141                	beqz	a0,2224 <__swbuf_r+0x36>
    22a6:	597d                	li	s2,-1
    22a8:	b7d9                	j	226e <__swbuf_r+0x80>

000022aa <__swsetup_r>:
    22aa:	1141                	addi	sp,sp,-16
    22ac:	80c18793          	addi	a5,gp,-2036 # 200000b4 <_impure_ptr>
    22b0:	c226                	sw	s1,4(sp)
    22b2:	4384                	lw	s1,0(a5)
    22b4:	c422                	sw	s0,8(sp)
    22b6:	c04a                	sw	s2,0(sp)
    22b8:	c606                	sw	ra,12(sp)
    22ba:	892a                	mv	s2,a0
    22bc:	842e                	mv	s0,a1
    22be:	c489                	beqz	s1,22c8 <__swsetup_r+0x1e>
    22c0:	4c9c                	lw	a5,24(s1)
    22c2:	e399                	bnez	a5,22c8 <__swsetup_r+0x1e>
    22c4:	8526                	mv	a0,s1
    22c6:	2635                	jal	25f2 <__sinit>
    22c8:	00001797          	auipc	a5,0x1
    22cc:	29078793          	addi	a5,a5,656 # 3558 <__sf_fake_stdin>
    22d0:	02f41b63          	bne	s0,a5,2306 <__swsetup_r+0x5c>
    22d4:	40c0                	lw	s0,4(s1)
    22d6:	00c41703          	lh	a4,12(s0)
    22da:	01071793          	slli	a5,a4,0x10
    22de:	83c1                	srli	a5,a5,0x10
    22e0:	0087f693          	andi	a3,a5,8
    22e4:	eaad                	bnez	a3,2356 <__swsetup_r+0xac>
    22e6:	0107f693          	andi	a3,a5,16
    22ea:	ee95                	bnez	a3,2326 <__swsetup_r+0x7c>
    22ec:	47a5                	li	a5,9
    22ee:	00f92023          	sw	a5,0(s2)
    22f2:	04076713          	ori	a4,a4,64
    22f6:	a45a                	sh	a4,12(s0)
    22f8:	557d                	li	a0,-1
    22fa:	40b2                	lw	ra,12(sp)
    22fc:	4422                	lw	s0,8(sp)
    22fe:	4492                	lw	s1,4(sp)
    2300:	4902                	lw	s2,0(sp)
    2302:	0141                	addi	sp,sp,16
    2304:	8082                	ret
    2306:	00001797          	auipc	a5,0x1
    230a:	27278793          	addi	a5,a5,626 # 3578 <__sf_fake_stdout>
    230e:	00f41463          	bne	s0,a5,2316 <__swsetup_r+0x6c>
    2312:	4480                	lw	s0,8(s1)
    2314:	b7c9                	j	22d6 <__swsetup_r+0x2c>
    2316:	00001797          	auipc	a5,0x1
    231a:	22278793          	addi	a5,a5,546 # 3538 <__sf_fake_stderr>
    231e:	faf41ce3          	bne	s0,a5,22d6 <__swsetup_r+0x2c>
    2322:	44c0                	lw	s0,12(s1)
    2324:	bf4d                	j	22d6 <__swsetup_r+0x2c>
    2326:	8b91                	andi	a5,a5,4
    2328:	c39d                	beqz	a5,234e <__swsetup_r+0xa4>
    232a:	584c                	lw	a1,52(s0)
    232c:	c989                	beqz	a1,233e <__swsetup_r+0x94>
    232e:	04440793          	addi	a5,s0,68
    2332:	00f58463          	beq	a1,a5,233a <__swsetup_r+0x90>
    2336:	854a                	mv	a0,s2
    2338:	2b29                	jal	2852 <_free_r>
    233a:	02042a23          	sw	zero,52(s0)
    233e:	245e                	lhu	a5,12(s0)
    2340:	00042223          	sw	zero,4(s0)
    2344:	fdb7f793          	andi	a5,a5,-37
    2348:	a45e                	sh	a5,12(s0)
    234a:	481c                	lw	a5,16(s0)
    234c:	c01c                	sw	a5,0(s0)
    234e:	245e                	lhu	a5,12(s0)
    2350:	0087e793          	ori	a5,a5,8
    2354:	a45e                	sh	a5,12(s0)
    2356:	481c                	lw	a5,16(s0)
    2358:	eb99                	bnez	a5,236e <__swsetup_r+0xc4>
    235a:	245e                	lhu	a5,12(s0)
    235c:	20000713          	li	a4,512
    2360:	2807f793          	andi	a5,a5,640
    2364:	00e78563          	beq	a5,a4,236e <__swsetup_r+0xc4>
    2368:	85a2                	mv	a1,s0
    236a:	854a                	mv	a0,s2
    236c:	2991                	jal	27c0 <__smakebuf_r>
    236e:	245e                	lhu	a5,12(s0)
    2370:	0017f713          	andi	a4,a5,1
    2374:	c31d                	beqz	a4,239a <__swsetup_r+0xf0>
    2376:	485c                	lw	a5,20(s0)
    2378:	00042423          	sw	zero,8(s0)
    237c:	40f007b3          	neg	a5,a5
    2380:	cc1c                	sw	a5,24(s0)
    2382:	481c                	lw	a5,16(s0)
    2384:	4501                	li	a0,0
    2386:	fbb5                	bnez	a5,22fa <__swsetup_r+0x50>
    2388:	00c41783          	lh	a5,12(s0)
    238c:	0807f713          	andi	a4,a5,128
    2390:	d72d                	beqz	a4,22fa <__swsetup_r+0x50>
    2392:	0407e793          	ori	a5,a5,64
    2396:	a45e                	sh	a5,12(s0)
    2398:	b785                	j	22f8 <__swsetup_r+0x4e>
    239a:	8b89                	andi	a5,a5,2
    239c:	4701                	li	a4,0
    239e:	e391                	bnez	a5,23a2 <__swsetup_r+0xf8>
    23a0:	4858                	lw	a4,20(s0)
    23a2:	c418                	sw	a4,8(s0)
    23a4:	bff9                	j	2382 <__swsetup_r+0xd8>

000023a6 <__sflush_r>:
    23a6:	25de                	lhu	a5,12(a1)
    23a8:	1101                	addi	sp,sp,-32
    23aa:	cc22                	sw	s0,24(sp)
    23ac:	ca26                	sw	s1,20(sp)
    23ae:	ce06                	sw	ra,28(sp)
    23b0:	c84a                	sw	s2,16(sp)
    23b2:	c64e                	sw	s3,12(sp)
    23b4:	0087f713          	andi	a4,a5,8
    23b8:	84aa                	mv	s1,a0
    23ba:	842e                	mv	s0,a1
    23bc:	eb79                	bnez	a4,2492 <__sflush_r+0xec>
    23be:	41d8                	lw	a4,4(a1)
    23c0:	00e04d63          	bgtz	a4,23da <__sflush_r+0x34>
    23c4:	41b8                	lw	a4,64(a1)
    23c6:	00e04a63          	bgtz	a4,23da <__sflush_r+0x34>
    23ca:	4501                	li	a0,0
    23cc:	40f2                	lw	ra,28(sp)
    23ce:	4462                	lw	s0,24(sp)
    23d0:	44d2                	lw	s1,20(sp)
    23d2:	4942                	lw	s2,16(sp)
    23d4:	49b2                	lw	s3,12(sp)
    23d6:	6105                	addi	sp,sp,32
    23d8:	8082                	ret
    23da:	5458                	lw	a4,44(s0)
    23dc:	d77d                	beqz	a4,23ca <__sflush_r+0x24>
    23de:	0004a903          	lw	s2,0(s1)
    23e2:	01379693          	slli	a3,a5,0x13
    23e6:	0004a023          	sw	zero,0(s1)
    23ea:	0606db63          	bgez	a3,2460 <__sflush_r+0xba>
    23ee:	4870                	lw	a2,84(s0)
    23f0:	245e                	lhu	a5,12(s0)
    23f2:	8b91                	andi	a5,a5,4
    23f4:	c799                	beqz	a5,2402 <__sflush_r+0x5c>
    23f6:	405c                	lw	a5,4(s0)
    23f8:	8e1d                	sub	a2,a2,a5
    23fa:	585c                	lw	a5,52(s0)
    23fc:	c399                	beqz	a5,2402 <__sflush_r+0x5c>
    23fe:	403c                	lw	a5,64(s0)
    2400:	8e1d                	sub	a2,a2,a5
    2402:	545c                	lw	a5,44(s0)
    2404:	500c                	lw	a1,32(s0)
    2406:	4681                	li	a3,0
    2408:	8526                	mv	a0,s1
    240a:	9782                	jalr	a5
    240c:	57fd                	li	a5,-1
    240e:	245a                	lhu	a4,12(s0)
    2410:	00f51d63          	bne	a0,a5,242a <__sflush_r+0x84>
    2414:	4094                	lw	a3,0(s1)
    2416:	47f5                	li	a5,29
    2418:	06d7e863          	bltu	a5,a3,2488 <__sflush_r+0xe2>
    241c:	204007b7          	lui	a5,0x20400
    2420:	0785                	addi	a5,a5,1
    2422:	00d7d7b3          	srl	a5,a5,a3
    2426:	8b85                	andi	a5,a5,1
    2428:	c3a5                	beqz	a5,2488 <__sflush_r+0xe2>
    242a:	481c                	lw	a5,16(s0)
    242c:	00042223          	sw	zero,4(s0)
    2430:	c01c                	sw	a5,0(s0)
    2432:	01371793          	slli	a5,a4,0x13
    2436:	0007d863          	bgez	a5,2446 <__sflush_r+0xa0>
    243a:	57fd                	li	a5,-1
    243c:	00f51463          	bne	a0,a5,2444 <__sflush_r+0x9e>
    2440:	409c                	lw	a5,0(s1)
    2442:	e391                	bnez	a5,2446 <__sflush_r+0xa0>
    2444:	c868                	sw	a0,84(s0)
    2446:	584c                	lw	a1,52(s0)
    2448:	0124a023          	sw	s2,0(s1)
    244c:	ddbd                	beqz	a1,23ca <__sflush_r+0x24>
    244e:	04440793          	addi	a5,s0,68
    2452:	00f58463          	beq	a1,a5,245a <__sflush_r+0xb4>
    2456:	8526                	mv	a0,s1
    2458:	2eed                	jal	2852 <_free_r>
    245a:	02042a23          	sw	zero,52(s0)
    245e:	b7b5                	j	23ca <__sflush_r+0x24>
    2460:	500c                	lw	a1,32(s0)
    2462:	4601                	li	a2,0
    2464:	4685                	li	a3,1
    2466:	8526                	mv	a0,s1
    2468:	9702                	jalr	a4
    246a:	57fd                	li	a5,-1
    246c:	862a                	mv	a2,a0
    246e:	f8f511e3          	bne	a0,a5,23f0 <__sflush_r+0x4a>
    2472:	409c                	lw	a5,0(s1)
    2474:	dfb5                	beqz	a5,23f0 <__sflush_r+0x4a>
    2476:	4775                	li	a4,29
    2478:	00e78563          	beq	a5,a4,2482 <__sflush_r+0xdc>
    247c:	4759                	li	a4,22
    247e:	04e79363          	bne	a5,a4,24c4 <__sflush_r+0x11e>
    2482:	0124a023          	sw	s2,0(s1)
    2486:	b791                	j	23ca <__sflush_r+0x24>
    2488:	04076713          	ori	a4,a4,64
    248c:	a45a                	sh	a4,12(s0)
    248e:	557d                	li	a0,-1
    2490:	bf35                	j	23cc <__sflush_r+0x26>
    2492:	0105a983          	lw	s3,16(a1)
    2496:	f2098ae3          	beqz	s3,23ca <__sflush_r+0x24>
    249a:	0005a903          	lw	s2,0(a1)
    249e:	8b8d                	andi	a5,a5,3
    24a0:	0135a023          	sw	s3,0(a1)
    24a4:	41390933          	sub	s2,s2,s3
    24a8:	4701                	li	a4,0
    24aa:	e391                	bnez	a5,24ae <__sflush_r+0x108>
    24ac:	49d8                	lw	a4,20(a1)
    24ae:	c418                	sw	a4,8(s0)
    24b0:	f1205de3          	blez	s2,23ca <__sflush_r+0x24>
    24b4:	541c                	lw	a5,40(s0)
    24b6:	500c                	lw	a1,32(s0)
    24b8:	86ca                	mv	a3,s2
    24ba:	864e                	mv	a2,s3
    24bc:	8526                	mv	a0,s1
    24be:	9782                	jalr	a5
    24c0:	00a04763          	bgtz	a0,24ce <__sflush_r+0x128>
    24c4:	245e                	lhu	a5,12(s0)
    24c6:	0407e793          	ori	a5,a5,64
    24ca:	a45e                	sh	a5,12(s0)
    24cc:	b7c9                	j	248e <__sflush_r+0xe8>
    24ce:	99aa                	add	s3,s3,a0
    24d0:	40a90933          	sub	s2,s2,a0
    24d4:	bff1                	j	24b0 <__sflush_r+0x10a>

000024d6 <_fflush_r>:
    24d6:	499c                	lw	a5,16(a1)
    24d8:	c3a5                	beqz	a5,2538 <_fflush_r+0x62>
    24da:	1101                	addi	sp,sp,-32
    24dc:	cc22                	sw	s0,24(sp)
    24de:	ce06                	sw	ra,28(sp)
    24e0:	842a                	mv	s0,a0
    24e2:	c511                	beqz	a0,24ee <_fflush_r+0x18>
    24e4:	4d1c                	lw	a5,24(a0)
    24e6:	e781                	bnez	a5,24ee <_fflush_r+0x18>
    24e8:	c62e                	sw	a1,12(sp)
    24ea:	2221                	jal	25f2 <__sinit>
    24ec:	45b2                	lw	a1,12(sp)
    24ee:	00001797          	auipc	a5,0x1
    24f2:	06a78793          	addi	a5,a5,106 # 3558 <__sf_fake_stdin>
    24f6:	00f59c63          	bne	a1,a5,250e <_fflush_r+0x38>
    24fa:	404c                	lw	a1,4(s0)
    24fc:	00c59783          	lh	a5,12(a1)
    2500:	c79d                	beqz	a5,252e <_fflush_r+0x58>
    2502:	8522                	mv	a0,s0
    2504:	4462                	lw	s0,24(sp)
    2506:	40f2                	lw	ra,28(sp)
    2508:	6105                	addi	sp,sp,32
    250a:	e9dff06f          	j	23a6 <__sflush_r>
    250e:	00001797          	auipc	a5,0x1
    2512:	06a78793          	addi	a5,a5,106 # 3578 <__sf_fake_stdout>
    2516:	00f59463          	bne	a1,a5,251e <_fflush_r+0x48>
    251a:	440c                	lw	a1,8(s0)
    251c:	b7c5                	j	24fc <_fflush_r+0x26>
    251e:	00001797          	auipc	a5,0x1
    2522:	01a78793          	addi	a5,a5,26 # 3538 <__sf_fake_stderr>
    2526:	fcf59be3          	bne	a1,a5,24fc <_fflush_r+0x26>
    252a:	444c                	lw	a1,12(s0)
    252c:	bfc1                	j	24fc <_fflush_r+0x26>
    252e:	40f2                	lw	ra,28(sp)
    2530:	4462                	lw	s0,24(sp)
    2532:	4501                	li	a0,0
    2534:	6105                	addi	sp,sp,32
    2536:	8082                	ret
    2538:	4501                	li	a0,0
    253a:	8082                	ret

0000253c <std>:
    253c:	1141                	addi	sp,sp,-16
    253e:	c422                	sw	s0,8(sp)
    2540:	c606                	sw	ra,12(sp)
    2542:	842a                	mv	s0,a0
    2544:	a54e                	sh	a1,12(a0)
    2546:	a572                	sh	a2,14(a0)
    2548:	00052023          	sw	zero,0(a0)
    254c:	00052223          	sw	zero,4(a0)
    2550:	00052423          	sw	zero,8(a0)
    2554:	06052223          	sw	zero,100(a0)
    2558:	00052823          	sw	zero,16(a0)
    255c:	00052a23          	sw	zero,20(a0)
    2560:	00052c23          	sw	zero,24(a0)
    2564:	4621                	li	a2,8
    2566:	4581                	li	a1,0
    2568:	05c50513          	addi	a0,a0,92
    256c:	cb5fd0ef          	jal	ra,220 <memset>
    2570:	00001797          	auipc	a5,0x1
    2574:	b2878793          	addi	a5,a5,-1240 # 3098 <__sread>
    2578:	d05c                	sw	a5,36(s0)
    257a:	00001797          	auipc	a5,0x1
    257e:	b4a78793          	addi	a5,a5,-1206 # 30c4 <__swrite>
    2582:	d41c                	sw	a5,40(s0)
    2584:	00001797          	auipc	a5,0x1
    2588:	b8878793          	addi	a5,a5,-1144 # 310c <__sseek>
    258c:	d45c                	sw	a5,44(s0)
    258e:	00001797          	auipc	a5,0x1
    2592:	bae78793          	addi	a5,a5,-1106 # 313c <__sclose>
    2596:	d000                	sw	s0,32(s0)
    2598:	d81c                	sw	a5,48(s0)
    259a:	40b2                	lw	ra,12(sp)
    259c:	4422                	lw	s0,8(sp)
    259e:	0141                	addi	sp,sp,16
    25a0:	8082                	ret

000025a2 <_cleanup_r>:
    25a2:	00000597          	auipc	a1,0x0
    25a6:	f3458593          	addi	a1,a1,-204 # 24d6 <_fflush_r>
    25aa:	aa91                	j	26fe <_fwalk_reent>

000025ac <__sfmoreglue>:
    25ac:	1141                	addi	sp,sp,-16
    25ae:	c226                	sw	s1,4(sp)
    25b0:	06800613          	li	a2,104
    25b4:	fff58493          	addi	s1,a1,-1
    25b8:	02c484b3          	mul	s1,s1,a2
    25bc:	c04a                	sw	s2,0(sp)
    25be:	892e                	mv	s2,a1
    25c0:	c422                	sw	s0,8(sp)
    25c2:	c606                	sw	ra,12(sp)
    25c4:	07448593          	addi	a1,s1,116
    25c8:	2e0d                	jal	28fa <_malloc_r>
    25ca:	842a                	mv	s0,a0
    25cc:	cd01                	beqz	a0,25e4 <__sfmoreglue+0x38>
    25ce:	00052023          	sw	zero,0(a0)
    25d2:	01252223          	sw	s2,4(a0)
    25d6:	0531                	addi	a0,a0,12
    25d8:	c408                	sw	a0,8(s0)
    25da:	06848613          	addi	a2,s1,104
    25de:	4581                	li	a1,0
    25e0:	c41fd0ef          	jal	ra,220 <memset>
    25e4:	8522                	mv	a0,s0
    25e6:	40b2                	lw	ra,12(sp)
    25e8:	4422                	lw	s0,8(sp)
    25ea:	4492                	lw	s1,4(sp)
    25ec:	4902                	lw	s2,0(sp)
    25ee:	0141                	addi	sp,sp,16
    25f0:	8082                	ret

000025f2 <__sinit>:
    25f2:	4d1c                	lw	a5,24(a0)
    25f4:	e7a5                	bnez	a5,265c <__sinit+0x6a>
    25f6:	1141                	addi	sp,sp,-16
    25f8:	c606                	sw	ra,12(sp)
    25fa:	c422                	sw	s0,8(sp)
    25fc:	00000797          	auipc	a5,0x0
    2600:	fa678793          	addi	a5,a5,-90 # 25a2 <_cleanup_r>
    2604:	d51c                	sw	a5,40(a0)
    2606:	81018793          	addi	a5,gp,-2032 # 200000b8 <_global_impure_ptr>
    260a:	439c                	lw	a5,0(a5)
    260c:	04052423          	sw	zero,72(a0)
    2610:	04052623          	sw	zero,76(a0)
    2614:	04052823          	sw	zero,80(a0)
    2618:	00f51463          	bne	a0,a5,2620 <__sinit+0x2e>
    261c:	4785                	li	a5,1
    261e:	cd1c                	sw	a5,24(a0)
    2620:	842a                	mv	s0,a0
    2622:	2835                	jal	265e <__sfp>
    2624:	c048                	sw	a0,4(s0)
    2626:	8522                	mv	a0,s0
    2628:	281d                	jal	265e <__sfp>
    262a:	c408                	sw	a0,8(s0)
    262c:	8522                	mv	a0,s0
    262e:	2805                	jal	265e <__sfp>
    2630:	c448                	sw	a0,12(s0)
    2632:	4048                	lw	a0,4(s0)
    2634:	4601                	li	a2,0
    2636:	4591                	li	a1,4
    2638:	f05ff0ef          	jal	ra,253c <std>
    263c:	4408                	lw	a0,8(s0)
    263e:	4605                	li	a2,1
    2640:	45a5                	li	a1,9
    2642:	efbff0ef          	jal	ra,253c <std>
    2646:	4448                	lw	a0,12(s0)
    2648:	4609                	li	a2,2
    264a:	45c9                	li	a1,18
    264c:	ef1ff0ef          	jal	ra,253c <std>
    2650:	4785                	li	a5,1
    2652:	cc1c                	sw	a5,24(s0)
    2654:	40b2                	lw	ra,12(sp)
    2656:	4422                	lw	s0,8(sp)
    2658:	0141                	addi	sp,sp,16
    265a:	8082                	ret
    265c:	8082                	ret

0000265e <__sfp>:
    265e:	1141                	addi	sp,sp,-16
    2660:	81018793          	addi	a5,gp,-2032 # 200000b8 <_global_impure_ptr>
    2664:	c226                	sw	s1,4(sp)
    2666:	4384                	lw	s1,0(a5)
    2668:	c04a                	sw	s2,0(sp)
    266a:	c606                	sw	ra,12(sp)
    266c:	4c9c                	lw	a5,24(s1)
    266e:	c422                	sw	s0,8(sp)
    2670:	892a                	mv	s2,a0
    2672:	e781                	bnez	a5,267a <__sfp+0x1c>
    2674:	8526                	mv	a0,s1
    2676:	f7dff0ef          	jal	ra,25f2 <__sinit>
    267a:	04848493          	addi	s1,s1,72
    267e:	4480                	lw	s0,8(s1)
    2680:	40dc                	lw	a5,4(s1)
    2682:	17fd                	addi	a5,a5,-1
    2684:	0007d663          	bgez	a5,2690 <__sfp+0x32>
    2688:	409c                	lw	a5,0(s1)
    268a:	cfb9                	beqz	a5,26e8 <__sfp+0x8a>
    268c:	4084                	lw	s1,0(s1)
    268e:	bfc5                	j	267e <__sfp+0x20>
    2690:	00c41703          	lh	a4,12(s0)
    2694:	e739                	bnez	a4,26e2 <__sfp+0x84>
    2696:	77c1                	lui	a5,0xffff0
    2698:	0785                	addi	a5,a5,1
    269a:	06042223          	sw	zero,100(s0)
    269e:	00042023          	sw	zero,0(s0)
    26a2:	00042223          	sw	zero,4(s0)
    26a6:	00042423          	sw	zero,8(s0)
    26aa:	c45c                	sw	a5,12(s0)
    26ac:	00042823          	sw	zero,16(s0)
    26b0:	00042a23          	sw	zero,20(s0)
    26b4:	00042c23          	sw	zero,24(s0)
    26b8:	4621                	li	a2,8
    26ba:	4581                	li	a1,0
    26bc:	05c40513          	addi	a0,s0,92
    26c0:	b61fd0ef          	jal	ra,220 <memset>
    26c4:	02042a23          	sw	zero,52(s0)
    26c8:	02042c23          	sw	zero,56(s0)
    26cc:	04042423          	sw	zero,72(s0)
    26d0:	04042623          	sw	zero,76(s0)
    26d4:	8522                	mv	a0,s0
    26d6:	40b2                	lw	ra,12(sp)
    26d8:	4422                	lw	s0,8(sp)
    26da:	4492                	lw	s1,4(sp)
    26dc:	4902                	lw	s2,0(sp)
    26de:	0141                	addi	sp,sp,16
    26e0:	8082                	ret
    26e2:	06840413          	addi	s0,s0,104
    26e6:	bf71                	j	2682 <__sfp+0x24>
    26e8:	4591                	li	a1,4
    26ea:	854a                	mv	a0,s2
    26ec:	ec1ff0ef          	jal	ra,25ac <__sfmoreglue>
    26f0:	c088                	sw	a0,0(s1)
    26f2:	fd49                	bnez	a0,268c <__sfp+0x2e>
    26f4:	47b1                	li	a5,12
    26f6:	00f92023          	sw	a5,0(s2)
    26fa:	4401                	li	s0,0
    26fc:	bfe1                	j	26d4 <__sfp+0x76>

000026fe <_fwalk_reent>:
    26fe:	7179                	addi	sp,sp,-48
    2700:	d422                	sw	s0,40(sp)
    2702:	d04a                	sw	s2,32(sp)
    2704:	cc52                	sw	s4,24(sp)
    2706:	ca56                	sw	s5,20(sp)
    2708:	c85a                	sw	s6,16(sp)
    270a:	c65e                	sw	s7,12(sp)
    270c:	d606                	sw	ra,44(sp)
    270e:	d226                	sw	s1,36(sp)
    2710:	ce4e                	sw	s3,28(sp)
    2712:	8a2a                	mv	s4,a0
    2714:	8aae                	mv	s5,a1
    2716:	04850413          	addi	s0,a0,72
    271a:	4901                	li	s2,0
    271c:	4b05                	li	s6,1
    271e:	5bfd                	li	s7,-1
    2720:	ec09                	bnez	s0,273a <_fwalk_reent+0x3c>
    2722:	50b2                	lw	ra,44(sp)
    2724:	5422                	lw	s0,40(sp)
    2726:	854a                	mv	a0,s2
    2728:	5492                	lw	s1,36(sp)
    272a:	5902                	lw	s2,32(sp)
    272c:	49f2                	lw	s3,28(sp)
    272e:	4a62                	lw	s4,24(sp)
    2730:	4ad2                	lw	s5,20(sp)
    2732:	4b42                	lw	s6,16(sp)
    2734:	4bb2                	lw	s7,12(sp)
    2736:	6145                	addi	sp,sp,48
    2738:	8082                	ret
    273a:	4404                	lw	s1,8(s0)
    273c:	00442983          	lw	s3,4(s0)
    2740:	19fd                	addi	s3,s3,-1
    2742:	0009d463          	bgez	s3,274a <_fwalk_reent+0x4c>
    2746:	4000                	lw	s0,0(s0)
    2748:	bfe1                	j	2720 <_fwalk_reent+0x22>
    274a:	24de                	lhu	a5,12(s1)
    274c:	00fb7b63          	bgeu	s6,a5,2762 <_fwalk_reent+0x64>
    2750:	00e49783          	lh	a5,14(s1)
    2754:	01778763          	beq	a5,s7,2762 <_fwalk_reent+0x64>
    2758:	85a6                	mv	a1,s1
    275a:	8552                	mv	a0,s4
    275c:	9a82                	jalr	s5
    275e:	00a96933          	or	s2,s2,a0
    2762:	06848493          	addi	s1,s1,104
    2766:	bfe9                	j	2740 <_fwalk_reent+0x42>

00002768 <__swhatbuf_r>:
    2768:	7119                	addi	sp,sp,-128
    276a:	daa6                	sw	s1,116(sp)
    276c:	84ae                	mv	s1,a1
    276e:	00e59583          	lh	a1,14(a1)
    2772:	dca2                	sw	s0,120(sp)
    2774:	de86                	sw	ra,124(sp)
    2776:	8432                	mv	s0,a2
    2778:	0005db63          	bgez	a1,278e <__swhatbuf_r+0x26>
    277c:	24de                	lhu	a5,12(s1)
    277e:	0006a023          	sw	zero,0(a3) # fc000000 <_eusrstack+0xdbff8000>
    2782:	0807f793          	andi	a5,a5,128
    2786:	e785                	bnez	a5,27ae <__swhatbuf_r+0x46>
    2788:	40000793          	li	a5,1024
    278c:	a01d                	j	27b2 <__swhatbuf_r+0x4a>
    278e:	0830                	addi	a2,sp,24
    2790:	c636                	sw	a3,12(sp)
    2792:	207000ef          	jal	ra,3198 <_fstat_r>
    2796:	46b2                	lw	a3,12(sp)
    2798:	fe0542e3          	bltz	a0,277c <__swhatbuf_r+0x14>
    279c:	4772                	lw	a4,28(sp)
    279e:	67bd                	lui	a5,0xf
    27a0:	8ff9                	and	a5,a5,a4
    27a2:	7779                	lui	a4,0xffffe
    27a4:	97ba                	add	a5,a5,a4
    27a6:	0017b793          	seqz	a5,a5
    27aa:	c29c                	sw	a5,0(a3)
    27ac:	bff1                	j	2788 <__swhatbuf_r+0x20>
    27ae:	04000793          	li	a5,64
    27b2:	c01c                	sw	a5,0(s0)
    27b4:	50f6                	lw	ra,124(sp)
    27b6:	5466                	lw	s0,120(sp)
    27b8:	54d6                	lw	s1,116(sp)
    27ba:	4501                	li	a0,0
    27bc:	6109                	addi	sp,sp,128
    27be:	8082                	ret

000027c0 <__smakebuf_r>:
    27c0:	25de                	lhu	a5,12(a1)
    27c2:	1101                	addi	sp,sp,-32
    27c4:	cc22                	sw	s0,24(sp)
    27c6:	ce06                	sw	ra,28(sp)
    27c8:	ca26                	sw	s1,20(sp)
    27ca:	c84a                	sw	s2,16(sp)
    27cc:	8b89                	andi	a5,a5,2
    27ce:	842e                	mv	s0,a1
    27d0:	cf89                	beqz	a5,27ea <__smakebuf_r+0x2a>
    27d2:	04740793          	addi	a5,s0,71
    27d6:	c01c                	sw	a5,0(s0)
    27d8:	c81c                	sw	a5,16(s0)
    27da:	4785                	li	a5,1
    27dc:	c85c                	sw	a5,20(s0)
    27de:	40f2                	lw	ra,28(sp)
    27e0:	4462                	lw	s0,24(sp)
    27e2:	44d2                	lw	s1,20(sp)
    27e4:	4942                	lw	s2,16(sp)
    27e6:	6105                	addi	sp,sp,32
    27e8:	8082                	ret
    27ea:	0074                	addi	a3,sp,12
    27ec:	0030                	addi	a2,sp,8
    27ee:	84aa                	mv	s1,a0
    27f0:	f79ff0ef          	jal	ra,2768 <__swhatbuf_r>
    27f4:	45a2                	lw	a1,8(sp)
    27f6:	892a                	mv	s2,a0
    27f8:	8526                	mv	a0,s1
    27fa:	2201                	jal	28fa <_malloc_r>
    27fc:	e919                	bnez	a0,2812 <__smakebuf_r+0x52>
    27fe:	00c41783          	lh	a5,12(s0)
    2802:	2007f713          	andi	a4,a5,512
    2806:	ff61                	bnez	a4,27de <__smakebuf_r+0x1e>
    2808:	9bf1                	andi	a5,a5,-4
    280a:	0027e793          	ori	a5,a5,2
    280e:	a45e                	sh	a5,12(s0)
    2810:	b7c9                	j	27d2 <__smakebuf_r+0x12>
    2812:	00000797          	auipc	a5,0x0
    2816:	d9078793          	addi	a5,a5,-624 # 25a2 <_cleanup_r>
    281a:	d49c                	sw	a5,40(s1)
    281c:	245e                	lhu	a5,12(s0)
    281e:	c008                	sw	a0,0(s0)
    2820:	c808                	sw	a0,16(s0)
    2822:	0807e793          	ori	a5,a5,128
    2826:	a45e                	sh	a5,12(s0)
    2828:	47a2                	lw	a5,8(sp)
    282a:	c85c                	sw	a5,20(s0)
    282c:	47b2                	lw	a5,12(sp)
    282e:	cf81                	beqz	a5,2846 <__smakebuf_r+0x86>
    2830:	00e41583          	lh	a1,14(s0)
    2834:	8526                	mv	a0,s1
    2836:	18d000ef          	jal	ra,31c2 <_isatty_r>
    283a:	c511                	beqz	a0,2846 <__smakebuf_r+0x86>
    283c:	245e                	lhu	a5,12(s0)
    283e:	9bf1                	andi	a5,a5,-4
    2840:	0017e793          	ori	a5,a5,1
    2844:	a45e                	sh	a5,12(s0)
    2846:	245e                	lhu	a5,12(s0)
    2848:	00f96933          	or	s2,s2,a5
    284c:	01241623          	sh	s2,12(s0)
    2850:	b779                	j	27de <__smakebuf_r+0x1e>

00002852 <_free_r>:
    2852:	c1dd                	beqz	a1,28f8 <_free_r+0xa6>
    2854:	ffc5a783          	lw	a5,-4(a1)
    2858:	1141                	addi	sp,sp,-16
    285a:	c422                	sw	s0,8(sp)
    285c:	c606                	sw	ra,12(sp)
    285e:	c226                	sw	s1,4(sp)
    2860:	ffc58413          	addi	s0,a1,-4
    2864:	0007d363          	bgez	a5,286a <_free_r+0x18>
    2868:	943e                	add	s0,s0,a5
    286a:	84aa                	mv	s1,a0
    286c:	1c3000ef          	jal	ra,322e <__malloc_lock>
    2870:	83018793          	addi	a5,gp,-2000 # 200000d8 <__malloc_free_list>
    2874:	439c                	lw	a5,0(a5)
    2876:	ef81                	bnez	a5,288e <_free_r+0x3c>
    2878:	00042223          	sw	zero,4(s0)
    287c:	8281a823          	sw	s0,-2000(gp) # 200000d8 <__malloc_free_list>
    2880:	4422                	lw	s0,8(sp)
    2882:	40b2                	lw	ra,12(sp)
    2884:	8526                	mv	a0,s1
    2886:	4492                	lw	s1,4(sp)
    2888:	0141                	addi	sp,sp,16
    288a:	1a70006f          	j	3230 <__malloc_unlock>
    288e:	00f47e63          	bgeu	s0,a5,28aa <_free_r+0x58>
    2892:	4014                	lw	a3,0(s0)
    2894:	00d40733          	add	a4,s0,a3
    2898:	00e79663          	bne	a5,a4,28a4 <_free_r+0x52>
    289c:	4398                	lw	a4,0(a5)
    289e:	43dc                	lw	a5,4(a5)
    28a0:	9736                	add	a4,a4,a3
    28a2:	c018                	sw	a4,0(s0)
    28a4:	c05c                	sw	a5,4(s0)
    28a6:	bfd9                	j	287c <_free_r+0x2a>
    28a8:	87ba                	mv	a5,a4
    28aa:	43d8                	lw	a4,4(a5)
    28ac:	c319                	beqz	a4,28b2 <_free_r+0x60>
    28ae:	fee47de3          	bgeu	s0,a4,28a8 <_free_r+0x56>
    28b2:	4394                	lw	a3,0(a5)
    28b4:	00d78633          	add	a2,a5,a3
    28b8:	00861f63          	bne	a2,s0,28d6 <_free_r+0x84>
    28bc:	4010                	lw	a2,0(s0)
    28be:	96b2                	add	a3,a3,a2
    28c0:	c394                	sw	a3,0(a5)
    28c2:	00d78633          	add	a2,a5,a3
    28c6:	fac71de3          	bne	a4,a2,2880 <_free_r+0x2e>
    28ca:	4310                	lw	a2,0(a4)
    28cc:	4358                	lw	a4,4(a4)
    28ce:	96b2                	add	a3,a3,a2
    28d0:	c394                	sw	a3,0(a5)
    28d2:	c3d8                	sw	a4,4(a5)
    28d4:	b775                	j	2880 <_free_r+0x2e>
    28d6:	00c47563          	bgeu	s0,a2,28e0 <_free_r+0x8e>
    28da:	47b1                	li	a5,12
    28dc:	c09c                	sw	a5,0(s1)
    28de:	b74d                	j	2880 <_free_r+0x2e>
    28e0:	4010                	lw	a2,0(s0)
    28e2:	00c406b3          	add	a3,s0,a2
    28e6:	00d71663          	bne	a4,a3,28f2 <_free_r+0xa0>
    28ea:	4314                	lw	a3,0(a4)
    28ec:	4358                	lw	a4,4(a4)
    28ee:	96b2                	add	a3,a3,a2
    28f0:	c014                	sw	a3,0(s0)
    28f2:	c058                	sw	a4,4(s0)
    28f4:	c3c0                	sw	s0,4(a5)
    28f6:	b769                	j	2880 <_free_r+0x2e>
    28f8:	8082                	ret

000028fa <_malloc_r>:
    28fa:	1101                	addi	sp,sp,-32
    28fc:	ca26                	sw	s1,20(sp)
    28fe:	00358493          	addi	s1,a1,3
    2902:	98f1                	andi	s1,s1,-4
    2904:	ce06                	sw	ra,28(sp)
    2906:	cc22                	sw	s0,24(sp)
    2908:	c84a                	sw	s2,16(sp)
    290a:	c64e                	sw	s3,12(sp)
    290c:	04a1                	addi	s1,s1,8
    290e:	47b1                	li	a5,12
    2910:	04f4f363          	bgeu	s1,a5,2956 <_malloc_r+0x5c>
    2914:	44b1                	li	s1,12
    2916:	04b4e263          	bltu	s1,a1,295a <_malloc_r+0x60>
    291a:	892a                	mv	s2,a0
    291c:	113000ef          	jal	ra,322e <__malloc_lock>
    2920:	83018793          	addi	a5,gp,-2000 # 200000d8 <__malloc_free_list>
    2924:	4398                	lw	a4,0(a5)
    2926:	843a                	mv	s0,a4
    2928:	e039                	bnez	s0,296e <_malloc_r+0x74>
    292a:	83418793          	addi	a5,gp,-1996 # 200000dc <__malloc_sbrk_start>
    292e:	439c                	lw	a5,0(a5)
    2930:	e791                	bnez	a5,293c <_malloc_r+0x42>
    2932:	4581                	li	a1,0
    2934:	854a                	mv	a0,s2
    2936:	2f25                	jal	306e <_sbrk_r>
    2938:	82a1aa23          	sw	a0,-1996(gp) # 200000dc <__malloc_sbrk_start>
    293c:	85a6                	mv	a1,s1
    293e:	854a                	mv	a0,s2
    2940:	273d                	jal	306e <_sbrk_r>
    2942:	59fd                	li	s3,-1
    2944:	07351963          	bne	a0,s3,29b6 <_malloc_r+0xbc>
    2948:	47b1                	li	a5,12
    294a:	00f92023          	sw	a5,0(s2)
    294e:	854a                	mv	a0,s2
    2950:	0e1000ef          	jal	ra,3230 <__malloc_unlock>
    2954:	a029                	j	295e <_malloc_r+0x64>
    2956:	fc04d0e3          	bgez	s1,2916 <_malloc_r+0x1c>
    295a:	47b1                	li	a5,12
    295c:	c11c                	sw	a5,0(a0)
    295e:	4501                	li	a0,0
    2960:	40f2                	lw	ra,28(sp)
    2962:	4462                	lw	s0,24(sp)
    2964:	44d2                	lw	s1,20(sp)
    2966:	4942                	lw	s2,16(sp)
    2968:	49b2                	lw	s3,12(sp)
    296a:	6105                	addi	sp,sp,32
    296c:	8082                	ret
    296e:	401c                	lw	a5,0(s0)
    2970:	8f85                	sub	a5,a5,s1
    2972:	0207cf63          	bltz	a5,29b0 <_malloc_r+0xb6>
    2976:	46ad                	li	a3,11
    2978:	00f6f663          	bgeu	a3,a5,2984 <_malloc_r+0x8a>
    297c:	c01c                	sw	a5,0(s0)
    297e:	943e                	add	s0,s0,a5
    2980:	c004                	sw	s1,0(s0)
    2982:	a031                	j	298e <_malloc_r+0x94>
    2984:	405c                	lw	a5,4(s0)
    2986:	02871363          	bne	a4,s0,29ac <_malloc_r+0xb2>
    298a:	82f1a823          	sw	a5,-2000(gp) # 200000d8 <__malloc_free_list>
    298e:	854a                	mv	a0,s2
    2990:	0a1000ef          	jal	ra,3230 <__malloc_unlock>
    2994:	00b40513          	addi	a0,s0,11
    2998:	00440793          	addi	a5,s0,4
    299c:	9961                	andi	a0,a0,-8
    299e:	40f50733          	sub	a4,a0,a5
    29a2:	df5d                	beqz	a4,2960 <_malloc_r+0x66>
    29a4:	943a                	add	s0,s0,a4
    29a6:	8f89                	sub	a5,a5,a0
    29a8:	c01c                	sw	a5,0(s0)
    29aa:	bf5d                	j	2960 <_malloc_r+0x66>
    29ac:	c35c                	sw	a5,4(a4)
    29ae:	b7c5                	j	298e <_malloc_r+0x94>
    29b0:	8722                	mv	a4,s0
    29b2:	4040                	lw	s0,4(s0)
    29b4:	bf95                	j	2928 <_malloc_r+0x2e>
    29b6:	00350413          	addi	s0,a0,3
    29ba:	9871                	andi	s0,s0,-4
    29bc:	fc8502e3          	beq	a0,s0,2980 <_malloc_r+0x86>
    29c0:	40a405b3          	sub	a1,s0,a0
    29c4:	854a                	mv	a0,s2
    29c6:	2565                	jal	306e <_sbrk_r>
    29c8:	fb351ce3          	bne	a0,s3,2980 <_malloc_r+0x86>
    29cc:	bfb5                	j	2948 <_malloc_r+0x4e>

000029ce <__sfputc_r>:
    29ce:	461c                	lw	a5,8(a2)
    29d0:	17fd                	addi	a5,a5,-1
    29d2:	c61c                	sw	a5,8(a2)
    29d4:	0007da63          	bgez	a5,29e8 <__sfputc_r+0x1a>
    29d8:	4e18                	lw	a4,24(a2)
    29da:	00e7c563          	blt	a5,a4,29e4 <__sfputc_r+0x16>
    29de:	47a9                	li	a5,10
    29e0:	00f59463          	bne	a1,a5,29e8 <__sfputc_r+0x1a>
    29e4:	80bff06f          	j	21ee <__swbuf_r>
    29e8:	421c                	lw	a5,0(a2)
    29ea:	852e                	mv	a0,a1
    29ec:	00178713          	addi	a4,a5,1
    29f0:	c218                	sw	a4,0(a2)
    29f2:	a38c                	sb	a1,0(a5)
    29f4:	8082                	ret

000029f6 <__sfputs_r>:
    29f6:	1101                	addi	sp,sp,-32
    29f8:	cc22                	sw	s0,24(sp)
    29fa:	ca26                	sw	s1,20(sp)
    29fc:	c84a                	sw	s2,16(sp)
    29fe:	c64e                	sw	s3,12(sp)
    2a00:	c452                	sw	s4,8(sp)
    2a02:	ce06                	sw	ra,28(sp)
    2a04:	892a                	mv	s2,a0
    2a06:	89ae                	mv	s3,a1
    2a08:	8432                	mv	s0,a2
    2a0a:	00d604b3          	add	s1,a2,a3
    2a0e:	5a7d                	li	s4,-1
    2a10:	00941463          	bne	s0,s1,2a18 <__sfputs_r+0x22>
    2a14:	4501                	li	a0,0
    2a16:	a809                	j	2a28 <__sfputs_r+0x32>
    2a18:	200c                	lbu	a1,0(s0)
    2a1a:	864e                	mv	a2,s3
    2a1c:	854a                	mv	a0,s2
    2a1e:	fb1ff0ef          	jal	ra,29ce <__sfputc_r>
    2a22:	0405                	addi	s0,s0,1
    2a24:	ff4516e3          	bne	a0,s4,2a10 <__sfputs_r+0x1a>
    2a28:	40f2                	lw	ra,28(sp)
    2a2a:	4462                	lw	s0,24(sp)
    2a2c:	44d2                	lw	s1,20(sp)
    2a2e:	4942                	lw	s2,16(sp)
    2a30:	49b2                	lw	s3,12(sp)
    2a32:	4a22                	lw	s4,8(sp)
    2a34:	6105                	addi	sp,sp,32
    2a36:	8082                	ret

00002a38 <_vfiprintf_r>:
    2a38:	7135                	addi	sp,sp,-160
    2a3a:	cd22                	sw	s0,152(sp)
    2a3c:	cb26                	sw	s1,148(sp)
    2a3e:	c94a                	sw	s2,144(sp)
    2a40:	c74e                	sw	s3,140(sp)
    2a42:	cf06                	sw	ra,156(sp)
    2a44:	c552                	sw	s4,136(sp)
    2a46:	c356                	sw	s5,132(sp)
    2a48:	c15a                	sw	s6,128(sp)
    2a4a:	dede                	sw	s7,124(sp)
    2a4c:	dce2                	sw	s8,120(sp)
    2a4e:	dae6                	sw	s9,116(sp)
    2a50:	89aa                	mv	s3,a0
    2a52:	84ae                	mv	s1,a1
    2a54:	8932                	mv	s2,a2
    2a56:	8436                	mv	s0,a3
    2a58:	c509                	beqz	a0,2a62 <_vfiprintf_r+0x2a>
    2a5a:	4d1c                	lw	a5,24(a0)
    2a5c:	e399                	bnez	a5,2a62 <_vfiprintf_r+0x2a>
    2a5e:	b95ff0ef          	jal	ra,25f2 <__sinit>
    2a62:	00001797          	auipc	a5,0x1
    2a66:	af678793          	addi	a5,a5,-1290 # 3558 <__sf_fake_stdin>
    2a6a:	0cf49863          	bne	s1,a5,2b3a <_vfiprintf_r+0x102>
    2a6e:	0049a483          	lw	s1,4(s3)
    2a72:	24de                	lhu	a5,12(s1)
    2a74:	8ba1                	andi	a5,a5,8
    2a76:	c7e5                	beqz	a5,2b5e <_vfiprintf_r+0x126>
    2a78:	489c                	lw	a5,16(s1)
    2a7a:	c3f5                	beqz	a5,2b5e <_vfiprintf_r+0x126>
    2a7c:	02000793          	li	a5,32
    2a80:	02f104a3          	sb	a5,41(sp)
    2a84:	03000793          	li	a5,48
    2a88:	d202                	sw	zero,36(sp)
    2a8a:	02f10523          	sb	a5,42(sp)
    2a8e:	c622                	sw	s0,12(sp)
    2a90:	02500b93          	li	s7,37
    2a94:	00001a97          	auipc	s5,0x1
    2a98:	b04a8a93          	addi	s5,s5,-1276 # 3598 <__sf_fake_stdout+0x20>
    2a9c:	4c05                	li	s8,1
    2a9e:	4b29                	li	s6,10
    2aa0:	844a                	mv	s0,s2
    2aa2:	201c                	lbu	a5,0(s0)
    2aa4:	c399                	beqz	a5,2aaa <_vfiprintf_r+0x72>
    2aa6:	0d779f63          	bne	a5,s7,2b84 <_vfiprintf_r+0x14c>
    2aaa:	41240cb3          	sub	s9,s0,s2
    2aae:	000c8e63          	beqz	s9,2aca <_vfiprintf_r+0x92>
    2ab2:	86e6                	mv	a3,s9
    2ab4:	864a                	mv	a2,s2
    2ab6:	85a6                	mv	a1,s1
    2ab8:	854e                	mv	a0,s3
    2aba:	f3dff0ef          	jal	ra,29f6 <__sfputs_r>
    2abe:	57fd                	li	a5,-1
    2ac0:	1cf50f63          	beq	a0,a5,2c9e <_vfiprintf_r+0x266>
    2ac4:	5692                	lw	a3,36(sp)
    2ac6:	96e6                	add	a3,a3,s9
    2ac8:	d236                	sw	a3,36(sp)
    2aca:	201c                	lbu	a5,0(s0)
    2acc:	1c078963          	beqz	a5,2c9e <_vfiprintf_r+0x266>
    2ad0:	57fd                	li	a5,-1
    2ad2:	00140913          	addi	s2,s0,1
    2ad6:	c802                	sw	zero,16(sp)
    2ad8:	ce02                	sw	zero,28(sp)
    2ada:	ca3e                	sw	a5,20(sp)
    2adc:	cc02                	sw	zero,24(sp)
    2ade:	040109a3          	sb	zero,83(sp)
    2ae2:	d482                	sw	zero,104(sp)
    2ae4:	00094583          	lbu	a1,0(s2)
    2ae8:	4615                	li	a2,5
    2aea:	8556                	mv	a0,s5
    2aec:	272d                	jal	3216 <memchr>
    2aee:	00190413          	addi	s0,s2,1
    2af2:	47c2                	lw	a5,16(sp)
    2af4:	e951                	bnez	a0,2b88 <_vfiprintf_r+0x150>
    2af6:	0107f713          	andi	a4,a5,16
    2afa:	c709                	beqz	a4,2b04 <_vfiprintf_r+0xcc>
    2afc:	02000713          	li	a4,32
    2b00:	04e109a3          	sb	a4,83(sp)
    2b04:	0087f713          	andi	a4,a5,8
    2b08:	c709                	beqz	a4,2b12 <_vfiprintf_r+0xda>
    2b0a:	02b00713          	li	a4,43
    2b0e:	04e109a3          	sb	a4,83(sp)
    2b12:	00094683          	lbu	a3,0(s2)
    2b16:	02a00713          	li	a4,42
    2b1a:	06e68f63          	beq	a3,a4,2b98 <_vfiprintf_r+0x160>
    2b1e:	47f2                	lw	a5,28(sp)
    2b20:	844a                	mv	s0,s2
    2b22:	4681                	li	a3,0
    2b24:	4625                	li	a2,9
    2b26:	2018                	lbu	a4,0(s0)
    2b28:	00140593          	addi	a1,s0,1
    2b2c:	fd070713          	addi	a4,a4,-48 # ffffdfd0 <_eusrstack+0xdfff5fd0>
    2b30:	0ae67763          	bgeu	a2,a4,2bde <_vfiprintf_r+0x1a6>
    2b34:	cab5                	beqz	a3,2ba8 <_vfiprintf_r+0x170>
    2b36:	ce3e                	sw	a5,28(sp)
    2b38:	a885                	j	2ba8 <_vfiprintf_r+0x170>
    2b3a:	00001797          	auipc	a5,0x1
    2b3e:	a3e78793          	addi	a5,a5,-1474 # 3578 <__sf_fake_stdout>
    2b42:	00f49563          	bne	s1,a5,2b4c <_vfiprintf_r+0x114>
    2b46:	0089a483          	lw	s1,8(s3)
    2b4a:	b725                	j	2a72 <_vfiprintf_r+0x3a>
    2b4c:	00001797          	auipc	a5,0x1
    2b50:	9ec78793          	addi	a5,a5,-1556 # 3538 <__sf_fake_stderr>
    2b54:	f0f49fe3          	bne	s1,a5,2a72 <_vfiprintf_r+0x3a>
    2b58:	00c9a483          	lw	s1,12(s3)
    2b5c:	bf19                	j	2a72 <_vfiprintf_r+0x3a>
    2b5e:	85a6                	mv	a1,s1
    2b60:	854e                	mv	a0,s3
    2b62:	f48ff0ef          	jal	ra,22aa <__swsetup_r>
    2b66:	d919                	beqz	a0,2a7c <_vfiprintf_r+0x44>
    2b68:	557d                	li	a0,-1
    2b6a:	40fa                	lw	ra,156(sp)
    2b6c:	446a                	lw	s0,152(sp)
    2b6e:	44da                	lw	s1,148(sp)
    2b70:	494a                	lw	s2,144(sp)
    2b72:	49ba                	lw	s3,140(sp)
    2b74:	4a2a                	lw	s4,136(sp)
    2b76:	4a9a                	lw	s5,132(sp)
    2b78:	4b0a                	lw	s6,128(sp)
    2b7a:	5bf6                	lw	s7,124(sp)
    2b7c:	5c66                	lw	s8,120(sp)
    2b7e:	5cd6                	lw	s9,116(sp)
    2b80:	610d                	addi	sp,sp,160
    2b82:	8082                	ret
    2b84:	0405                	addi	s0,s0,1
    2b86:	bf31                	j	2aa2 <_vfiprintf_r+0x6a>
    2b88:	41550533          	sub	a0,a0,s5
    2b8c:	00ac1533          	sll	a0,s8,a0
    2b90:	8fc9                	or	a5,a5,a0
    2b92:	c83e                	sw	a5,16(sp)
    2b94:	8922                	mv	s2,s0
    2b96:	b7b9                	j	2ae4 <_vfiprintf_r+0xac>
    2b98:	4732                	lw	a4,12(sp)
    2b9a:	00470693          	addi	a3,a4,4
    2b9e:	4318                	lw	a4,0(a4)
    2ba0:	c636                	sw	a3,12(sp)
    2ba2:	02074763          	bltz	a4,2bd0 <_vfiprintf_r+0x198>
    2ba6:	ce3a                	sw	a4,28(sp)
    2ba8:	2018                	lbu	a4,0(s0)
    2baa:	02e00793          	li	a5,46
    2bae:	04f71d63          	bne	a4,a5,2c08 <_vfiprintf_r+0x1d0>
    2bb2:	3018                	lbu	a4,1(s0)
    2bb4:	02a00793          	li	a5,42
    2bb8:	02f71b63          	bne	a4,a5,2bee <_vfiprintf_r+0x1b6>
    2bbc:	47b2                	lw	a5,12(sp)
    2bbe:	0409                	addi	s0,s0,2
    2bc0:	00478713          	addi	a4,a5,4
    2bc4:	439c                	lw	a5,0(a5)
    2bc6:	c63a                	sw	a4,12(sp)
    2bc8:	0207c163          	bltz	a5,2bea <_vfiprintf_r+0x1b2>
    2bcc:	ca3e                	sw	a5,20(sp)
    2bce:	a82d                	j	2c08 <_vfiprintf_r+0x1d0>
    2bd0:	40e00733          	neg	a4,a4
    2bd4:	0027e793          	ori	a5,a5,2
    2bd8:	ce3a                	sw	a4,28(sp)
    2bda:	c83e                	sw	a5,16(sp)
    2bdc:	b7f1                	j	2ba8 <_vfiprintf_r+0x170>
    2bde:	036787b3          	mul	a5,a5,s6
    2be2:	4685                	li	a3,1
    2be4:	842e                	mv	s0,a1
    2be6:	97ba                	add	a5,a5,a4
    2be8:	bf3d                	j	2b26 <_vfiprintf_r+0xee>
    2bea:	57fd                	li	a5,-1
    2bec:	b7c5                	j	2bcc <_vfiprintf_r+0x194>
    2bee:	0405                	addi	s0,s0,1
    2bf0:	ca02                	sw	zero,20(sp)
    2bf2:	4681                	li	a3,0
    2bf4:	4781                	li	a5,0
    2bf6:	4625                	li	a2,9
    2bf8:	2018                	lbu	a4,0(s0)
    2bfa:	00140593          	addi	a1,s0,1
    2bfe:	fd070713          	addi	a4,a4,-48
    2c02:	06e67463          	bgeu	a2,a4,2c6a <_vfiprintf_r+0x232>
    2c06:	f2f9                	bnez	a3,2bcc <_vfiprintf_r+0x194>
    2c08:	200c                	lbu	a1,0(s0)
    2c0a:	460d                	li	a2,3
    2c0c:	00001517          	auipc	a0,0x1
    2c10:	99450513          	addi	a0,a0,-1644 # 35a0 <__sf_fake_stdout+0x28>
    2c14:	2509                	jal	3216 <memchr>
    2c16:	cd11                	beqz	a0,2c32 <_vfiprintf_r+0x1fa>
    2c18:	00001797          	auipc	a5,0x1
    2c1c:	98878793          	addi	a5,a5,-1656 # 35a0 <__sf_fake_stdout+0x28>
    2c20:	8d1d                	sub	a0,a0,a5
    2c22:	04000793          	li	a5,64
    2c26:	00a797b3          	sll	a5,a5,a0
    2c2a:	4542                	lw	a0,16(sp)
    2c2c:	0405                	addi	s0,s0,1
    2c2e:	8d5d                	or	a0,a0,a5
    2c30:	c82a                	sw	a0,16(sp)
    2c32:	200c                	lbu	a1,0(s0)
    2c34:	4619                	li	a2,6
    2c36:	00001517          	auipc	a0,0x1
    2c3a:	96e50513          	addi	a0,a0,-1682 # 35a4 <__sf_fake_stdout+0x2c>
    2c3e:	00140913          	addi	s2,s0,1
    2c42:	02b10423          	sb	a1,40(sp)
    2c46:	2bc1                	jal	3216 <memchr>
    2c48:	c135                	beqz	a0,2cac <_vfiprintf_r+0x274>
    2c4a:	ffffd797          	auipc	a5,0xffffd
    2c4e:	3b678793          	addi	a5,a5,950 # 0 <_sinit>
    2c52:	e795                	bnez	a5,2c7e <_vfiprintf_r+0x246>
    2c54:	4742                	lw	a4,16(sp)
    2c56:	47b2                	lw	a5,12(sp)
    2c58:	10077713          	andi	a4,a4,256
    2c5c:	cf09                	beqz	a4,2c76 <_vfiprintf_r+0x23e>
    2c5e:	0791                	addi	a5,a5,4
    2c60:	c63e                	sw	a5,12(sp)
    2c62:	5792                	lw	a5,36(sp)
    2c64:	97d2                	add	a5,a5,s4
    2c66:	d23e                	sw	a5,36(sp)
    2c68:	bd25                	j	2aa0 <_vfiprintf_r+0x68>
    2c6a:	036787b3          	mul	a5,a5,s6
    2c6e:	4685                	li	a3,1
    2c70:	842e                	mv	s0,a1
    2c72:	97ba                	add	a5,a5,a4
    2c74:	b751                	j	2bf8 <_vfiprintf_r+0x1c0>
    2c76:	079d                	addi	a5,a5,7
    2c78:	9be1                	andi	a5,a5,-8
    2c7a:	07a1                	addi	a5,a5,8
    2c7c:	b7d5                	j	2c60 <_vfiprintf_r+0x228>
    2c7e:	0078                	addi	a4,sp,12
    2c80:	00000697          	auipc	a3,0x0
    2c84:	d7668693          	addi	a3,a3,-650 # 29f6 <__sfputs_r>
    2c88:	8626                	mv	a2,s1
    2c8a:	080c                	addi	a1,sp,16
    2c8c:	854e                	mv	a0,s3
    2c8e:	00000097          	auipc	ra,0x0
    2c92:	000000e7          	jalr	zero # 0 <_sinit>
    2c96:	57fd                	li	a5,-1
    2c98:	8a2a                	mv	s4,a0
    2c9a:	fcf514e3          	bne	a0,a5,2c62 <_vfiprintf_r+0x22a>
    2c9e:	24de                	lhu	a5,12(s1)
    2ca0:	0407f793          	andi	a5,a5,64
    2ca4:	ec0792e3          	bnez	a5,2b68 <_vfiprintf_r+0x130>
    2ca8:	5512                	lw	a0,36(sp)
    2caa:	b5c1                	j	2b6a <_vfiprintf_r+0x132>
    2cac:	0078                	addi	a4,sp,12
    2cae:	00000697          	auipc	a3,0x0
    2cb2:	d4868693          	addi	a3,a3,-696 # 29f6 <__sfputs_r>
    2cb6:	8626                	mv	a2,s1
    2cb8:	080c                	addi	a1,sp,16
    2cba:	854e                	mv	a0,s3
    2cbc:	2a01                	jal	2dcc <_printf_i>
    2cbe:	bfe1                	j	2c96 <_vfiprintf_r+0x25e>

00002cc0 <_printf_common>:
    2cc0:	7179                	addi	sp,sp,-48
    2cc2:	ca56                	sw	s5,20(sp)
    2cc4:	499c                	lw	a5,16(a1)
    2cc6:	8aba                	mv	s5,a4
    2cc8:	4598                	lw	a4,8(a1)
    2cca:	d422                	sw	s0,40(sp)
    2ccc:	d226                	sw	s1,36(sp)
    2cce:	ce4e                	sw	s3,28(sp)
    2cd0:	cc52                	sw	s4,24(sp)
    2cd2:	d606                	sw	ra,44(sp)
    2cd4:	d04a                	sw	s2,32(sp)
    2cd6:	c85a                	sw	s6,16(sp)
    2cd8:	c65e                	sw	s7,12(sp)
    2cda:	89aa                	mv	s3,a0
    2cdc:	842e                	mv	s0,a1
    2cde:	84b2                	mv	s1,a2
    2ce0:	8a36                	mv	s4,a3
    2ce2:	00e7d363          	bge	a5,a4,2ce8 <_printf_common+0x28>
    2ce6:	87ba                	mv	a5,a4
    2ce8:	c09c                	sw	a5,0(s1)
    2cea:	04344703          	lbu	a4,67(s0)
    2cee:	c319                	beqz	a4,2cf4 <_printf_common+0x34>
    2cf0:	0785                	addi	a5,a5,1
    2cf2:	c09c                	sw	a5,0(s1)
    2cf4:	401c                	lw	a5,0(s0)
    2cf6:	0207f793          	andi	a5,a5,32
    2cfa:	c781                	beqz	a5,2d02 <_printf_common+0x42>
    2cfc:	409c                	lw	a5,0(s1)
    2cfe:	0789                	addi	a5,a5,2
    2d00:	c09c                	sw	a5,0(s1)
    2d02:	00042903          	lw	s2,0(s0)
    2d06:	00697913          	andi	s2,s2,6
    2d0a:	00091a63          	bnez	s2,2d1e <_printf_common+0x5e>
    2d0e:	01940b13          	addi	s6,s0,25
    2d12:	5bfd                	li	s7,-1
    2d14:	445c                	lw	a5,12(s0)
    2d16:	4098                	lw	a4,0(s1)
    2d18:	8f99                	sub	a5,a5,a4
    2d1a:	04f94c63          	blt	s2,a5,2d72 <_printf_common+0xb2>
    2d1e:	401c                	lw	a5,0(s0)
    2d20:	04344683          	lbu	a3,67(s0)
    2d24:	0207f793          	andi	a5,a5,32
    2d28:	00d036b3          	snez	a3,a3
    2d2c:	eba5                	bnez	a5,2d9c <_printf_common+0xdc>
    2d2e:	04340613          	addi	a2,s0,67
    2d32:	85d2                	mv	a1,s4
    2d34:	854e                	mv	a0,s3
    2d36:	9a82                	jalr	s5
    2d38:	57fd                	li	a5,-1
    2d3a:	04f50363          	beq	a0,a5,2d80 <_printf_common+0xc0>
    2d3e:	401c                	lw	a5,0(s0)
    2d40:	4611                	li	a2,4
    2d42:	4098                	lw	a4,0(s1)
    2d44:	8b99                	andi	a5,a5,6
    2d46:	4454                	lw	a3,12(s0)
    2d48:	4481                	li	s1,0
    2d4a:	00c79763          	bne	a5,a2,2d58 <_printf_common+0x98>
    2d4e:	40e684b3          	sub	s1,a3,a4
    2d52:	0004d363          	bgez	s1,2d58 <_printf_common+0x98>
    2d56:	4481                	li	s1,0
    2d58:	441c                	lw	a5,8(s0)
    2d5a:	4818                	lw	a4,16(s0)
    2d5c:	00f75463          	bge	a4,a5,2d64 <_printf_common+0xa4>
    2d60:	8f99                	sub	a5,a5,a4
    2d62:	94be                	add	s1,s1,a5
    2d64:	4901                	li	s2,0
    2d66:	0469                	addi	s0,s0,26
    2d68:	5b7d                	li	s6,-1
    2d6a:	05249863          	bne	s1,s2,2dba <_printf_common+0xfa>
    2d6e:	4501                	li	a0,0
    2d70:	a809                	j	2d82 <_printf_common+0xc2>
    2d72:	4685                	li	a3,1
    2d74:	865a                	mv	a2,s6
    2d76:	85d2                	mv	a1,s4
    2d78:	854e                	mv	a0,s3
    2d7a:	9a82                	jalr	s5
    2d7c:	01751e63          	bne	a0,s7,2d98 <_printf_common+0xd8>
    2d80:	557d                	li	a0,-1
    2d82:	50b2                	lw	ra,44(sp)
    2d84:	5422                	lw	s0,40(sp)
    2d86:	5492                	lw	s1,36(sp)
    2d88:	5902                	lw	s2,32(sp)
    2d8a:	49f2                	lw	s3,28(sp)
    2d8c:	4a62                	lw	s4,24(sp)
    2d8e:	4ad2                	lw	s5,20(sp)
    2d90:	4b42                	lw	s6,16(sp)
    2d92:	4bb2                	lw	s7,12(sp)
    2d94:	6145                	addi	sp,sp,48
    2d96:	8082                	ret
    2d98:	0905                	addi	s2,s2,1
    2d9a:	bfad                	j	2d14 <_printf_common+0x54>
    2d9c:	00d40733          	add	a4,s0,a3
    2da0:	03000613          	li	a2,48
    2da4:	04c701a3          	sb	a2,67(a4)
    2da8:	04544703          	lbu	a4,69(s0)
    2dac:	00168793          	addi	a5,a3,1
    2db0:	97a2                	add	a5,a5,s0
    2db2:	0689                	addi	a3,a3,2
    2db4:	04e781a3          	sb	a4,67(a5)
    2db8:	bf9d                	j	2d2e <_printf_common+0x6e>
    2dba:	4685                	li	a3,1
    2dbc:	8622                	mv	a2,s0
    2dbe:	85d2                	mv	a1,s4
    2dc0:	854e                	mv	a0,s3
    2dc2:	9a82                	jalr	s5
    2dc4:	fb650ee3          	beq	a0,s6,2d80 <_printf_common+0xc0>
    2dc8:	0905                	addi	s2,s2,1
    2dca:	b745                	j	2d6a <_printf_common+0xaa>

00002dcc <_printf_i>:
    2dcc:	7179                	addi	sp,sp,-48
    2dce:	d422                	sw	s0,40(sp)
    2dd0:	d226                	sw	s1,36(sp)
    2dd2:	d04a                	sw	s2,32(sp)
    2dd4:	ce4e                	sw	s3,28(sp)
    2dd6:	d606                	sw	ra,44(sp)
    2dd8:	cc52                	sw	s4,24(sp)
    2dda:	ca56                	sw	s5,20(sp)
    2ddc:	c85a                	sw	s6,16(sp)
    2dde:	89b6                	mv	s3,a3
    2de0:	2d94                	lbu	a3,24(a1)
    2de2:	06900793          	li	a5,105
    2de6:	8932                	mv	s2,a2
    2de8:	84aa                	mv	s1,a0
    2dea:	842e                	mv	s0,a1
    2dec:	04358613          	addi	a2,a1,67
    2df0:	02f68d63          	beq	a3,a5,2e2a <_printf_i+0x5e>
    2df4:	06d7e263          	bltu	a5,a3,2e58 <_printf_i+0x8c>
    2df8:	05800793          	li	a5,88
    2dfc:	18f68663          	beq	a3,a5,2f88 <_printf_i+0x1bc>
    2e00:	00d7ed63          	bltu	a5,a3,2e1a <_printf_i+0x4e>
    2e04:	20068e63          	beqz	a3,3020 <_printf_i+0x254>
    2e08:	04300793          	li	a5,67
    2e0c:	0af68e63          	beq	a3,a5,2ec8 <_printf_i+0xfc>
    2e10:	04240a93          	addi	s5,s0,66
    2e14:	04d40123          	sb	a3,66(s0)
    2e18:	a0c9                	j	2eda <_printf_i+0x10e>
    2e1a:	06300793          	li	a5,99
    2e1e:	0af68563          	beq	a3,a5,2ec8 <_printf_i+0xfc>
    2e22:	06400793          	li	a5,100
    2e26:	fef695e3          	bne	a3,a5,2e10 <_printf_i+0x44>
    2e2a:	401c                	lw	a5,0(s0)
    2e2c:	4308                	lw	a0,0(a4)
    2e2e:	0807f693          	andi	a3,a5,128
    2e32:	00450593          	addi	a1,a0,4
    2e36:	c6c5                	beqz	a3,2ede <_printf_i+0x112>
    2e38:	411c                	lw	a5,0(a0)
    2e3a:	c30c                	sw	a1,0(a4)
    2e3c:	0007d863          	bgez	a5,2e4c <_printf_i+0x80>
    2e40:	02d00713          	li	a4,45
    2e44:	40f007b3          	neg	a5,a5
    2e48:	04e401a3          	sb	a4,67(s0)
    2e4c:	00000697          	auipc	a3,0x0
    2e50:	76068693          	addi	a3,a3,1888 # 35ac <__sf_fake_stdout+0x34>
    2e54:	4729                	li	a4,10
    2e56:	a865                	j	2f0e <_printf_i+0x142>
    2e58:	07000793          	li	a5,112
    2e5c:	16f68263          	beq	a3,a5,2fc0 <_printf_i+0x1f4>
    2e60:	02d7e563          	bltu	a5,a3,2e8a <_printf_i+0xbe>
    2e64:	06e00793          	li	a5,110
    2e68:	18f68963          	beq	a3,a5,2ffa <_printf_i+0x22e>
    2e6c:	06f00793          	li	a5,111
    2e70:	faf690e3          	bne	a3,a5,2e10 <_printf_i+0x44>
    2e74:	400c                	lw	a1,0(s0)
    2e76:	431c                	lw	a5,0(a4)
    2e78:	0805f813          	andi	a6,a1,128
    2e7c:	00478513          	addi	a0,a5,4
    2e80:	06080763          	beqz	a6,2eee <_printf_i+0x122>
    2e84:	c308                	sw	a0,0(a4)
    2e86:	439c                	lw	a5,0(a5)
    2e88:	a885                	j	2ef8 <_printf_i+0x12c>
    2e8a:	07500793          	li	a5,117
    2e8e:	fef683e3          	beq	a3,a5,2e74 <_printf_i+0xa8>
    2e92:	07800793          	li	a5,120
    2e96:	12f68963          	beq	a3,a5,2fc8 <_printf_i+0x1fc>
    2e9a:	07300793          	li	a5,115
    2e9e:	f6f699e3          	bne	a3,a5,2e10 <_printf_i+0x44>
    2ea2:	431c                	lw	a5,0(a4)
    2ea4:	41d0                	lw	a2,4(a1)
    2ea6:	4581                	li	a1,0
    2ea8:	00478693          	addi	a3,a5,4
    2eac:	c314                	sw	a3,0(a4)
    2eae:	0007aa83          	lw	s5,0(a5)
    2eb2:	8556                	mv	a0,s5
    2eb4:	268d                	jal	3216 <memchr>
    2eb6:	c501                	beqz	a0,2ebe <_printf_i+0xf2>
    2eb8:	41550533          	sub	a0,a0,s5
    2ebc:	c048                	sw	a0,4(s0)
    2ebe:	405c                	lw	a5,4(s0)
    2ec0:	c81c                	sw	a5,16(s0)
    2ec2:	040401a3          	sb	zero,67(s0)
    2ec6:	a861                	j	2f5e <_printf_i+0x192>
    2ec8:	431c                	lw	a5,0(a4)
    2eca:	04240a93          	addi	s5,s0,66
    2ece:	00478693          	addi	a3,a5,4
    2ed2:	439c                	lw	a5,0(a5)
    2ed4:	c314                	sw	a3,0(a4)
    2ed6:	04f40123          	sb	a5,66(s0)
    2eda:	4785                	li	a5,1
    2edc:	b7d5                	j	2ec0 <_printf_i+0xf4>
    2ede:	0407f693          	andi	a3,a5,64
    2ee2:	411c                	lw	a5,0(a0)
    2ee4:	c30c                	sw	a1,0(a4)
    2ee6:	dab9                	beqz	a3,2e3c <_printf_i+0x70>
    2ee8:	07c2                	slli	a5,a5,0x10
    2eea:	87c1                	srai	a5,a5,0x10
    2eec:	bf81                	j	2e3c <_printf_i+0x70>
    2eee:	0405f593          	andi	a1,a1,64
    2ef2:	c308                	sw	a0,0(a4)
    2ef4:	d9c9                	beqz	a1,2e86 <_printf_i+0xba>
    2ef6:	239e                	lhu	a5,0(a5)
    2ef8:	06f00713          	li	a4,111
    2efc:	0ee68763          	beq	a3,a4,2fea <_printf_i+0x21e>
    2f00:	00000697          	auipc	a3,0x0
    2f04:	6ac68693          	addi	a3,a3,1708 # 35ac <__sf_fake_stdout+0x34>
    2f08:	4729                	li	a4,10
    2f0a:	040401a3          	sb	zero,67(s0)
    2f0e:	404c                	lw	a1,4(s0)
    2f10:	c40c                	sw	a1,8(s0)
    2f12:	0005c563          	bltz	a1,2f1c <_printf_i+0x150>
    2f16:	4008                	lw	a0,0(s0)
    2f18:	996d                	andi	a0,a0,-5
    2f1a:	c008                	sw	a0,0(s0)
    2f1c:	e399                	bnez	a5,2f22 <_printf_i+0x156>
    2f1e:	8ab2                	mv	s5,a2
    2f20:	cd89                	beqz	a1,2f3a <_printf_i+0x16e>
    2f22:	8ab2                	mv	s5,a2
    2f24:	02e7f5b3          	remu	a1,a5,a4
    2f28:	1afd                	addi	s5,s5,-1
    2f2a:	95b6                	add	a1,a1,a3
    2f2c:	218c                	lbu	a1,0(a1)
    2f2e:	00ba8023          	sb	a1,0(s5)
    2f32:	02e7d5b3          	divu	a1,a5,a4
    2f36:	0ce7f063          	bgeu	a5,a4,2ff6 <_printf_i+0x22a>
    2f3a:	47a1                	li	a5,8
    2f3c:	00f71e63          	bne	a4,a5,2f58 <_printf_i+0x18c>
    2f40:	401c                	lw	a5,0(s0)
    2f42:	8b85                	andi	a5,a5,1
    2f44:	cb91                	beqz	a5,2f58 <_printf_i+0x18c>
    2f46:	4058                	lw	a4,4(s0)
    2f48:	481c                	lw	a5,16(s0)
    2f4a:	00e7c763          	blt	a5,a4,2f58 <_printf_i+0x18c>
    2f4e:	03000793          	li	a5,48
    2f52:	fefa8fa3          	sb	a5,-1(s5)
    2f56:	1afd                	addi	s5,s5,-1
    2f58:	41560633          	sub	a2,a2,s5
    2f5c:	c810                	sw	a2,16(s0)
    2f5e:	874e                	mv	a4,s3
    2f60:	86ca                	mv	a3,s2
    2f62:	0070                	addi	a2,sp,12
    2f64:	85a2                	mv	a1,s0
    2f66:	8526                	mv	a0,s1
    2f68:	d59ff0ef          	jal	ra,2cc0 <_printf_common>
    2f6c:	5a7d                	li	s4,-1
    2f6e:	0b451d63          	bne	a0,s4,3028 <_printf_i+0x25c>
    2f72:	557d                	li	a0,-1
    2f74:	50b2                	lw	ra,44(sp)
    2f76:	5422                	lw	s0,40(sp)
    2f78:	5492                	lw	s1,36(sp)
    2f7a:	5902                	lw	s2,32(sp)
    2f7c:	49f2                	lw	s3,28(sp)
    2f7e:	4a62                	lw	s4,24(sp)
    2f80:	4ad2                	lw	s5,20(sp)
    2f82:	4b42                	lw	s6,16(sp)
    2f84:	6145                	addi	sp,sp,48
    2f86:	8082                	ret
    2f88:	04d582a3          	sb	a3,69(a1)
    2f8c:	00000697          	auipc	a3,0x0
    2f90:	62068693          	addi	a3,a3,1568 # 35ac <__sf_fake_stdout+0x34>
    2f94:	400c                	lw	a1,0(s0)
    2f96:	4308                	lw	a0,0(a4)
    2f98:	0805f813          	andi	a6,a1,128
    2f9c:	411c                	lw	a5,0(a0)
    2f9e:	0511                	addi	a0,a0,4
    2fa0:	02080d63          	beqz	a6,2fda <_printf_i+0x20e>
    2fa4:	c308                	sw	a0,0(a4)
    2fa6:	0015f713          	andi	a4,a1,1
    2faa:	c701                	beqz	a4,2fb2 <_printf_i+0x1e6>
    2fac:	0205e593          	ori	a1,a1,32
    2fb0:	c00c                	sw	a1,0(s0)
    2fb2:	4741                	li	a4,16
    2fb4:	fbb9                	bnez	a5,2f0a <_printf_i+0x13e>
    2fb6:	400c                	lw	a1,0(s0)
    2fb8:	fdf5f593          	andi	a1,a1,-33
    2fbc:	c00c                	sw	a1,0(s0)
    2fbe:	b7b1                	j	2f0a <_printf_i+0x13e>
    2fc0:	419c                	lw	a5,0(a1)
    2fc2:	0207e793          	ori	a5,a5,32
    2fc6:	c19c                	sw	a5,0(a1)
    2fc8:	07800793          	li	a5,120
    2fcc:	04f402a3          	sb	a5,69(s0)
    2fd0:	00000697          	auipc	a3,0x0
    2fd4:	5f068693          	addi	a3,a3,1520 # 35c0 <__sf_fake_stdout+0x48>
    2fd8:	bf75                	j	2f94 <_printf_i+0x1c8>
    2fda:	0405f813          	andi	a6,a1,64
    2fde:	c308                	sw	a0,0(a4)
    2fe0:	fc0803e3          	beqz	a6,2fa6 <_printf_i+0x1da>
    2fe4:	07c2                	slli	a5,a5,0x10
    2fe6:	83c1                	srli	a5,a5,0x10
    2fe8:	bf7d                	j	2fa6 <_printf_i+0x1da>
    2fea:	00000697          	auipc	a3,0x0
    2fee:	5c268693          	addi	a3,a3,1474 # 35ac <__sf_fake_stdout+0x34>
    2ff2:	4721                	li	a4,8
    2ff4:	bf19                	j	2f0a <_printf_i+0x13e>
    2ff6:	87ae                	mv	a5,a1
    2ff8:	b735                	j	2f24 <_printf_i+0x158>
    2ffa:	4194                	lw	a3,0(a1)
    2ffc:	431c                	lw	a5,0(a4)
    2ffe:	49cc                	lw	a1,20(a1)
    3000:	0806f813          	andi	a6,a3,128
    3004:	00478513          	addi	a0,a5,4
    3008:	00080663          	beqz	a6,3014 <_printf_i+0x248>
    300c:	c308                	sw	a0,0(a4)
    300e:	439c                	lw	a5,0(a5)
    3010:	c38c                	sw	a1,0(a5)
    3012:	a039                	j	3020 <_printf_i+0x254>
    3014:	c308                	sw	a0,0(a4)
    3016:	0406f693          	andi	a3,a3,64
    301a:	439c                	lw	a5,0(a5)
    301c:	daf5                	beqz	a3,3010 <_printf_i+0x244>
    301e:	a38e                	sh	a1,0(a5)
    3020:	00042823          	sw	zero,16(s0)
    3024:	8ab2                	mv	s5,a2
    3026:	bf25                	j	2f5e <_printf_i+0x192>
    3028:	4814                	lw	a3,16(s0)
    302a:	8656                	mv	a2,s5
    302c:	85ca                	mv	a1,s2
    302e:	8526                	mv	a0,s1
    3030:	9982                	jalr	s3
    3032:	f54500e3          	beq	a0,s4,2f72 <_printf_i+0x1a6>
    3036:	401c                	lw	a5,0(s0)
    3038:	8b89                	andi	a5,a5,2
    303a:	e78d                	bnez	a5,3064 <_printf_i+0x298>
    303c:	47b2                	lw	a5,12(sp)
    303e:	4448                	lw	a0,12(s0)
    3040:	f2f55ae3          	bge	a0,a5,2f74 <_printf_i+0x1a8>
    3044:	853e                	mv	a0,a5
    3046:	b73d                	j	2f74 <_printf_i+0x1a8>
    3048:	4685                	li	a3,1
    304a:	8656                	mv	a2,s5
    304c:	85ca                	mv	a1,s2
    304e:	8526                	mv	a0,s1
    3050:	9982                	jalr	s3
    3052:	f36500e3          	beq	a0,s6,2f72 <_printf_i+0x1a6>
    3056:	0a05                	addi	s4,s4,1
    3058:	445c                	lw	a5,12(s0)
    305a:	4732                	lw	a4,12(sp)
    305c:	8f99                	sub	a5,a5,a4
    305e:	fefa45e3          	blt	s4,a5,3048 <_printf_i+0x27c>
    3062:	bfe9                	j	303c <_printf_i+0x270>
    3064:	4a01                	li	s4,0
    3066:	01940a93          	addi	s5,s0,25
    306a:	5b7d                	li	s6,-1
    306c:	b7f5                	j	3058 <_printf_i+0x28c>

0000306e <_sbrk_r>:
    306e:	1141                	addi	sp,sp,-16
    3070:	c422                	sw	s0,8(sp)
    3072:	842a                	mv	s0,a0
    3074:	852e                	mv	a0,a1
    3076:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    307a:	c606                	sw	ra,12(sp)
    307c:	b31fe0ef          	jal	ra,1bac <_sbrk>
    3080:	57fd                	li	a5,-1
    3082:	00f51763          	bne	a0,a5,3090 <_sbrk_r+0x22>
    3086:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    308a:	439c                	lw	a5,0(a5)
    308c:	c391                	beqz	a5,3090 <_sbrk_r+0x22>
    308e:	c01c                	sw	a5,0(s0)
    3090:	40b2                	lw	ra,12(sp)
    3092:	4422                	lw	s0,8(sp)
    3094:	0141                	addi	sp,sp,16
    3096:	8082                	ret

00003098 <__sread>:
    3098:	1141                	addi	sp,sp,-16
    309a:	c422                	sw	s0,8(sp)
    309c:	842e                	mv	s0,a1
    309e:	00e59583          	lh	a1,14(a1)
    30a2:	c606                	sw	ra,12(sp)
    30a4:	2279                	jal	3232 <_read_r>
    30a6:	00054963          	bltz	a0,30b8 <__sread+0x20>
    30aa:	487c                	lw	a5,84(s0)
    30ac:	97aa                	add	a5,a5,a0
    30ae:	c87c                	sw	a5,84(s0)
    30b0:	40b2                	lw	ra,12(sp)
    30b2:	4422                	lw	s0,8(sp)
    30b4:	0141                	addi	sp,sp,16
    30b6:	8082                	ret
    30b8:	245e                	lhu	a5,12(s0)
    30ba:	777d                	lui	a4,0xfffff
    30bc:	177d                	addi	a4,a4,-1
    30be:	8ff9                	and	a5,a5,a4
    30c0:	a45e                	sh	a5,12(s0)
    30c2:	b7fd                	j	30b0 <__sread+0x18>

000030c4 <__swrite>:
    30c4:	25de                	lhu	a5,12(a1)
    30c6:	1101                	addi	sp,sp,-32
    30c8:	cc22                	sw	s0,24(sp)
    30ca:	ca26                	sw	s1,20(sp)
    30cc:	c84a                	sw	s2,16(sp)
    30ce:	c64e                	sw	s3,12(sp)
    30d0:	ce06                	sw	ra,28(sp)
    30d2:	1007f793          	andi	a5,a5,256
    30d6:	84aa                	mv	s1,a0
    30d8:	842e                	mv	s0,a1
    30da:	8932                	mv	s2,a2
    30dc:	89b6                	mv	s3,a3
    30de:	c791                	beqz	a5,30ea <__swrite+0x26>
    30e0:	00e59583          	lh	a1,14(a1)
    30e4:	4689                	li	a3,2
    30e6:	4601                	li	a2,0
    30e8:	2209                	jal	31ea <_lseek_r>
    30ea:	245e                	lhu	a5,12(s0)
    30ec:	777d                	lui	a4,0xfffff
    30ee:	177d                	addi	a4,a4,-1
    30f0:	8ff9                	and	a5,a5,a4
    30f2:	a45e                	sh	a5,12(s0)
    30f4:	00e41583          	lh	a1,14(s0)
    30f8:	4462                	lw	s0,24(sp)
    30fa:	40f2                	lw	ra,28(sp)
    30fc:	86ce                	mv	a3,s3
    30fe:	864a                	mv	a2,s2
    3100:	49b2                	lw	s3,12(sp)
    3102:	4942                	lw	s2,16(sp)
    3104:	8526                	mv	a0,s1
    3106:	44d2                	lw	s1,20(sp)
    3108:	6105                	addi	sp,sp,32
    310a:	a825                	j	3142 <_write_r>

0000310c <__sseek>:
    310c:	1141                	addi	sp,sp,-16
    310e:	c422                	sw	s0,8(sp)
    3110:	842e                	mv	s0,a1
    3112:	00e59583          	lh	a1,14(a1)
    3116:	c606                	sw	ra,12(sp)
    3118:	28c9                	jal	31ea <_lseek_r>
    311a:	57fd                	li	a5,-1
    311c:	245a                	lhu	a4,12(s0)
    311e:	00f51a63          	bne	a0,a5,3132 <__sseek+0x26>
    3122:	77fd                	lui	a5,0xfffff
    3124:	17fd                	addi	a5,a5,-1
    3126:	8ff9                	and	a5,a5,a4
    3128:	a45e                	sh	a5,12(s0)
    312a:	40b2                	lw	ra,12(sp)
    312c:	4422                	lw	s0,8(sp)
    312e:	0141                	addi	sp,sp,16
    3130:	8082                	ret
    3132:	6785                	lui	a5,0x1
    3134:	8fd9                	or	a5,a5,a4
    3136:	a45e                	sh	a5,12(s0)
    3138:	c868                	sw	a0,84(s0)
    313a:	bfc5                	j	312a <__sseek+0x1e>

0000313c <__sclose>:
    313c:	00e59583          	lh	a1,14(a1)
    3140:	a805                	j	3170 <_close_r>

00003142 <_write_r>:
    3142:	1141                	addi	sp,sp,-16
    3144:	c422                	sw	s0,8(sp)
    3146:	842a                	mv	s0,a0
    3148:	852e                	mv	a0,a1
    314a:	85b2                	mv	a1,a2
    314c:	8636                	mv	a2,a3
    314e:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    3152:	c606                	sw	ra,12(sp)
    3154:	a1bfe0ef          	jal	ra,1b6e <_write>
    3158:	57fd                	li	a5,-1
    315a:	00f51763          	bne	a0,a5,3168 <_write_r+0x26>
    315e:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    3162:	439c                	lw	a5,0(a5)
    3164:	c391                	beqz	a5,3168 <_write_r+0x26>
    3166:	c01c                	sw	a5,0(s0)
    3168:	40b2                	lw	ra,12(sp)
    316a:	4422                	lw	s0,8(sp)
    316c:	0141                	addi	sp,sp,16
    316e:	8082                	ret

00003170 <_close_r>:
    3170:	1141                	addi	sp,sp,-16
    3172:	c422                	sw	s0,8(sp)
    3174:	842a                	mv	s0,a0
    3176:	852e                	mv	a0,a1
    3178:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    317c:	c606                	sw	ra,12(sp)
    317e:	20c5                	jal	325e <_close>
    3180:	57fd                	li	a5,-1
    3182:	00f51763          	bne	a0,a5,3190 <_close_r+0x20>
    3186:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    318a:	439c                	lw	a5,0(a5)
    318c:	c391                	beqz	a5,3190 <_close_r+0x20>
    318e:	c01c                	sw	a5,0(s0)
    3190:	40b2                	lw	ra,12(sp)
    3192:	4422                	lw	s0,8(sp)
    3194:	0141                	addi	sp,sp,16
    3196:	8082                	ret

00003198 <_fstat_r>:
    3198:	1141                	addi	sp,sp,-16
    319a:	c422                	sw	s0,8(sp)
    319c:	842a                	mv	s0,a0
    319e:	852e                	mv	a0,a1
    31a0:	85b2                	mv	a1,a2
    31a2:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    31a6:	c606                	sw	ra,12(sp)
    31a8:	20c9                	jal	326a <_fstat>
    31aa:	57fd                	li	a5,-1
    31ac:	00f51763          	bne	a0,a5,31ba <_fstat_r+0x22>
    31b0:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    31b4:	439c                	lw	a5,0(a5)
    31b6:	c391                	beqz	a5,31ba <_fstat_r+0x22>
    31b8:	c01c                	sw	a5,0(s0)
    31ba:	40b2                	lw	ra,12(sp)
    31bc:	4422                	lw	s0,8(sp)
    31be:	0141                	addi	sp,sp,16
    31c0:	8082                	ret

000031c2 <_isatty_r>:
    31c2:	1141                	addi	sp,sp,-16
    31c4:	c422                	sw	s0,8(sp)
    31c6:	842a                	mv	s0,a0
    31c8:	852e                	mv	a0,a1
    31ca:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    31ce:	c606                	sw	ra,12(sp)
    31d0:	205d                	jal	3276 <_isatty>
    31d2:	57fd                	li	a5,-1
    31d4:	00f51763          	bne	a0,a5,31e2 <_isatty_r+0x20>
    31d8:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    31dc:	439c                	lw	a5,0(a5)
    31de:	c391                	beqz	a5,31e2 <_isatty_r+0x20>
    31e0:	c01c                	sw	a5,0(s0)
    31e2:	40b2                	lw	ra,12(sp)
    31e4:	4422                	lw	s0,8(sp)
    31e6:	0141                	addi	sp,sp,16
    31e8:	8082                	ret

000031ea <_lseek_r>:
    31ea:	1141                	addi	sp,sp,-16
    31ec:	c422                	sw	s0,8(sp)
    31ee:	842a                	mv	s0,a0
    31f0:	852e                	mv	a0,a1
    31f2:	85b2                	mv	a1,a2
    31f4:	8636                	mv	a2,a3
    31f6:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    31fa:	c606                	sw	ra,12(sp)
    31fc:	2059                	jal	3282 <_lseek>
    31fe:	57fd                	li	a5,-1
    3200:	00f51763          	bne	a0,a5,320e <_lseek_r+0x24>
    3204:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    3208:	439c                	lw	a5,0(a5)
    320a:	c391                	beqz	a5,320e <_lseek_r+0x24>
    320c:	c01c                	sw	a5,0(s0)
    320e:	40b2                	lw	ra,12(sp)
    3210:	4422                	lw	s0,8(sp)
    3212:	0141                	addi	sp,sp,16
    3214:	8082                	ret

00003216 <memchr>:
    3216:	0ff5f593          	andi	a1,a1,255
    321a:	962a                	add	a2,a2,a0
    321c:	00c51463          	bne	a0,a2,3224 <memchr+0xe>
    3220:	4501                	li	a0,0
    3222:	8082                	ret
    3224:	211c                	lbu	a5,0(a0)
    3226:	feb78ee3          	beq	a5,a1,3222 <memchr+0xc>
    322a:	0505                	addi	a0,a0,1
    322c:	bfc5                	j	321c <memchr+0x6>

0000322e <__malloc_lock>:
    322e:	8082                	ret

00003230 <__malloc_unlock>:
    3230:	8082                	ret

00003232 <_read_r>:
    3232:	1141                	addi	sp,sp,-16
    3234:	c422                	sw	s0,8(sp)
    3236:	842a                	mv	s0,a0
    3238:	852e                	mv	a0,a1
    323a:	85b2                	mv	a1,a2
    323c:	8636                	mv	a2,a3
    323e:	8801a023          	sw	zero,-1920(gp) # 20000128 <errno>
    3242:	c606                	sw	ra,12(sp)
    3244:	20a9                	jal	328e <_read>
    3246:	57fd                	li	a5,-1
    3248:	00f51763          	bne	a0,a5,3256 <_read_r+0x24>
    324c:	88018793          	addi	a5,gp,-1920 # 20000128 <errno>
    3250:	439c                	lw	a5,0(a5)
    3252:	c391                	beqz	a5,3256 <_read_r+0x24>
    3254:	c01c                	sw	a5,0(s0)
    3256:	40b2                	lw	ra,12(sp)
    3258:	4422                	lw	s0,8(sp)
    325a:	0141                	addi	sp,sp,16
    325c:	8082                	ret

0000325e <_close>:
    325e:	05800793          	li	a5,88
    3262:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    3266:	557d                	li	a0,-1
    3268:	8082                	ret

0000326a <_fstat>:
    326a:	05800793          	li	a5,88
    326e:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    3272:	557d                	li	a0,-1
    3274:	8082                	ret

00003276 <_isatty>:
    3276:	05800793          	li	a5,88
    327a:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    327e:	4501                	li	a0,0
    3280:	8082                	ret

00003282 <_lseek>:
    3282:	05800793          	li	a5,88
    3286:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    328a:	557d                	li	a0,-1
    328c:	8082                	ret

0000328e <_read>:
    328e:	05800793          	li	a5,88
    3292:	88f1a023          	sw	a5,-1920(gp) # 20000128 <errno>
    3296:	557d                	li	a0,-1
    3298:	8082                	ret
    329a:	0000                	unimp
    329c:	b3cdb5cf          	fnmadd.d	fa1,fs11,ft8,fs6,rup
    32a0:	bccaf5b3          	0xbccaf5b3
    32a4:	eacdafbb          	0xeacdafbb
    32a8:	aca3c9b3          	0xaca3c9b3
    32ac:	ddc5e5b3          	0xddc5e5b3
    32b0:	c6d6d8bf b3cdb5cf 	0xb3cdb5cfc6d6d8bf
    32b8:	d1d2                	sw	s4,224(sp)
    32ba:	f4c6                	fsw	fa7,104(sp)
    32bc:	afb6                	sh	a3,26(a5)
    32be:	0000                	unimp
    32c0:	0e1e                	slli	t3,t3,0x7
    32c2:	0000                	unimp
    32c4:	0e76                	slli	t3,t3,0x1d
    32c6:	0000                	unimp
    32c8:	0e4a                	slli	t3,t3,0x12
    32ca:	0000                	unimp
    32cc:	0e76                	slli	t3,t3,0x1d
    32ce:	0000                	unimp
    32d0:	0e76                	slli	t3,t3,0x1d
    32d2:	0000                	unimp
    32d4:	0e64                	addi	s1,sp,796
    32d6:	0000                	unimp
    32d8:	0e7a                	slli	t3,t3,0x1e
    32da:	0000                	unimp
    32dc:	0e9e                	slli	t4,t4,0x7
    32de:	0000                	unimp
    32e0:	bccaaabf aecbd3bc 	0xaecbd3bcbccaaabf
    32e8:	aecbaca3          	sw	a2,-1287(s7)
    32ec:	c3b1                	beqz	a5,3330 <_read+0xa2>
    32ee:	c631                	beqz	a2,333a <_read+0xac>
    32f0:	b6f4                	sb	a3,15(a3)
    32f2:	aabf00af          	0xaabf00af
    32f6:	bcca                	sh	a0,60(s1)
    32f8:	d3bc                	sw	a5,96(a5)
    32fa:	c8c8                	sw	a0,20(s1)
    32fc:	f5b3aca3          	sw	s11,-167(t2)
    3300:	bcca                	sh	a0,60(s1)
    3302:	c2ce                	sw	s3,68(sp)
    3304:	c8b6                	sw	a3,80(sp)
    3306:	203a                	lhu	a4,2(s0)
    3308:	2e25                	jal	3640 <_data_lma+0x6c>
    330a:	6632                	flw	fa2,12(sp)
    330c:	e3a1                	bnez	a5,334c <_read+0xbe>
    330e:	00000a43          	fmadd.s	fs4,ft0,ft0,ft0,rne
    3312:	0000                	unimp
    3314:	bccaaabf e8b0c1bd 	0xe8b0c1bdbccaaabf
    331c:	0000                	unimp
    331e:	0000                	unimp
    3320:	c1bd                	beqz	a1,3386 <_read+0xf8>
    3322:	e8b0                	fsw	fa2,80(s1)
    3324:	d1d2                	sw	s4,224(sp)
    3326:	a3cd                	j	3908 <_data_lma+0x334>
    3328:	b9d6                	sh	a3,52(a1)
    332a:	0000                	unimp
    332c:	ddc5e5b3          	0xddc5e5b3
    3330:	f7c1                	bnez	a5,32b8 <_read+0x2a>
    3332:	aabfccb3          	0xaabfccb3
    3336:	bcca                	sh	a0,60(s1)
    3338:	c8b5aca3          	sw	a1,-871(a1)
    333c:	fdb4                	fsw	fa3,120(a1)
    333e:	b4b0                	sb	a2,11(s1)
    3340:	fcbc                	fsw	fa5,120(s1)
    3342:	c831                	beqz	s0,3396 <_read+0x108>
    3344:	bccfc8b7          	lui	a7,0xbccfc
    3348:	00aecbd3          	fadd.s	fs7,ft9,fa0,rmm
    334c:	d3bc                	sw	a5,96(a5)
    334e:	eacdaecb          	fnmsub.d	ft9,fs11,fa2,ft9,rdn
    3352:	aca3c9b3          	0xaca3c9b3
    3356:	b1cac3d3          	0xb1cac3d3
    335a:	203a                	lhu	a4,2(s0)
    335c:	6c25                	lui	s8,0x9
    335e:	2075                	jal	340a <_read+0x17c>
    3360:	736d                	lui	t1,0xffffb
    3362:	c8b5aca3          	sw	a1,-871(a1)
    3366:	fdb4                	fsw	fa3,120(a1)
    3368:	b4b0                	sb	a2,11(s1)
    336a:	fcbc                	fsw	fa5,120(s1)
    336c:	c832                	sw	a2,16(sp)
    336e:	bccfc8b7          	lui	a7,0xbccfc
    3372:	0ac8c8d3          	fsub.d	fa7,fa7,fa2,rmm
    3376:	0000                	unimp
    3378:	d3bc                	sw	a5,96(a5)
    337a:	c8c8                	sw	a0,20(s1)
    337c:	eacd                	bnez	a3,342e <_read+0x1a0>
    337e:	aca3c9b3          	0xaca3c9b3
    3382:	c2ce                	sw	s3,68(sp)
    3384:	c8b6                	sw	a3,80(sp)
    3386:	cfc9                	beqz	a5,3420 <_read+0x192>
    3388:	fdc9                	bnez	a1,3322 <_read+0x94>
    338a:	203a                	lhu	a4,2(s0)
    338c:	a132                	sh	a2,2(a0)
    338e:	aca343e3          	blt	t1,a0,2e54 <_printf_i+0x88>
    3392:	c8b5                	beqz	s1,3406 <_read+0x178>
    3394:	fdb4                	fsw	fa3,120(a1)
    3396:	b4b0                	sb	a2,11(s1)
    3398:	fcbc                	fsw	fa5,120(s1)
    339a:	c8b7c833          	0xc8b7c833
    339e:	b0c1bdcf          	fnmadd.s	fs11,ft3,fa2,fs6,rup
    33a2:	00e8                	addi	a0,sp,76
    33a4:	bccaaabf c3d6b2be 	0xc3d6b2bebccaaabf
    33ac:	ebc33033          	0xebc33033
    33b0:	0000                	unimp
    33b2:	0000                	unimp
    33b4:	b2be                	sh	a5,34(a3)
    33b6:	c3d6                	sw	s5,196(sp)
    33b8:	eacd                	bnez	a3,346a <__clz_tab+0x32>
    33ba:	aca3c9b3          	0xaca3c9b3
    33be:	bccaaabf cbc2fdb9 	0xcbc2fdb9bccaaabf
    33c6:	aecbaca3          	sw	a2,-1287(s7)
    33ca:	c3b1                	beqz	a5,340e <_read+0x180>
    33cc:	c632                	sw	a2,12(sp)
    33ce:	b6f4                	sb	a3,15(a3)
    33d0:	000000af          	0xaf
    33d4:	fdb9                	bnez	a1,3332 <_read+0xa4>
    33d6:	cbc2                	sw	a6,212(sp)
    33d8:	eacd                	bnez	a3,348a <__clz_tab+0x52>
    33da:	aca3c9b3          	0xaca3c9b3
    33de:	ddc5e5b3          	0xddc5e5b3
    33e2:	f7c1                	bnez	a5,336a <_read+0xdc>
    33e4:	e1bdccb3          	0xe1bdccb3
    33e8:	f8ca                	fsw	fs2,112(sp)
    33ea:	0000                	unimp
    33ec:	0000                	unimp
    33ee:	c2c8                	sw	a0,4(a3)
    33f0:	0000                	unimp
    33f2:	c47a                	sw	t5,8(sp)
    33f4:	0000                	unimp
    33f6:	3d80                	lbu	s0,25(a1)
    33f8:	e9e6                	fsw	fs9,208(sp)
    33fa:	ffff                	0xffff
    33fc:	e938                	fsw	fa4,80(a0)
    33fe:	ffff                	0xffff
    3400:	e938                	fsw	fa4,80(a0)
    3402:	ffff                	0xffff
    3404:	e936                	fsw	fa3,144(sp)
    3406:	ffff                	0xffff
    3408:	e93c                	fsw	fa5,80(a0)
    340a:	ffff                	0xffff
    340c:	e93c                	fsw	fa5,80(a0)
    340e:	ffff                	0xffff
    3410:	e90c                	fsw	fa1,16(a0)
    3412:	ffff                	0xffff
    3414:	e936                	fsw	fa3,144(sp)
    3416:	ffff                	0xffff
    3418:	e93c                	fsw	fa5,80(a0)
    341a:	ffff                	0xffff
    341c:	e90c                	fsw	fa1,16(a0)
    341e:	ffff                	0xffff
    3420:	e93c                	fsw	fa5,80(a0)
    3422:	ffff                	0xffff
    3424:	e936                	fsw	fa3,144(sp)
    3426:	ffff                	0xffff
    3428:	e9d4                	fsw	fa3,20(a1)
    342a:	ffff                	0xffff
    342c:	e9d4                	fsw	fa3,20(a1)
    342e:	ffff                	0xffff
    3430:	e9d4                	fsw	fa3,20(a1)
    3432:	ffff                	0xffff
    3434:	e90c                	fsw	fa1,16(a0)
    3436:	ffff                	0xffff

00003438 <__clz_tab>:
    3438:	0100 0202 0303 0303 0404 0404 0404 0404     ................
    3448:	0505 0505 0505 0505 0505 0505 0505 0505     ................
    3458:	0606 0606 0606 0606 0606 0606 0606 0606     ................
    3468:	0606 0606 0606 0606 0606 0606 0606 0606     ................
    3478:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    3488:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    3498:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    34a8:	0707 0707 0707 0707 0707 0707 0707 0707     ................
    34b8:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34c8:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34d8:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34e8:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    34f8:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3508:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3518:	0808 0808 0808 0808 0808 0808 0808 0808     ................
    3528:	0808 0808 0808 0808 0808 0808 0808 0808     ................

00003538 <__sf_fake_stderr>:
	...

00003558 <__sf_fake_stdin>:
	...

00003578 <__sf_fake_stdout>:
	...
    3598:	2d23 2b30 0020 0000 6c68 004c 6665 4567     #-0+ ...hlL.efgE
    35a8:	4746 0000 3130 3332 3534 3736 3938 4241     FG..0123456789AB
    35b8:	4443 4645 0000 0000 3130 3332 3534 3736     CDEF....01234567
    35c8:	3938 6261 6463 6665 0000 0000               89abcdef....
