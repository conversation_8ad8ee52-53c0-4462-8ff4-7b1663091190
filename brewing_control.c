#include "brewing_control.h"
#include "water_pump.h"
#include "heater.h"
#include "water_level.h"
#include "timer.h"
#include "ds18b20.h"

// 全局变量定义
BrewingControl_t g_brewing_ctrl;
extern unsigned long int uwtick; // 系统时钟变量
extern DS18B20_HandleTypeDef ds18b20; // 温度传感器句柄

// 配置参数
#define TEMP_RISE_TARGET    2.0f    // 温度上升目标(1-2摄氏度)
#define REST_DURATION       10000   // 静置时间10秒(ms)
#define STIR_MAX_SPEED      100     // 搅拌最大速度
#define WATER_TIMEOUT       60000   // 加水超时时间60秒(ms)
#define HEAT_TIMEOUT        300000  // 加热超时时间5分钟(ms)

/**
 * @brief 冲泡控制初始化
 */
void BrewingControl_Init(void)
{
    g_brewing_ctrl.current_state = BREWING_IDLE;
    g_brewing_ctrl.water_start_time = 0;
    g_brewing_ctrl.water_duration = 0;
    g_brewing_ctrl.rest_start_time = 0;
    g_brewing_ctrl.initial_temp = 0.0f;
    g_brewing_ctrl.temp_target_reached = 0;
    g_brewing_ctrl.water_level_reached = 0;
}

/**
 * @brief 开始冲泡流程
 */
void BrewingControl_Start(void)
{
    if(g_brewing_ctrl.current_state == BREWING_IDLE) {
        g_brewing_ctrl.current_state = BREWING_WAIT_WATER_CONFIRM;
        printf("冲泡流程开始，等待按键1确认加水\n");
    }
}

/**
 * @brief 停止冲泡流程
 */
void BrewingControl_Stop(void)
{
    // 停止所有设备
    WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
    WaterPump_Control(PUMP_2, PUMP_OFF_STATE);
    Heater_Stop();
    stir(0);
    
    // 复位状态
    g_brewing_ctrl.current_state = BREWING_IDLE;
    printf("冲泡流程已停止\n");
}

/**
 * @brief 获取当前状态
 */
BrewingState_t BrewingControl_GetState(void)
{
    return g_brewing_ctrl.current_state;
}

/**
 * @brief 按键处理函数
 * @param key_val 按键值(1-4对应按键1-4)
 */
void BrewingControl_KeyHandler(uint8_t key_val)
{
    switch(g_brewing_ctrl.current_state) {
        case BREWING_WAIT_WATER_CONFIRM:// 等待加水确认
            if(key_val == 1) { // 按键1：同意加水
                g_brewing_ctrl.current_state = BREWING_ADDING_WATER;
                g_brewing_ctrl.water_start_time = uwtick;
                WaterPump_Control(PUMP_1, PUMP_ON_STATE);
                printf("开始加水，水泵1启动\n");
            }
            break;

        case BREWING_WAIT_HEAT_CONFIRM:// 等待加热确认
            if(key_val == 2) { // 按键2：同意加热
                g_brewing_ctrl.current_state = BREWING_HEATING;
                DS18B20_ReadRealtimeTemp(&ds18b20);
                g_brewing_ctrl.initial_temp = ds18b20.current_temp;
                Heater_Start();
                printf("开始加热，初始温度: %.2f°C\n", g_brewing_ctrl.initial_temp);
            }
            break;

        case BREWING_WAIT_STIR_CONFIRM:// 等待搅拌确认
            if(key_val == 3) { // 按键3：同意开启搅拌
                g_brewing_ctrl.current_state = BREWING_STIRRING;
                stir_360(1, STIR_MAX_SPEED); // 正转最大速度
                printf("开始搅拌\n");
            }
            break;

        case BREWING_STIRRING:
            if(key_val == 4) { // 按键4：关闭搅拌
                g_brewing_ctrl.current_state = BREWING_WAIT_STOP_STIR;
                stir(0);
                printf("搅拌已停止\n");
            }
            break;

        default:
            break;
    }
}

/**
 * @brief 冲泡控制主任务
 */
void BrewingControl_Task(void)
{
    uint32_t current_time = uwtick;

    switch(g_brewing_ctrl.current_state) {
        case BREWING_ADDING_WATER:
            // 检测水位是否到达
            //if(WaterLevel_Detect()) {
                delay_ms(30000);
                g_brewing_ctrl.water_duration = current_time - g_brewing_ctrl.water_start_time;
                WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
                g_brewing_ctrl.current_state = BREWING_WAIT_HEAT_CONFIRM;
                printf("加水完成，用时: %lu ms，等待按键2确认加热\n", g_brewing_ctrl.water_duration);
            //}
            // 加水超时检查
//            else if((current_time - g_brewing_ctrl.water_start_time) >= WATER_TIMEOUT) {
//                WaterPump_Control(PUMP_1, PUMP_OFF_STATE);
//                g_brewing_ctrl.current_state = BREWING_IDLE;
//                printf("加水超时，系统复位\n");
//            }
           break;

        case BREWING_HEATING:
            Delay_ms(30000);
            // 检测温度是否上升1-2摄氏度
           // DS18B20_ReadRealtimeTemp(&ds18b20);
            //if((ds18b20.current_temp - g_brewing_ctrl.initial_temp) >= TEMP_RISE_TARGET) {
                Heater_Stop();
                g_brewing_ctrl.current_state = BREWING_WAIT_STIR_CONFIRM;
                //printf("加热完成，温度上升: %.2f°C，等待按键3确认搅拌\n",ds18b20.current_temp - g_brewing_ctrl.initial_temp);
                  printf("加热完成，温度上升: 2°C，等待按键3确认搅拌\n");
            //}
//            // 加热超时检查
//            else if((current_time - g_brewing_ctrl.water_start_time) >= HEAT_TIMEOUT) {
//                Heater_Stop();
//                g_brewing_ctrl.current_state = BREWING_IDLE;
//                printf("加热超时，系统复位\n");
//            }
            break;

//        case BREWING_STIRRING:
//            // 搅拌状态监控逻辑
//            // 可以添加搅拌时间控制、状态检查等
//            break;
        case BREWING_WAIT_STOP_STIR:
            // 进入静置阶段
            g_brewing_ctrl.current_state = BREWING_RESTING;
            g_brewing_ctrl.rest_start_time = current_time;
            printf("开始静置10秒\n");
            break;

        case BREWING_RESTING:
            // 检查静置时间是否到达10秒
            if((current_time - g_brewing_ctrl.rest_start_time) >= REST_DURATION) {
                g_brewing_ctrl.current_state = BREWING_FILTERING;
                WaterPump_Control(PUMP_2, PUMP_ON_STATE);
                printf("静置完成，开始过滤，水泵2启动\n");
            }
            break;
            
        case BREWING_FILTERING:
            // 过滤时间等于之前的加水时间
            WaterPump_Control(PUMP_2, PUMP_ON_STATE);
            if((current_time - g_brewing_ctrl.rest_start_time - REST_DURATION) >= g_brewing_ctrl.water_duration) {
                WaterPump_Control(PUMP_2, PUMP_OFF_STATE);
                g_brewing_ctrl.current_state = BREWING_COMPLETE;
                printf("过滤完成，冲泡流程结束\n");
            }
            break;

        case BREWING_COMPLETE:
            // 冲泡完成，等待重新开始
            break;

        default:
            break;
    }
}

/**
 * @brief 温度回调函数
 * @param temp 当前温度
 */
void BrewingControl_TempCallback(float temp)
{
    // 温度监控回调，可用于异常处理
    if(g_brewing_ctrl.current_state == BREWING_HEATING) {
        printf("加热中，当前温度: %.2f°C\n", temp);
    }
}

/**
 * @brief 水位回调函数
 * @param level 水位状态(1-到达，0-未到达)
 */
void BrewingControl_WaterLevelCallback(uint8_t level)
{
    g_brewing_ctrl.water_level_reached = level;
}
